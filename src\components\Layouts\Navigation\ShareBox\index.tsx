import styles from "./sharebox.module.scss";
import ContactInfos from "@/constants/contents/contactInfo";
import Link from "next/link";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import FacebookIcon from '@mui/icons-material/Facebook';
import InstagramIcon from '@mui/icons-material/Instagram';
import TwitterIcon from '@mui/icons-material/Twitter';

const ShareBox = () => {
    const { t: footerTranslation } = useTranslation(EnumTranslationJson.Footer);
    return (
        <div className={styles.container}>
            <ul className={styles.contactInfo}>
                {ContactInfos?.email && <li><Link href={`mailto:${ContactInfos.email.url}`}><MailOutlineIcon />{ContactInfos.email.name}</Link> ({footerTranslation("businessJob")})</li>}
                {ContactInfos?.whatsapp && <li><Link href={ContactInfos.whatsapp.url} target="_blank"><WhatsAppIcon />{ContactInfos.whatsapp.name}</Link> ({footerTranslation("textOnly")})</li>}
                {ContactInfos?.facebook && <li><Link href={ContactInfos.facebook.url} target="_blank"><FacebookIcon />{ContactInfos.facebook.name}</Link></li>}
                {ContactInfos?.twitter && <li><Link href={ContactInfos.twitter.url} target="_blank"><TwitterIcon />{ContactInfos.twitter.name}</Link></li>}
                {ContactInfos?.instagram && <li><Link href={ContactInfos.instagram.url} target="_blank"><InstagramIcon />{ContactInfos.instagram.name}</Link></li>}
            </ul>
        </div>
    );
};

export default ShareBox;
