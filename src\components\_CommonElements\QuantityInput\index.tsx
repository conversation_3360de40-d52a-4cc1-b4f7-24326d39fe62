import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import { useCallback, useEffect, useState } from "react";
import styles from "./quantityInput.module.scss";

interface Props {
    defaultValue?: number;
    minValue?: number;
    maxValue?: number;
    onChange: (quantity: number) => void;
}
const QuantityInput = (props: Props) => {
    const { t: validationTranslation } = useTranslation(EnumTranslationJson.Validation);
    const { defaultValue, minValue, maxValue, onChange } = props;
    const [quantity, setQuantity] = useState<number>(defaultValue || 0);
    const [error, setError] = useState<string>("");
    const decrement = useCallback(() => {
        if (minValue !== undefined && quantity === minValue) {
            setError(validationTranslation("quantity.minimumWarning"))
            return;
        }
        setQuantity(quantity - 1);
        setError("");
    }, [quantity, minValue, validationTranslation]);

    const increment = useCallback(() => {
        if (maxValue !== undefined && quantity === maxValue) {
            setError(validationTranslation("quantity.maximumWarning"))
            return;
        }
        setQuantity(quantity + 1);
        setError("");
    }, [quantity, maxValue, validationTranslation]);

    useEffect(() => {
        onChange(quantity);
    }, [quantity, onChange]);

    useEffect(() => {
        setQuantity(defaultValue || 0);
        setError("");
    }, [defaultValue]);

    return (
        <div className={styles.container}>
            <span className={styles.decrement} onClick={decrement}>-</span>
            <span className={styles.amount}>{quantity}</span>
            <span className={styles.increment} onClick={increment}>+</span>
        </div>
    );
}
export default QuantityInput;