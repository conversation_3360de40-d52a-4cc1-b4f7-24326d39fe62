import ENDPOINT from "@/models/api/endpoint";
import { useMemo } from "react";
import API from "@/utils/API";
import { BACKEND } from "@/constants/config";
import RequestForgotPasswordAPIResult from "@/models/api/result/user/requestForgotPassword";
import ConfirmForgotPasswordAPIResult from "@/models/api/result/user/confirmForgotPassword";

export interface INewUserData {
    // name: string;
    nickname: string;
    email: string;
    password: string;
    confirmPassword: string;
    promotion: boolean;
    recaptcha:string;
}
export interface ILoginUserData {
    email: string;
    password: string;
}

class AuthenicationHooks {
    static useUserInfo = () => {
        const jwt = API.GetUserJWT();
        const isAuthorized = useMemo(() => {
            return jwt !== undefined;
        }, [jwt]);
        return { jwt, isAuthorized };    
    }
}
export default AuthenicationHooks;

const useRequestForgotPassword = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit();
    const requestAsync = async (email: string) => {
        const formData = new FormData();
        formData.append("email", email);

        return await fetchAsync<RequestForgotPasswordAPIResult>(ENDPOINT.forgotPasswordCode(), {
            method: "POST",
            data: formData,
        });
    };

    return requestAsync;
};

// -----------------------------------------------

export interface IForgotPasswordData {
    email: string;
    verificationCode: string;
    newPassword: string;
    confirmNewPassword: string;
}

const useConfirmForgotPassword = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit();
    const requestAsync = async (forgotPasswordFields: IForgotPasswordData) => {
        const { email, verificationCode: validateCode, newPassword, confirmNewPassword } = forgotPasswordFields;
        const formData = new FormData();
        formData.append("Email", email);
        formData.append("RecoveryCode", validateCode);
        formData.append("NewPassword", newPassword);
        formData.append("ConfirmNewPassword", confirmNewPassword);

        return await fetchAsync<ConfirmForgotPasswordAPIResult>(
            ENDPOINT.forgotPassword(),
            {
                method: "POST",
                data: formData,
            }
        );
    };

    return requestAsync;
};


// -----------------------------------------------

export {
    useRequestForgotPassword,
    useConfirmForgotPassword,
};
