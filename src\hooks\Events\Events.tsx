import { BACKEND } from "@/constants/config";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import ENDPOINT from "@/models/api/endpoint";
import EventsAPIResult from "@/models/api/result/events/events";

const useEvents = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION, EnumRequestHeader.LANGUAGE] });

    const getEventsAsync = async (eventCategory: string, isTrending: boolean, locale: string) => {
        const _timeing = isTrending ? "trending" : "past";
        const _endpoint = isTrending ? ENDPOINT.TrendingEventByPage(1, eventCategory) : ENDPOINT.PastEventByPage(1, eventCategory);
        return await BACKEND.Gateway.fetchQuery<EventsAPIResult>(
            {
                url: _endpoint,
                params: {
                    queryKey: `event_${_timeing}_${eventCategory}_${locale}`
                },
                headers: {
                    [EnumRequestHeader.LANGUAGE]: locale || "en-US"
                }
            }
        );
    };

    return { getEventsAsync };
};

export { useEvents };
