import ReservableSite from "./ReservableSite";

interface ReservableSession {
    membershipReservableSessionId: string;
    siteId: string;
    reservableSessionType: string;
    sessionStartDate: string;
    sessionStartTime: string;
    sessionEndDate: string;
    sessionEndTime: string;
    site: ReservableSite;
    limit: number;
    reservationCount: number;
    isReservable: boolean;
    isLimit: boolean;
}

export default ReservableSession;