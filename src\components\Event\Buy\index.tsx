import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import classNames from "classnames";
import dayjs, { Dayjs } from "dayjs";
import { useSnackbar } from "notistack";
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';

import { BACKEND } from "@/constants/config";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import EventDetails from "@/models/api/models/EventDetails";
import EventSessionBundlesByDate from "@/models/api/models/EventSessionBundlesByDate";
import { EventSessionBundleAPIResult } from "@/models/api/result/events/session";
import ENDPOINT from "@/models/api/endpoint";
import AuthenicationHooks from "@/hooks/Authentication";
import { dispatch, useSelector } from "@/redux/store";
import { ModalActions } from "@/redux/slices/uiSlice";
import { addItem, increaseItemQuantity, openClearShoppingCartConfirmDialog, setEventId, setEventName } from "@/redux/slices/cartSlice";

import Section from "@/components/_CommonElements/Section";
import Label from "@/components/_CommonElements/Label";
import QuantityInput from "@/components/_CommonElements/QuantityInput";
import { TextButton } from "@/components/_CommonElements/Button";
import LoadingButton from "@/components/_CommonElements/Button/LoadingButton";
import SelectBox from "@/components/_CommonElements/SelectBox";

import styles from "@/components/Event/InfoDisplay/event.module.scss";
import eventBuyStyles from "@/components/Event/Buy/eventBuy.module.scss";
import { ShoppingCartSummarizedItem, UpdateShoppingCartItemAPIResult } from "@/models/api/result/user/shoppingCart";
import { ShoppingCartItem } from "@/models/api/models/ShoppingCart";
import API from "@/utils/API";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import i18n from "@/utils/i18n";

const DateCalendar = dynamic(async () => (await import("@mui/x-date-pickers")).DateCalendar, { ssr: false });
interface EventBuyContainerProps {
    eventDetails: EventDetails;
}
const useShoppingCart = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const updateAsync = async ({eventSessionBundleId, quantity} : {eventSessionBundleId: string, quantity: number}) => {
        const formData = API.ToFormData({eventSessionBundleId, quantity});
        return await requestAsync<UpdateShoppingCartItemAPIResult>(
            ENDPOINT.UpdateShoppingCartItem(),
            {
                method: "POST",
                data: formData
            }
        );
    };
    return { updateAsync };
}
const EventBuyContainer = (props: EventBuyContainerProps) => {
    const { eventDetails } = props;
    const { isAuthorized } = AuthenicationHooks.useUserInfo();
    const userVerified = useSelector((state) => state.user.userVerified);
    const cartItems = useSelector((state) => state.cart.items);
    const cartEventId = useSelector((state) => state.cart.eventId);
    const { updateAsync } = useShoppingCart();
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { enqueueSnackbar } = useSnackbar();
    const router = useRouter();
    const [selectedDate, setSelectedDate] = useState<Dayjs | null>(null);
    const [selectedDateAvailableEventSessionBundlesOption, setSelectedDateAvailableEventSessionBundlesOption] = useState<EventSessionBundlesByDate | undefined>();
    const [selectedEventSessionBundleId, setSelectedEventSessionBundleId] = useState<string>();
    const [selectedEventSessionBundle, setSelectedEventSessionBundle] = useState<ShoppingCartItem>();
    const [quantity, setQuantity] = useState<number>(0);
    const [ isLoadingSessionList, setIsLoadingSessionList ] = useState<boolean>(false);
    
    const availableDates = useMemo(() => (eventDetails.availableSessionDateList.map((dateString) => new Date(dateString)).map((date) => dayjs(date.getTime()))), [eventDetails.availableSessionDateList]);

    const selectedDateAvailableEventSessionBundlesOptionDataSource = useMemo(() => {
        if (!selectedDateAvailableEventSessionBundlesOption) {
            return undefined;
        }
        const dataSource = [];

        for (const session of Object.keys(selectedDateAvailableEventSessionBundlesOption.sessionGroup)) {
            const sessionGroup = selectedDateAvailableEventSessionBundlesOption.sessionGroup[session];
            const sessionStartDateTime_LocalTime = dayjs.utc(sessionGroup.sessionStartDateTime).tz(eventDetails.eventTimeZone);
            const sessionEndDateTime_LocalTime = dayjs.utc(sessionGroup.sessionEndDateTime).tz(eventDetails.eventTimeZone);
            const optionDisplayHeader = `${sessionStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss")} ～ ${sessionEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss")}`;
            dataSource.push({
                id: `opt-${session}`,
                value: '',
                primaryValue: optionDisplayHeader,
                disabled: false,
                isHeader: true
            });

            sessionGroup.items?.forEach(bundle => {
                dataSource.push({
                    id: bundle.eventSessionBundleId,
                    value: bundle.eventSessionBundleId,
                    primaryValue: bundle.eventBundleName,
                    secondaryValue: i18n.GetCurrency(bundle.currency, bundle.price, router.locale),
                    disabled: bundle.soldOut,
                    outOfStock: bundle.soldOut,
                    isHeader: false
                });
            });
        }

        return dataSource;
    }, [router.locale, eventDetails.eventTimeZone, selectedDateAvailableEventSessionBundlesOption]);

    const selectedDateAvailableEventSessionBundleFlattenedDataSource = useMemo(() => {
        if (!selectedDateAvailableEventSessionBundlesOption) {
            return undefined;
        }

        const dataSource: ShoppingCartSummarizedItem[] = [];

        for (const session of Object.keys(selectedDateAvailableEventSessionBundlesOption.sessionGroup)) {
            const sessionGroup = selectedDateAvailableEventSessionBundlesOption.sessionGroup[session];

            sessionGroup.items?.forEach(bundle => {
                dataSource.push(bundle); 
            });
        }

        return dataSource;
    }, [selectedDateAvailableEventSessionBundlesOption]);

    const requestFetchSessionBundleList = useCallback(async (showLoading = true) => {
        if (!selectedDate) {
            return null;
        }
        if (showLoading) {
            setIsLoadingSessionList(true);
        } else {
            setIsLoadingSessionList(false);
        }
        try {
            const date = selectedDate.format("YYYY-MM-DD");
            const res = (await BACKEND.Gateway.fetchQuery<EventSessionBundleAPIResult>({
                url: ENDPOINT.SessionBundlesByEventIdDate(eventDetails.eventId, date),
                params: {
                  queryKey: `session-bundles-${eventDetails.eventId}-${date}`
                },
                refetchInterval: false,
                cacheTime: 0,
                staleTime: 0,
            })).data!;
            setSelectedDateAvailableEventSessionBundlesOption(res);
        } catch {
            enqueueSnackbar(snackbarTranslation("messages.fetchEventSession.error"));
        } finally {
            setIsLoadingSessionList(false);
        }
    }, [selectedDate, eventDetails.eventId, enqueueSnackbar, snackbarTranslation]);

    const onSelectedDateChanged = useCallback((date: unknown) => {
        if (isLoadingSessionList) return;
        setSelectedDate((prevDate) => {
            if (prevDate != date) setIsLoadingSessionList(true);
            return date as Dayjs | null;
        });
        setSelectedDateAvailableEventSessionBundlesOption(undefined);
        setQuantity(0);
    }, [isLoadingSessionList]);

    useEffect(() => {
        void requestFetchSessionBundleList(isLoadingSessionList);
    }, [router.locale, requestFetchSessionBundleList]);


    const onSelectedEventSessionBundleChanged = useCallback((id: string | undefined) => {
        if (id === "disabled") {
            return;
        }

        const selectedBundle = selectedDateAvailableEventSessionBundleFlattenedDataSource?.find(bundle => bundle.eventSessionBundleId === id);
        setSelectedEventSessionBundleId(id);
        setSelectedEventSessionBundle(selectedBundle);
    }, [selectedDateAvailableEventSessionBundleFlattenedDataSource]);

    const onQuantityChange = useCallback((newQuantity: number) => {
        setQuantity(newQuantity)
    }, []);

    const isDateUnavailable = useCallback((date: unknown): boolean => {
        const formattedDate = dayjs(date as Dayjs).format("YYYY-MM-DD");
        // Check if the formattedDate is included in availableDates
        return !availableDates.some((availableDate) =>
            availableDate.isSame(formattedDate, "day")
        );
    }, [availableDates]);

    const addToCart = useCallback(() => {
        void (async () => {
            if (!isAuthorized) {
                dispatch(ModalActions.openLoginModal());
                return;
            }
            if (!userVerified) {
                dispatch(ModalActions.openAccountVerificationModal());
                return;
            }
            if (!selectedEventSessionBundle || quantity <= 0) {
                return;
            }
            const isSelectedItemFromOtherEvent = cartEventId !== selectedEventSessionBundle.eventId;
            if (cartEventId && isSelectedItemFromOtherEvent) {
                dispatch(openClearShoppingCartConfirmDialog({ titleI18nKey: "modals.clearShoppingCart_systemrequest.title", contentI18nKey: "modals.clearShoppingCart_systemrequest.content"}));
                return;
            }
            dispatch(setEventId(eventDetails.eventId));
            dispatch(setEventName(eventDetails.eventName));
            try {
                const targetItem = cartItems.find(item => item.eventSessionBundleId === selectedEventSessionBundle.eventSessionBundleId);
                await updateAsync({
                    eventSessionBundleId: selectedEventSessionBundle.eventSessionBundleId,
                    quantity: quantity
                });
                if (targetItem) {
                    dispatch(increaseItemQuantity({eventSessionBundleId: targetItem.eventSessionBundleId, quantity}));
                }
                else {
                    dispatch(addItem({
                        ...selectedEventSessionBundle,
                        quantity
                    }));
                }
                enqueueSnackbar(snackbarTranslation("messages.cart.add.success"), { variant: "success" });
            }
            catch (error: unknown) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
            }
        })();
    }, [isAuthorized, userVerified, selectedEventSessionBundle, quantity, cartEventId, eventDetails.eventId, eventDetails.eventName, cartItems, updateAsync, enqueueSnackbar, snackbarTranslation]);

    const currentDateTime = dayjs();
    const defaultCalendarMonth = useMemo(() => {
        return currentDateTime.isAfter(availableDates[0]) ? currentDateTime : availableDates[0];
    }, [currentDateTime, availableDates]);
    
    useEffect(() => {

    }, [router.locale]);
    return (
        <>
            <div className={styles.purcahseSectionContainer}>
                <Section
                    labelSize="small"
                    label={`${eventTranslation("step.label", { NUMBER: 1 })} - ${eventTranslation("step.one")}`}
                    className={{
                        root: styles.purchaseSection
                    }}
                    content={
                        <DateCalendar
                            classes={{ root: styles.calendar }}
                            disablePast
                            defaultCalendarMonth={defaultCalendarMonth}
                            value={selectedDate}
                            onChange={onSelectedDateChanged}
                            shouldDisableDate={isDateUnavailable}
                            maxDate={availableDates[availableDates.length - 1]}
                        />
                    }
                />
            </div>
            <div className={styles.purcahseSectionContainer}>
                <Section
                    labelSize="small"
                    label={`${eventTranslation("step.label", { NUMBER: 2 })} - ${eventTranslation("step.two")}`}
                    className={{
                        root: classNames(styles.purchaseSection, { [styles.disabled]: selectedDate === null })
                    }}
                    content={
                        <div className={styles.purchaseBox}>
                            <div className={styles.row}>
                                <Label title={eventTranslation("detail.session")} />
                                { isLoadingSessionList ? <LoadingButton />: 
                                    <SelectBox
                                        classes={{
                                            box: styles.selectBox,
                                            header: eventBuyStyles.selectOptionHeader,
                                            headerLabel: eventBuyStyles.label,
                                            option: styles.selectOption,
                                            optionValue: eventBuyStyles.optionValue
                                        }}
                                        disabled={isLoadingSessionList || selectedDate === null}
                                        dataSource={selectedDateAvailableEventSessionBundlesOptionDataSource}
                                        onChange={onSelectedEventSessionBundleChanged}
                                    />
                                }
                            </div>
                            <div className={styles.row}>
                                <Label title={eventTranslation("detail.qty")} />
                                <QuantityInput
                                    defaultValue={quantity}
                                    minValue={0}
                                    maxValue={50}
                                    onChange={onQuantityChange}
                                />
                            </div>
                            <div className={styles.buyBtn}>
                                <TextButton
                                    disabled={!selectedEventSessionBundleId || quantity == 0}
                                    startIcon={<AddShoppingCartIcon />}
                                    size="extra"
                                    onClick={addToCart}
                                    fullWidth
                                >
                                    &nbsp; {eventTranslation("detail.addToCart")}
                                </TextButton>
                            </div>
                        </div>
                    }
                />
                {selectedDate === null && <div className={styles.disabledArea} />}
            </div>
        </>
    )
};

export default EventBuyContainer;