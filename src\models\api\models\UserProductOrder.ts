import {UserOrder, UserOrderItem} from "./UserOrder";

interface UserProductOrderItem extends UserOrderItem {
    itemType: string;
    itemName: string;
    pricePerUnit: number;
    unitTotalFee: number;
    itemCurrency: string;
    quantity: number;
}

interface UserProductOrder extends Omit<UserOrder, "itemList"> {
    paymentOrderId: string;
    userId: string;
    currency: string;
    orderTotalFee: number;
    createdDateTime: number;
    itemList: UserProductOrderItem[];
}
export type { UserProductOrder, UserProductOrderItem };