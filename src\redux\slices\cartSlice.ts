import { createSelector, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { ShoppingCartSummarizedItem } from "@/models/api/result/user/shoppingCart";
import { DiscountCodeInfoForDisplay } from "@/models/api/models/DiscountApplyStatusAndEventBundlePriceInfo";

interface ClearShoppingCartConfirmDialogOptions {
    open: boolean;
    titleI18nKey: string;
    contentI18nKey: string;
}

export interface CartSliceState {
    eventId?: string;
    eventName?: string;
    discountInfo?: DiscountCodeInfoForDisplay;
    totalPrice: number;
    items: ShoppingCartSummarizedItem[];
    clearShoppingCartConfirmDialog: ClearShoppingCartConfirmDialogOptions;
}

const initialState: CartSliceState = {
    items: [],
    eventId: undefined,
    eventName: undefined,
    discountInfo: undefined,
    totalPrice: 0,
    clearShoppingCartConfirmDialog: {
        open: false,
        titleI18nKey: '',
        contentI18nKey: '',
    }
};

export const cartSlice = createSlice({
    name: "cart",
    initialState,
    reducers: {
        setEventId: (state, action: PayloadAction<string>) => {
            state.eventId = action.payload;
        },
        setEventName: (state, action: PayloadAction<string>) => {
            state.eventName = action.payload;
        },
        setItems: (state, action: PayloadAction<ShoppingCartSummarizedItem[]>) => {
            state.items = action.payload;
            if (action.payload.length > 0) {
                state.totalPrice = action.payload.reduce((total: number, item: ShoppingCartSummarizedItem) => {
                    total += item.price * item.quantity;
                    return total;
                }, 0.00);
            }
        },
        addItem: (state, action: PayloadAction<ShoppingCartSummarizedItem>) => {
            state.items.push(action.payload);
        },
        removeItem: (state, action: PayloadAction<string>) => {
            // Keep the mismatched items.
            state.items = state.items.filter(item => item.eventSessionBundleId !== action.payload);
        },
        increaseItemQuantity: (state, action: PayloadAction<{eventSessionBundleId: string, quantity: number}>) => {
            const targetIndex = state.items.findIndex(i => i.eventSessionBundleId === action.payload.eventSessionBundleId);
            if (targetIndex !== -1) {
                state.items[targetIndex].quantity += action.payload.quantity;
                state.items = state.items.slice();
            }
        },
        decreaseItemQuantity: (state, action: PayloadAction<{eventSessionBundleId: string, quantity: number}>) => {
            const targetIndex = state.items.findIndex(i => i.eventSessionBundleId === action.payload.eventSessionBundleId);
            if (targetIndex !== -1) {
                state.items[targetIndex].quantity -= action.payload.quantity;
                state.items = state.items.slice();
            }
            if (state.items.length <= 0) clearCart();
        },
        clearCart: (state) => {
            state.eventId = undefined;
            state.eventName = undefined;
            state.discountInfo = undefined;
            state.items = [];
        },
        setDiscountInfo: (state, action: PayloadAction<DiscountCodeInfoForDisplay | undefined>) => {
            state.discountInfo = action.payload;
        },
        setTotalPrice: (state, action: PayloadAction<number>) => {
            state.totalPrice = action.payload;
        },
        openClearShoppingCartConfirmDialog: (state, action: PayloadAction<{titleI18nKey: string, contentI18nKey: string}>) => {
            const newState = Object.assign({}, initialState.clearShoppingCartConfirmDialog);
            newState.open = true;
            newState.titleI18nKey = action.payload.titleI18nKey;
            newState.contentI18nKey = action.payload.contentI18nKey;
            state.clearShoppingCartConfirmDialog = newState;
        },
        closeClearShoppingCartConfirmDialog: (state) => {
            state.clearShoppingCartConfirmDialog.open = false;
            state.clearShoppingCartConfirmDialog = Object.assign({}, state.clearShoppingCartConfirmDialog);
        }
    }
});

export const {
    setEventId,
    setEventName,
    setItems,
    addItem,
    removeItem,
    increaseItemQuantity,
    decreaseItemQuantity,
    clearCart,
    setDiscountInfo,
    setTotalPrice,
    openClearShoppingCartConfirmDialog,
    closeClearShoppingCartConfirmDialog
} = cartSlice.actions;

const selectCartItems = (state: RootState) => state.cart.items;

export const selectCheckoutItemsByIdSelector = createSelector([selectCartItems], items => items.reduce<string[]>((checkoutList, item: ShoppingCartSummarizedItem) => {
    new Array(item.quantity).fill(null).forEach(() => {
        checkoutList.push(item.eventSessionBundleId);
    });
    return checkoutList;
}, []));

export default cartSlice.reducer;