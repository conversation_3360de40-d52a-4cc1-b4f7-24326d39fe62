@use "@/styles/utils/viewport.module.scss" as viewport;

.container {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .logo {
        width: 100%;
        max-width: 40vw;
        @include viewport.within("tablet") {
            max-width: 20vw;
        }
        @include viewport.within("mobile") {
            max-width: 40vw;
        }
    }

    .contactInfo {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 20px 0;

        .iconbox {
            padding: 15px
        }
        .iconStyle {
            font-size: 25px;
            width: 25px;
        }
    }
}
