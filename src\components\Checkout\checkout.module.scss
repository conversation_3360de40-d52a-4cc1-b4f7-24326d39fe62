@use "@/styles/utils/viewport.module.scss" as viewport;
.container {
    display: flex;
    flex-direction: column;
    padding: 1rem 0;
    gap: 20px !important;

    .group {
        display: flex;
        flex-direction: column;
        padding: 1.25rem;
        border-radius: 5px;
        box-shadow: 1px 2px 5px 1px #F0F0F0;
        gap: 15px;
        width: 100%;

        & .applyDiscountCodeButton {
            width: auto;
            text-wrap: nowrap;
        }
        h2 {

        }
        & .highlight {
            color: red;
        }

        .row {
            display: flex;
            align-items: center;
            &.select {
                // display: grid;
                // grid-template-columns: 50% 50%;
            }

            .col {
                display: initial;
                padding: 0.25rem 0 0 0.75rem;
                flex-grow: 1;
                width: 50%;

                .radio {
                    padding: 0.25rem 0;
                    border: 1px solid #B2BCCA;
                    display: flex;
                    border-radius: 5px;
                    gap: 0;
                    &.active {
                        background-color: #E8EFFA;
                        border-color: #1660CF;
                    }
                    &.disabled {
                        pointer-events: none;
                        opacity: 0.5;
                    }

                    &+.radio {
                        margin-top: 1rem;

                    }
                }

                .labelContainer {
                    display: flex;
                    padding: 9px;
                    width: 100%;

                    &.shipment {
                        .price {
                            padding-right: 2rem;
                            max-width: 70px;
                            width: 100%;
                        }

                        .content {
                            font-size: 13px;
                        }

                        .logo {
                            margin-left: auto;
                            padding-right: 1rem;
                            color: rgb(79, 79, 79);

                            svg {
                                font-size: 2.5rem;
                            }
                        }
                    }

                    &.payment {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        gap: 10px;
                        @include viewport.within("tablet") {
                            flex-direction: column;
                        }
                        .content {
                            font-weight: bolder;
                            .note {
                                font-weight: normal;
                                font-size: 13px;
                            }
                        }

                        .logo {
                            display: flex;
                            gap: 5px;
                            svg {
                                max-width: 50px;
                                max-height: 35px;
                            }


                        }
                    }
                }
            }
        }

        .overView {
            padding: 0;

            h2 {
                margin: 0;
            }

            div[class~="MuiAccordionSummary-content"] {
                margin: 0;
            }
        }

        .details {
            padding: 0;
            &.disabled {
                pointer-events: none;
                opacity: 0.5;
            }
            .itemWrapper {  
                display: flex;
                flex-direction: column;
                gap: 5px;
                hr{
                    margin: 0.5rem 0 ;
                }
                .item {
                    display: flex;
                    gap: 15px;
                    .image {
                        img {
                            border-radius: 10px;
                        }
                    }
                    .content {
                        width: 100%;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        
                        .lower {
                            display: flex;
                            justify-content: space-between;
                            .qtyBox {
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                gap: 10px;
                                & .boxContainer {
                                    display: flex;
                                    flex-direction: row;
                                    gap: 0;
                                    & .box {
                                        width: 30px;
                                        height: 30px;
                                        border: 1px solid gray;
                                        text-align: center;
                                        font-size: 20px;
                                        display: flex;
                                        justify-content: center;
                                        border-radius: 5px;
                                        cursor: pointer;
                                        user-select: none;
                                        &.noBorder{
                                            border: none;
                                            height: 28px;
                                        }
                                    }
                                }
                                & .box {
                                    width: 30px;
                                    height: 30px;
                                    border: 1px solid gray;
                                    text-align: center;
                                    font-size: 20px;
                                    display: flex;
                                    justify-content: center;
                                    border-radius: 5px;
                                    cursor: pointer;
                                    &.noBorder{
                                        border: none;
                                        height: 28px;
                                    }
                                }
                                & .unitPrice {
                                    color: rgb(150, 150, 150);
                                }
                            }
                        }
                    }
                    & .actionContainer {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        align-items: flex-end;
                        width: 200px;
                        .price {
                            font-size: 1.1rem;
                            padding-top: 10px;
                        }
                    }
                }

                .billRow {
                    padding: 0.25rem 0 ;
                    display: flex;
                    font-size: 1.1rem;
                    justify-content: space-between;
                    & .label {

                    }
                    & .value {
                        font-weight: bold;
                        font-size: 1.3rem;
                    }
                    & .loading {
                        margin: 0 auto;
                    }
                    & .confirmButton {
                        width: 100%;
                    }
                }
            }
        }
    }
}
