import { styled } from '@mui/material/styles';
import { StepIconProps } from '@mui/material/StepIcon';
import ForwardToInboxIcon from '@mui/icons-material/ForwardToInbox';
import EventIcon from '@mui/icons-material/Event';
import ConfirmationNumberIcon from '@mui/icons-material/ConfirmationNumber';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

const BuyGuideStepIconRoot = styled('div')<{
    ownerState: { completed?: boolean; active?: boolean };
  }>(({ theme, ownerState }) => ({
    backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[700] : '#ccc',
    zIndex: 1,
    color: '#fff',
    width: 50,
    height: 50,
    display: 'flex',
    borderRadius: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    ...(ownerState.active && {
      backgroundImage:
        'linear-gradient(136deg, rgb(77 184 72) 0%, #429b3f 50%, #7cff9a 100%)',
      boxShadow: '2px 0 4px 0 #ffd800, -2px 0 4px 0 #ff8e00, 0 2px 4px 0 #ff00d3, 0 -2px 4px 0 #ff00d3',
    }),
    ...(ownerState.completed && {
      backgroundImage:
        'linear-gradient(136deg, rgb(77 184 72) 0%, #429b3f 50%, #7cff9a 100%)',
    }),
}));

const BuyGuideStepIcon = (props: StepIconProps) => {
    const { active, completed, className } = props;

    const icons: { [index: string]: React.ReactElement } = {
        1: <ForwardToInboxIcon />,
        2: <EventIcon />,
        3: <AccessTimeIcon />,
        4: <ConfirmationNumberIcon />,
    };

    return (
        <BuyGuideStepIconRoot ownerState={{ completed, active }} className={className}>
            {icons[String(props.icon)]}
        </BuyGuideStepIconRoot>
    );
};

export default BuyGuideStepIcon;