/** @type {import('next').NextConfig} */
const { i18n } = require("./next-i18next.config");

const cspHeader = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline' 'https://\*.facebook.net' 'https://www.googletagmanager.com' 'https://\*.doubleclick.net';
  style-src 'self' 'unsafe-inline' 'https://site-assets.fontawesome.com' 'https://fonts.googleapis.com' 'https://fonts.gstatic.com';
  img-src 'self' blob: data:;
  font-src 'self';
  object-src 'none';
  base-uri 'self';
  form-action 'self';
  frame-ancestors 'self' 'https://\*.youtube.com';
  upgrade-insecure-requests;
`;

module.exports = {
  poweredByHeader: false,
  reactStrictMode: true,
  i18n,
  env: {
    PROJECT_FRONTEND_ENV: process.env.PROJECT_FRONTEND_ENV,
    PROJECT_BACKEND_ENV: process.env.PROJECT_BACKEND_ENV,
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      if (!config.resolve.fallback) {
        config.resolve.fallback = {};
      }
      config.resolve.fallback.fs = false;
    }
    return config;
  },
  modularizeImports: {
    "@mui/material": {
      transform: "@mui/material/{{member}}",
    },
    "@mui/icons-material": {
      transform: "@mui/icons-material/{{member}}",
    },
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
        ]
      }
    ];
  },
  transpilePackages: ['mui-file-input'],
  compiler: {
    emotion: true,
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error"], // Remove console.* output except console.error:
          }
        : false,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
}
