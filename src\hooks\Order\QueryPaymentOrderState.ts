import { BACKE<PERSON> } from "@/constants/config";
import { OrderType } from "@/constants/enum/OrderType";
import <PERSON>NDPOINT from "@/models/api/endpoint";
import PaymentGatewayOrderStatusAPIResult from "@/models/api/result/order/paymentGatewayStatus";

const useQueryPaymentOrderState = (orderId: string, gatewayName: string) => {
    return BACKEND.Gateway.useQuery<PaymentGatewayOrderStatusAPIResult>({
        url: ENDPOINT.QueryPaymentOrderState(orderId, gatewayName),
        params: {
            queryKey: `query-paymentgateway-${gatewayName}-orderid-${orderId}-state`
        },
        refetchInterval: (data) => {
            if (data?.data?.responseCode == "0000" && 
                (data?.data.transactionState == OrderType.PAID.toString() || 
                 data?.data.transactionState == OrderType.EXPIRED.toString() || 
                 data?.data.transactionState == OrderType.FAILED.toString())) return false;
            return 3000;
        },
        cacheTime: 0,
    });
};

export { useQueryPaymentOrderState };