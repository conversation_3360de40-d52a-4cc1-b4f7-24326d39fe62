import Modal from "@/components/_CommonElements/Modal";
import { ReactNode, useCallback, useState } from "react";
import styles from "./autoOpened.module.scss";
import classNames from "classnames";

interface Props {
    title: string;
    children: ReactNode;
    disabledBasicLayout?: boolean;
}

const AutoOpenedModal = (props: Props) => {
    const { title, children, disabledBasicLayout } = props;
    const [visible, setVisible] = useState<boolean>(true);
    const onClose = useCallback(() => {
        setVisible(false);
    }, []);
    return (
        <Modal
            classes={{
                paperContainer: classNames(styles.paperContainer, {[styles.disableBasicLayout]: disabledBasicLayout}),
                modalContainer: classNames(styles.modalContainer, {[styles.disableBasicLayout]: disabledBasicLayout}),
                scrollContainer: classNames(styles.scrollContainer, {[styles.disableBasicLayout]: disabledBasicLayout}),
                content: classNames(styles.content, {[styles.disableBasicLayout]: disabledBasicLayout})
            }}
            visible={visible}
            title={disabledBasicLayout ? undefined : title}
            onBackdropClick={onClose}
            onCancel={onClose}
            hideCancelButton
            hideConfirmButton={disabledBasicLayout}
        >
            {children}
        </Modal>
    );
};

export default AutoOpenedModal;