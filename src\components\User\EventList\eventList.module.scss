@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;
@use "@/styles/utils/viewport.module.scss" as viewport;
@import "rfs/scss";

.list {
  display: flex;
  flex: {
    grow: 1;
    direction: column;
    wrap: nowrap;
  }
  padding-top: 20px;
  gap: 20px;
}

div.itemContainer {
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  // padding: 10px;  
  padding-left: 10px;
  border: map.get(theme.$border, "width") solid map.get(theme.$color, "border");
  border-radius: map.get(theme.$border, "radius");
  margin: 10px 0;
}

.emptyRecord {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  border: map.get(theme.$border, "width") solid map.get(theme.$color, "border");
  border-radius: map.get(theme.$border, "radius");
  height: 300px;
  margin: 10px 0 20px 0;
}

div.thumbnail {
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;

  & img {
    object-fit: contain;
    max-width: 300px;
    height: auto;
    width: 100%;
  }
}
$ticket-height: 50px;

.eventInfo {
  font-weight: 700;
  padding: 10px;
  & a > .name:hover {
    text-decoration: underline;
  }
  & .name {
    @include font-size(18px);
  }
  & .dateTime {
    font-weight: normal;
  }
}

.purchasedTickets,
.boundTickets {
  & .imageHeader {
    position: relative;
    overflow: hidden;

    & .eventBannerImage {
      height: 15rem;
      object-fit: contain;
      position: relative;
      z-index: 1;
    }

    & .eventBannerImageBlurredBackground {
      filter: blur(100px) saturate(0.5);
      position: absolute;
      top: 0;
      background-repeat: no-repeat;
      height: 100%;
      width: 100%;
      background-size: cover;
      background-position-y: calc(-15rem / 8);
    }
  }
}

.boundTickets {
  & .actionBar {
    background: linear-gradient(-45deg, rgba(77,184,64,1), rgba(77,184,72,1) 3rem, rgba(255,120,2,1) 3rem, rgba(255,120,2,1) 6rem);

    & .actionButton {
      color: #0c0c0c;
    }
  }
}

.tickets {
  overflow-y: auto;

  & .ticket {
    height: $ticket-height;
    justify-content: space-between;
    flex-wrap: nowrap;

    & .token {
      flex-grow: 1;
      width: 10%;
      align-items: center;
      padding: 0 0 0 10px;

      & > * {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      & span {
        user-select: none;
        display: inline-block;
        border: 2px solid white;
        background-color: map.get(theme.$color, "text");
        color: map.get(theme.$color, "background");
        padding: 0 5px;
        border-radius: 15px;
      }
    }
  }
}

div.upcomingEvent {
  font-size: 18px;
  padding: 15px;
  letter-spacing: 2px;

  & .dateTime {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 70px;
    margin: 0 7px 0 -7px;

    font-weight: 500;

    & :nth-child(1) {
      font-size: 28px;
    }

    & :nth-child(2) {
      font-size: 24px;
    }

    & :nth-child(3) {
      font-size: 20px;
    }
  }

  & .name {
    display: flex;
    align-items: center;
    padding: 0 5px 0 10px;
    flex-grow: 1;
    border-left: 3px solid map.get(theme.$color, "primary");
  }

  & .redeemStatus {
    font-size: 18px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    color: map.get(theme.$color, "secondary");
    min-width: 60px;
    user-select: none;

    @include viewport.within("mobile") {
      font-size: 14px;
      min-width: 50px;
    }

    &.redeemed {
      color: map.get(theme.$color, "primary");
    }
  }
}
.paginationContainer {
    margin: 0px auto;
    max-width: 100%;
}