import { BACKEND } from "@/constants/config";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import ENDPOINT from "@/models/api/endpoint";
import { UpdateShoppingCartItemAPIResult, DeleteShoppingCartItemAPIResult } from "@/models/api/result/user/shoppingCart";
import { decreaseItemQuantity, increaseItemQuantity, removeItem } from "@/redux/slices/cartSlice";
import { dispatch, useSelector } from "@/redux/store";
import API from "@/utils/API";
import Card from "@mui/material/Card";
import Box from "@mui/material/Box"
import { useTranslation } from "next-i18next";
import { useSnackbar } from "notistack";
import { useCallback, useMemo, useState } from "react";
import CardContent from "@mui/material/CardContent";
import shoppingCartMenuStyles from "@/components/Event/ShoppingCart/ShoppingCartMenu/shoppingCartMenu.module.scss";
import ButtonGroup from "@mui/material/ButtonGroup";
import Button from "@mui/material/Button";
import { IconButton } from "@/components/_CommonElements/Button";
import DeleteIcon from '@mui/icons-material/Delete';
import { CircularProgress } from "@mui/material";
import { ShoppingCartSummarizedItem } from "@/models/api/models/ShoppingCart";
import i18n from "@/utils/i18n";
import { useRouter } from "next/router";
import OneTimePaymentOrderItem from "@/models/props/OneTimePaymentOrderItem";

interface ShoppingCartItemDisplayProps {
    item: ShoppingCartSummarizedItem | OneTimePaymentOrderItem;
    isCheckingOut?: boolean;
}
interface UpdateShoppingCartItemProps {
    eventSessionBundleId: string;
    quantity: number;
}
const useShoppingCart = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const updateAsync = async (item: UpdateShoppingCartItemProps) => {
        const formData = API.ToFormData(item as unknown as {[key: string]: unknown});
        return await requestAsync<UpdateShoppingCartItemAPIResult>(
            ENDPOINT.UpdateShoppingCartItem(),
            {
                method: RequestMethod.POST,
                data: formData
            }
        );
    };
    const deleteAsync = async (eventSessionBundleId: string) => {
        const formData = API.ToFormData({"eventSessionBundleId": eventSessionBundleId});
        return await requestAsync<DeleteShoppingCartItemAPIResult>(
            ENDPOINT.DeleteShoppingCartItem(eventSessionBundleId),
            {
                method: RequestMethod.DELETE,
                data: formData
            }
        );
    };
    return { updateAsync, deleteAsync };
};

const ShoppingCartItemDisplay = (props: ShoppingCartItemDisplayProps) => {
    const { item, isCheckingOut = false } = props;
    const [ isUpdating, setIsUpdating ] = useState(false);
    const [ isDeleting, setIsDeleting ] = useState(false);
    const router = useRouter();
    const { enqueueSnackbar } = useSnackbar();
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { updateAsync, deleteAsync } = useShoppingCart();
    const cartItems = useSelector(state => state.cart.items);

    const isOneTimePaymentOrderItem = useMemo(() => 'itemName' in item, []);
    const itemName = useMemo(() => isOneTimePaymentOrderItem ? (item as OneTimePaymentOrderItem).itemName : (item as ShoppingCartSummarizedItem).eventBundleName, [item]);
    const itemCurrency = useMemo(() => isOneTimePaymentOrderItem ? (item as OneTimePaymentOrderItem).currency : (item as ShoppingCartSummarizedItem).currency, [item]);
    const itemOriginalPrice = useMemo(() => isOneTimePaymentOrderItem ? null : (item as ShoppingCartSummarizedItem).originalPrice, [item]);
    const itemPrice = useMemo(() => isOneTimePaymentOrderItem ? (item as OneTimePaymentOrderItem).itemFee : (item as ShoppingCartSummarizedItem).price, [item]);
    const itemQuantity = useMemo(() => isOneTimePaymentOrderItem ? (item as OneTimePaymentOrderItem).itemQuantity : (item as ShoppingCartSummarizedItem).quantity, [item]);

    const decreaseItemByOne = useCallback(() => {
        void (async () => {
            const _item = item as ShoppingCartSummarizedItem;
            setIsUpdating(true);
            const targetItem = cartItems.find(i => i.eventSessionBundleId === _item.eventSessionBundleId);
            if (targetItem) {
                // Dont decrease any more, keep the last one quantity for this item.
                if (targetItem.quantity > 1) {
                    try {
                        await updateAsync({
                            eventSessionBundleId: _item.eventSessionBundleId,
                            quantity: -1
                        });
                        dispatch(decreaseItemQuantity({eventSessionBundleId: _item.eventSessionBundleId, quantity: 1}));   
                    }
                    catch (error: unknown) {
                        const errorMessage = API.GetErrorMessage(error);
                        enqueueSnackbar(errorMessage, { variant: "error" });
                    }
                }
            }
            setIsUpdating(false);
        })();
    }, [updateAsync, enqueueSnackbar, cartItems, item]);

    const isLoading = useMemo(() => isUpdating || isDeleting, [isUpdating, isDeleting]);
    const increaseItemByOne = useCallback(() => {
        void (async () => {
            const _item = item as ShoppingCartSummarizedItem;
            setIsUpdating(true);
            try {
                await updateAsync({
                    eventSessionBundleId: _item.eventSessionBundleId,
                    quantity: 1
                });
                dispatch(increaseItemQuantity({ eventSessionBundleId: _item.eventSessionBundleId, quantity: 1}));   
            }
            catch (error: unknown) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
            }
            setIsUpdating(false);
        })();
        
    }, [updateAsync, enqueueSnackbar, item]);

    const removeItemInCart = useCallback((eventSessionBundleId: string) => {
        void (async () => {
            setIsDeleting(true);
            try {
                await deleteAsync(eventSessionBundleId);
                dispatch(removeItem(eventSessionBundleId));
                enqueueSnackbar(snackbarTranslation("messages.cart.remove.success"), { variant: "success" });
            }
            catch (error) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
            }
        })();
    }, [deleteAsync, enqueueSnackbar, snackbarTranslation]);

    return (
        <Card elevation={1} className={shoppingCartMenuStyles.itemCardContainer}>
            <Box className={shoppingCartMenuStyles.itemNameContainer}>
                <CardContent>
                    {itemName}<br />
                    { i18n.GetCurrency(itemCurrency, itemOriginalPrice ?? itemPrice * itemQuantity, router.locale) } (@ { i18n.GetCurrency(item.currency, itemOriginalPrice ?? itemPrice, router.locale) })
                </CardContent>
            </Box>
            {!isOneTimePaymentOrderItem && !isCheckingOut ? 
                <Box className={shoppingCartMenuStyles.itemQuantityContainer}>
                    <CardContent sx={{display: "flex", gap: 1}}>
                        <ButtonGroup size="small" aria-label="small outlined button group">
                            <Button disabled={isLoading} onClick={decreaseItemByOne}>-</Button>
                            <Button className={shoppingCartMenuStyles.quantity} disabled>{isUpdating ? <CircularProgress size="1rem" /> : itemQuantity}</Button>
                            <Button disabled={isLoading} onClick={increaseItemByOne}>+</Button>
                        </ButtonGroup>
                        <IconButton disabled={isLoading} onClick={() => removeItemInCart((item as ShoppingCartSummarizedItem).eventSessionBundleId)}>
                            { isDeleting ? <CircularProgress size="1rem" /> : <DeleteIcon fontSize="medium" /> }
                        </IconButton>
                    </CardContent>
                </Box>
                : null
            }
        </Card>
    );
};
ShoppingCartItemDisplay.displayName = "ShoppingCartItemDisplay";
export default ShoppingCartItemDisplay;