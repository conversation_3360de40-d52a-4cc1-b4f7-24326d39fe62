import { APISuccessResult } from "@stoneleigh/api-lib";
import { ShoppingCartSummarizedItem } from "../../models/ShoppingCart";

type FetchShoppingCartAPIResult = APISuccessResult<{
    eventId: string;
    eventName: string;
    items: ShoppingCartSummarizedItem[];
}>;
type UpdateShoppingCartItemAPIResult = APISuccessResult<null>;
type DeleteShoppingCartItemAPIResult = APISuccessResult<null>;
type DeleteAllShoppingCartItemAPIResult = APISuccessResult<null>;

export type { ShoppingCartSummarizedItem, FetchShoppingCartAPIResult, UpdateShoppingCartItemAPIResult, DeleteShoppingCartItemAPIResult, DeleteAllShoppingCartItemAPIResult };