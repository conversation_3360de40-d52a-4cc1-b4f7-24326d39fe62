$website-min-width: 320px;

$website-min-height: 320px;

$mobile-breakpoint: 480px;
$tablet-breakpoint: 1100px;
$desktop-breakpoint: 1440px;
$wide-breakpoint: 10000px;

$breakpoint: (
  "mobile" : $mobile-breakpoint,
  "tablet" : $tablet-breakpoint,
  "desktop" : $desktop-breakpoint,
  "wide": $wide-breakpoint
);

$breakpoint-beyond: (
  "mobile": $mobile-breakpoint + 1,
  "tablet": $tablet-breakpoint + 1,
  "desktop": $desktop-breakpoint + 1,
  "wide": $wide-breakpoint + 1
);


@mixin within($display) {
  @each $key, $value in $breakpoint {
    @if ($display == $key) {
      @media (max-width: $value) {
        @content;
      };
    };
  };
};
@mixin beyond($display) {
    @each $key,
    $value in $breakpoint-beyond {
        @if ($display == $key) {
            @media (min-width: $value) {
                @content;
            }
        }
    }
}

@mixin queryWithOrientation($display, $orientation) {
  @each $key, $value in $breakpoint {
    @if ($display == $key) {
      @media (max-width: $value) and (orientation: $orientation) {
        @content;
      };
    };
  };
};