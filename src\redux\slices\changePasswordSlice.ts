import { createSlice, PayloadAction } from "@reduxjs/toolkit";
export interface ChangePasswordSliceState {
    oldPassword:string,
    newPassword: string,
    confirmNewPassword: string
}

const initialState: ChangePasswordSliceState = {
    oldPassword:"",  
    newPassword: "",
    confirmNewPassword: ""
};

export const changePasswordSlice = createSlice({
    name: "changePassword",
    initialState,
    reducers: {
        setOldPassword: (state, action: PayloadAction<string>) => {
            state.oldPassword = action.payload;
        },
        setNewPassword: (state, action: PayloadAction<string>) => {
            state.newPassword = action.payload;
        },
        setConfirmNewPassword: (state, action: PayloadAction<string>) => {
            state.confirmNewPassword = action.payload;
        }
    }
});

export const { setOldPassword,  setNewPassword, setConfirmNewPassword } = changePasswordSlice.actions;

export default changePasswordSlice.reducer;