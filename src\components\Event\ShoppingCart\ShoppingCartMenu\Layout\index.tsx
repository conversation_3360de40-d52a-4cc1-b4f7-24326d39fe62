import React, {  useCallback, useState, useMemo, Fragment } from "react";
import { usePathname } from "next/navigation";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { getCookie } from "cookies-next";
import Markdown from "markdown-to-jsx";
import { useSnackbar } from "notistack";
import { Box, Button, ButtonGroup, Divider, Popover, Paper, Stack, Typography, useMediaQuery, SwipeableDrawer, Tabs, Tab, CircularProgress, RadioGroup, Radio, FormControl, InputLabel, FilledInput, InputAdornment, Chip } from "@mui/material";
import { styled } from '@mui/styles';
import { useTheme } from "@mui/material/styles";
import { grey } from "@mui/material/colors";
import CloseIcon from '@mui/icons-material/Close';
import ShoppingCartCheckoutIcon from '@mui/icons-material/ShoppingCartCheckout';

import RouteMap from "@/constants/config/RouteMap";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { EnumDiscountCodeType } from "@/constants/enum/DiscountCodeType";
import PaymentMethodList from "@/constants/paymentMethods";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import { useDiscountCode } from "@/hooks/Order/DiscountCode";

import { Checkbox } from "@/components/_CommonElements/Input";
import { IconButton } from "@/components/_CommonElements/Button";
import PromoteCodeInput from "@/components/Event/ShoppingCart/ShoppingCartMenu/PromoteCodeInput";
import ShoppingCartLayoutActionArea, { ActionComponent } from "@/components/Event/ShoppingCart/ShoppingCartMenu/ActionArea";
import ShoppingCartItems from "@/components/Event/ShoppingCart/ShoppingCartMenu/ItemList";
import shoppingCartMenuStyles from "@/components/Event/ShoppingCart/ShoppingCartMenu/shoppingCartMenu.module.scss";
import { ShoppingCartSummarizedItem } from "@/models/api/result/user/shoppingCart";
import EventPaymentMethod from "@/models/api/models/EventPaymentMethod";
import EventPaymentGatewayAndMethod from "@/models/api/models/EventPaymentGatewayAndMethod";
import { dispatch, useSelector } from "@/redux/store";
import { setDiscountInfo, setItems, setTotalPrice } from "@/redux/slices/cartSlice";
import i18n from "@/utils/i18n";
import theme from "@/styles/mui/theme";
import PaymentSelector from "../PaymentSelector";


interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}
const TabPanel = React.memo((props: TabPanelProps) => {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Paper elevation={0}>
                    {children}
                </Paper>
            )}
        </div>
    );
});
TabPanel.displayName = "TabPanel";

const Puller = styled(Box)(() => ({
    width: 30,
    height: 6,
    backgroundColor: theme.palette.mode === 'light' ? grey[300] : grey[900],
    borderRadius: 3,
    position: 'absolute',
    top: 8,
    left: 'calc(50% - 15px)',
}));

const AgreementLabel = React.memo(() => {
    const { t: purchaseTranslation } = useTranslation(EnumTranslationJson.Purchase);
    const Router = useRouter();

    return (
        <span>
            <Markdown
                options={{
                    wrapper: Fragment,
                    forceWrapper: false
                }}
            >
                {purchaseTranslation("modal.content.agreementClaim", {
                    TNC_URL: `/${Router.locale}${RouteMap.TermsAndConditions}`,
                    POLICY_URL: `/${Router.locale}${RouteMap.PrivacyPolicy}`
                })}
            </Markdown>
        </span>
    );
});
AgreementLabel.displayName = "AgreementLabel";

interface ShoppingCartLayoutProps {
    eventName?: string;
    eventCurrency?: string;
    cartDataSource: { [char: string]: ShoppingCartSummarizedItem[] };
    anchorEl: HTMLButtonElement | null;
    open: boolean;
    onOpen: (e: React.MouseEvent<HTMLButtonElement>) => void;
    onClose: () => void;
    submitOrder: (isAcceptedAgreement: boolean, promoteCode: string, paymentMethod: { id: string, gateway: string }) => Promise<void>;
    isLoading: boolean;
}
const ShoppingCartLayout = (props: ShoppingCartLayoutProps) => {
    const {
        eventName,
        eventCurrency,
        cartDataSource,
        anchorEl,
        open,
        onOpen,
        onClose,
        submitOrder,
        isLoading
    } = props;

    const theme = useTheme();
    const pathname = usePathname();
    const router = useRouter();
    const screenBreakpointLagerThenSM = useMediaQuery(theme?.breakpoints?.up('sm'));
    const { t: shoppingCartTranslation } = useTranslation(EnumTranslationJson.ShoppingCart);
    const { t: purchaseTranslation } = useTranslation(EnumTranslationJson.Purchase);
    const { t: snackBarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { removeAppliedDiscountCodeAsync } = useDiscountCode();
    const { enqueueSnackbar } = useSnackbar();
    const [promoteCode, setPromoteCode] = useState<string>("");
    const [isAcceptedAgreement, setIsAcceptedAgreement] = useState<boolean>(false);
    const [isCheckingOut, setIsCheckingOut] = useState<boolean>(false);
    const [tabIndex, setTabIndex] = useState<number>(0);
    const [selectedPayment, setSelectedPayment] = useState<EventPaymentGatewayAndMethod>();

    const isEventPage = useMemo(() => pathname.includes('event'), [pathname]);
    const shoppginCartPopOverId = useMemo(() => `${isEventPage ? 'event-' : ''}shopping-cart-popper`, [isEventPage]);
    const cartItems = useSelector(state => state.cart.items);
    const discountForOrder = useSelector(state => state.cart.discountInfo);
    const discountForOrderTotalPrice = useSelector(state => state.cart.totalPrice);
    const discountValueAndType = useMemo(() => {
        if (!discountForOrder) return undefined;
        let _result = "";
        const _discountValueType: EnumDiscountCodeType = discountForOrder.discountValueType as EnumDiscountCodeType;
        switch (_discountValueType) {
            case EnumDiscountCodeType.FIXED:
                _result = i18n.GetCurrency(eventCurrency as string, discountForOrder.discountValue, router.locale);
                break;
            case EnumDiscountCodeType.PERCENTAGE:
                _result = discountForOrder.discountValue * 100 + "%";
                break;
            default:
                console.log(eventCurrency);
                _result = discountForOrder.discountValue.toString();
        }
        return " -" + _result;
    }, [discountForOrder, eventCurrency, router.locale]);
    const subTotalPriceInCart = useMemo(() => 
        Object.values(cartDataSource).flat().reduce(
            (subTotal: number, item) => (subTotal += ((item?.originalPrice ?? item.price) * item.quantity)) && subTotal, 0)
    , [cartDataSource]);
    const totalPriceInCart = useMemo(() => {
        if (!discountForOrder) return Object.values(cartDataSource).flat().reduce((total: number, item) => (total += (item.price * item.quantity)) && total, 0);
        return discountForOrderTotalPrice;
    }, [cartDataSource, discountForOrder, discountForOrderTotalPrice]);
    const referralCode = getCookie(EnumCookieKey.TRACED_REFERRAL_CODE)?.toString() ?? "";

    const allPaymentMethods: EventPaymentMethod[] = PaymentMethodList.items;

    const onPromoteCodeChanged = useCallback((code: string) => {
        setPromoteCode(code);
    }, []);

    const onChangeIsAcceptedAgreement = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setIsAcceptedAgreement(e.target.checked);
    }, []);

    const checkIsAcceptedAgreement = useCallback(() => isAcceptedAgreement, [isAcceptedAgreement]);

    const backToItemList = useCallback(() => {
        const rollbackCartItems = cartItems.map(item => ({ ...item, price: item.price ?? 0 }));
        dispatch(setItems(rollbackCartItems));
        setTabIndex(0);
    }, [cartItems]);

    const createOrder = useCallback(() => {
        setIsCheckingOut(true);
        if (selectedPayment) {
            void submitOrder(isAcceptedAgreement, referralCode, { id: selectedPayment.paymentGatewayPaymentMethodName, gateway: selectedPayment.paymentGatewayId });
        }
        setIsCheckingOut(false);
    }, [selectedPayment, submitOrder, isAcceptedAgreement, referralCode]);

    const removeAppliedDiscount = useCallback(async () => {
        try {
            const removeResponse = await removeAppliedDiscountCodeAsync();
            if (removeResponse.data) {
                const rollbackCartItems = cartItems.map(item => ({ ...item, price: item.originalPrice ?? item.price }));
                dispatch(setItems(rollbackCartItems));
                dispatch(setDiscountInfo(undefined));
            }
        } catch (error) {
            enqueueSnackbar(snackBarTranslation("messages.discountCode.apply.error.invalid"), { variant: "error" });
        }
    }, [cartItems, enqueueSnackbar, removeAppliedDiscountCodeAsync, snackBarTranslation]);

    const setShowPromotedCodeInput = useCallback(() => {
        setTabIndex(1);
    }, []);
    const Actions = useMemo<ActionComponent[]>(() => ([
        {
            label: 'popover.button.checkout',
            onClick: () => {
                setShowPromotedCodeInput();
            },
            icon: <ShoppingCartCheckoutIcon />,
            disableCheckingOn: checkIsAcceptedAgreement,
        }
    ]), [setShowPromotedCodeInput, checkIsAcceptedAgreement]);

    const ShopppingCartLayoutCore = useMemo(() => {
        return (
            <Paper 
                elevation={8} 
                className={shoppingCartMenuStyles.shoppingCartPopperContainer}
                sx={{
                    width: {
                        md: '45vw',
                        lg: '35vw',
                    }
                }}>
                <Box pt={1} pl={1} pr={1} display={'flex'} justifyContent={'flex-end'} sx={{ minHeight: "1.5rem" }}>
                    {screenBreakpointLagerThenSM && 
                        <IconButton
                            color="inherit"
                            aria-label="close"
                            onClick={onClose}
                        >
                            <CloseIcon />
                        </IconButton>
                    }
                </Box>
                <Box>
                    <Box pl={2}>
                        <Typography variant="h6">
                            {eventName}
                        </Typography>
                    </Box>
                    <Tabs value={tabIndex} variant="fullWidth" sx={{ display: 'none' }}>
                        <Tab label="cart" />
                        <Tab label="promote-code" />
                    </Tabs>

                    <TabPanel value={tabIndex} index={0}>
                        <ShoppingCartItems dataSource={cartDataSource} />
                        <ShoppingCartLayoutActionArea 
                            agreementComponent={
                                <Checkbox
                                    label={<AgreementLabel />}
                                    checked={isAcceptedAgreement}
                                    onChange={onChangeIsAcceptedAgreement}
                                />
                            }
                            actions={Actions}
                        />
                    </TabPanel>
                    <TabPanel value={tabIndex} index={1}>
                        <Stack 
                            direction={'column'} 
                            spacing={2} p={1} 
                            justifyContent={'flex-start'} 
                            alignItems={'center'}
                            className={shoppingCartMenuStyles.cartPromoteAndOverview}
                        >
                            {discountForOrder ?
                                <div className={shoppingCartMenuStyles.discountPrice}>
                                    <span>{purchaseTranslation("modal.subTotal")}</span>
                                    { eventCurrency && <b>{i18n.GetCurrency(eventCurrency, subTotalPriceInCart, router.locale)}</b> }
                                    <br />
                                    <span>{purchaseTranslation("modal.discountCaption")}</span>
                                    <Chip
                                        label={discountValueAndType}
                                        variant="outlined"
                                        className={shoppingCartMenuStyles.appliedClip}
                                        onClick={() => void removeAppliedDiscount()}
                                        onDelete={() => void removeAppliedDiscount()}
                                    />
                                    {/* <Markdown
                                        options={{
                                            wrapper: Fragment,
                                            forceWrapper: false,
                                        }}
                                    >          
                                        {purchaseTranslation("modal.discountCaption", { 
                                            "DISCOUNT_VALUE_TYPE": discountValueAndType,
                                            "DISCOUNT_CAPTION_CLASS": shoppingCartMenuStyles.discountCaption
                                        })}
                                    </Markdown> */}
                                </div> : null
                            }
                            <div className={shoppingCartMenuStyles.totalPrice}>
                                <span>{purchaseTranslation("modal.total")}</span>
                                { eventCurrency && <b>{i18n.GetCurrency(eventCurrency, totalPriceInCart, router.locale)}</b> }
                            </div>
                            {!discountForOrder && 
                                <Box sx={{ alignSelf: 'flex-end' }}>
                                    <PromoteCodeInput onChange={onPromoteCodeChanged} />
                                </Box>
                            }
                            <Box className={shoppingCartMenuStyles.paymenttitleContainer}>
                                <div className={shoppingCartMenuStyles.group}>
                                    <h2>{purchaseTranslation('paymenttitle.title')}</h2>
                                    <div className={shoppingCartMenuStyles.row}>
                                        <div className={shoppingCartMenuStyles.col}>
                                            <PaymentSelector 
                                                allPaymentMethods={allPaymentMethods}
                                                isCheckingOut={isCheckingOut}
                                                selectedPaymentState={[selectedPayment, setSelectedPayment]}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </Box>
                        </Stack>
                        <Box sx={{ position: 'relative' }}>
                            <Divider />
                            <ButtonGroup 
                                variant="text"
                                size={'large'}
                                sx={{
                                    right: 0
                                }}
                                fullWidth
                            >
                                <Button onClick={backToItemList}>{shoppingCartTranslation('popover.button.back')}</Button>
                                <Button onClick={createOrder} disabled={!selectedPayment}>{shoppingCartTranslation('popover.button.goToPay')}</Button>
                            </ButtonGroup>
                        </Box>
                    </TabPanel>
                </Box>
                { isLoading &&
                    <Box sx={{background: "rgba(255, 255, 255, 0.5)", position: "absolute", top: 0, bottom: 0, left: 0, right: 0, display: "flex", justifyContent: "center", alignItems: "center"}}>
                        <CircularProgress />
                    </Box>
                }
            </Paper>
        )
    }, [
        screenBreakpointLagerThenSM,  
        eventName, 
        tabIndex, 
        cartDataSource, 
        isAcceptedAgreement,  
        Actions, 
        discountForOrder, 
        purchaseTranslation, 
        eventCurrency, 
        subTotalPriceInCart, 
        router.locale, 
        discountValueAndType, 
        totalPriceInCart,  
        selectedPayment,
        allPaymentMethods, 
        shoppingCartTranslation,  
        isLoading,
        isCheckingOut,
        onClose,
        onChangeIsAcceptedAgreement,
        onPromoteCodeChanged,
        backToItemList, 
        createOrder,
    ]);

    if (screenBreakpointLagerThenSM) {
        return (
            <Popover
                id={shoppginCartPopOverId}
                anchorOrigin={{
                    vertical: 'center',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                anchorEl={anchorEl}
                open={open}
                onClose={onClose}
                className={shoppingCartMenuStyles.shoppingCartPopper}
                slotProps={{
                    root: {
                        slotProps: {
                            backdrop: {
                                style: {
                                    backgroundColor: 'transparent',
                                },
                            },
                        }
                    }
                }}
                disablePortal={true}
                disableScrollLock={true}
                marginThreshold={0}
            >
                {ShopppingCartLayoutCore}
            </Popover>
        );
    }
    return (
        <SwipeableDrawer 
            anchor="bottom"
            open={open}
            onOpen={onOpen}
            onClose={onClose}>
            <Puller />
            {ShopppingCartLayoutCore}
        </SwipeableDrawer>
    );
};

export default ShoppingCartLayout;