@use 'sass:map';
@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;
@import "rfs/scss";

@mixin letter-spacing() {
    $letter-spacing: 4px;
    letter-spacing: $letter-spacing;
    display: inline-block;
    transform: translate($letter-spacing * 0.5, 1px);
};

.accountInfoContainer {
  width: 100%;
};

.field {
  margin: 10px 0;
};

.content {
  @include font-size(20px);
  letter-spacing: 2px;
  padding: 0 2px;
};

.selectBox {
  max-width: 650px;
  width: 100%;
  @include viewport.within("tablet") {
      width: 90vw;
  }
}

.selectOption {
  white-space: pre-wrap !important;   
  &:hover {
      background-color: rgba(0, 0, 0, 0.1) !important;
  }
}

.accountInfoContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  & .portrait  {
    display: flex;
    flex-direction: column;
    flex: 0.5;
    align-items: center;
    justify-content: center;

    & img {
      max-width: 200px;
    }

    & .uploadImageButton {
      cursor: pointer;
    }
  }

  & .detail {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  & .formBody {
    max-width: 100%;


    @include viewport.within("tablet") {
      max-width: calc(100vw - 32px - 30px);
    }
    @include viewport.beyond("desktop") {
      max-width: calc(100% - 32px - 60px - 46px);
    }
  }
}