/** @type {import('next-i18next').UserConfig} */
module.exports = {
  i18n: {
    localeDetection: false,
    // https://github.com/isaachinman/next-i18next#options
    defaultLocale: "_default",
    locales: ["_default", "zh-HK", "en-US", "th-TH"],
    // reloadOnPrerender: true, // https://github.com/isaachinman/next-i18next#reloading-resources-in-development
    returnNull: false,
    localeExtension: "yml"
  },
};

