import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import { useTranslation } from "next-i18next";
import { Dispatch, SetStateAction, useCallback, useEffect, useState } from "react";

import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useReservableSession } from "@/hooks/MembershipReservation/ReservableSession";
import ReserveSessionAPIResult from "@/models/api/result/reserve/reserveSession";
import API from "@/utils/API";
import { CircularProgress } from "@mui/material";
import { useSnackbar } from "notistack";
import dayjs from "dayjs";
import UserReservedSession from "@/models/api/models/UserReservedSession";

interface ReserveCancelDialogProps {
    userReservedSession: UserReservedSession | null;
    setUserBookedResult: Dispatch<SetStateAction<UserReservedSession[] | null>>;
    open: boolean;
    onClose: () => void;
}

const ReserveCancelDialog = (props: ReserveCancelDialogProps) => {
    const { userReservedSession, setUserBookedResult, open, onClose } = props;
    const [ isLoading, setIsloading ] = useState(false);
    
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const { cancelReservedSessionAsync } = useReservableSession();
    const { enqueueSnackbar } = useSnackbar();
    
    const handleClose = useCallback(() => {
        onClose();
    }, []);

    const handleAllow = useCallback(() => {
        void (async () => {
            setIsloading(true);
            try {
                if (userReservedSession) {
                    const reserveId = userReservedSession.reserveId ?? "";
                    const { data: cancelSuccess } : ReserveSessionAPIResult = await cancelReservedSessionAsync(reserveId);
                    if (cancelSuccess) {
                        enqueueSnackbar(modalTranslation("modals.membershipReserveCancel.apiResponse.success"), { variant: "success" });
                        setUserBookedResult(null);
                    }
                }
            }
            catch (error) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
            } finally {
                setIsloading(false);
                handleClose();
            }
        })();
    }, [userReservedSession, enqueueSnackbar, handleClose]);

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            disableEscapeKeyDown
        >
            <DialogTitle>
                {modalTranslation("modals.membershipReserveCancel.title")}
            </DialogTitle>
            <DialogContent>
                <DialogContentText>
                    
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button disabled={isLoading} onClick={handleClose} color="error">{modalTranslation("buttons.cancel")}</Button>
                <Button disabled={isLoading} onClick={handleAllow} autoFocus>
                    { isLoading ? <CircularProgress /> : modalTranslation("buttons.confirm") }
                </Button>
            </DialogActions>
        </Dialog>
    );
};

ReserveCancelDialog.displayName = "ReserveCancelDialog";
export default ReserveCancelDialog;