@use "@/styles/utils/viewport.module.scss"as viewport;

.hoverStyle {
    font-size: 18px !important;
    font-weight: 600 !important;

    @include viewport.within("desktop") {
        font-size: 16px;
        font-weight: 600;
    }
    &.active {
        color: #ff7802;
        div[class~="nav-label"] {
            > div {
                &:nth-child(1):after {
                    width: 100%;
                    left: 0;
                }
            }
        }
    }
    &:hover {
        div[class~="nav-label"] {
            > div {
                &:nth-child(1):after {
                    width: 100%;
                    left: 0;
                }
            }
        }
    }
    div[class~="nav-label"] {
        > div {
            position: relative;
            display: flex;
            flex-direction: column;
            white-space: nowrap;
            transition: text-shadow 0.4s;
            font-size: 0.8rem !important;
            font-weight: 700;
            transition-property: background-color,transform,-webkit-transform;
            transition-duration: .2s;
            letter-spacing: 3px;
            @include viewport.within("tablet") {
                border-bottom: none;
            }
            &:nth-child(1):after {
                position: absolute;
                content: "";
                width: 0;
                height: 3px;
                left: 50%;
                bottom: -8px;
                background-color: #ff7802;
                transition: all .2s ease-in-out;
            }
        }
    }
}

.mobileItem {
    display: none !important;

    @include viewport.within("desktop") {
        display: none !important;
    }

    @include viewport.within("tablet") {
        display: flex !important;
    }
}

.beforeLine {
    &::before {
        content: "";
        width: 90%;
        border-bottom: solid 1px rgb(0, 0, 0);
        position: absolute;
        margin: 0 auto;
    }
}