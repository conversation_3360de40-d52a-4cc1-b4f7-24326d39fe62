import Label from "@/components/_CommonElements/Label";
import classNames from "classnames";
import React from "react";
import { ReactNode } from "react";
import styles from "./eventMetadata.module.scss";

interface EventMetadataProps {
    title: string;
    content: ReactNode;
    classes?: {
        title?: string;
        content?: string;
    }
}
const EventMetadata = React.memo((props: EventMetadataProps) => {
    const { title, content, classes = {} } = props;
    return (
        <div className={styles.metadata}>
            <Label className={classes.title} title={title} />
            <div className={classNames(
                styles.content,
                {[classes.content!]: classes.content !== undefined })}>
                {content}
            </div>
        </div>
    );
});
EventMetadata.displayName = "EventMetadata";
export default EventMetadata;