import { FunctionComponent } from "react";
import styles from "@/components/_CommonElements/Input/input.module.scss";
import classNames from "classnames";

import MUISwitch from '@mui/material/Switch';

interface SwitchProps {
  checked: boolean;
  disabled?: boolean;
  defaultChecked?: boolean;
  onChange?(event: React.ChangeEvent<HTMLInputElement>): void;
};

const Switch: FunctionComponent<SwitchProps> = (props) => {
  const {
    checked,
    disabled,
    defaultChecked,
    onChange,
  } = props;

  return (<div className={styles.switch}>
    <MUISwitch
      checked={checked}
      disabled={disabled}
      defaultChecked={defaultChecked}
      onChange={onChange}
    />
  </div>);
};

export default Switch;