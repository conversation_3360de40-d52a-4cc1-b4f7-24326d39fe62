import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import AccountBoxIcon from '@mui/icons-material/AccountBox';
import BadgeIcon from '@mui/icons-material/Badge';
import BookOnlineIcon from '@mui/icons-material/BookOnline';
import HomeIcon from '@mui/icons-material/Home';
import InsertInvitationIcon from '@mui/icons-material/InsertInvitation';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import { styled } from '@mui/material/styles';
import { AppBar, IconButton, Toolbar, Typography, Fab, Box, Divider } from "@mui/material";
import { useTranslation } from "next-i18next";

import styles from "./mobileBottomAppBar.module.scss";
import RouteMap from "@/constants/config/RouteMap";
import { useSelector } from "@/redux/store";

const StyledFab = styled(Fab)({
    position: 'absolute',
    zIndex: 1,
    top: -30,
    left: 0,
    right: 0,
    margin: '0 auto',
});

const MobileBottomAppBar = () => {
    const { t: mobileBottomAppBarTranslation } = useTranslation(EnumTranslationJson.MobileBottomAppBar);
    const isMembership = useSelector(state => state.user.isMembership);

    const showMembership = false;

    return (
        <AppBar position="fixed" className={styles.appBar}>
            <Toolbar className={styles.toolBar}>
                <IconButton color="inherit" className={styles.toolButton} LinkComponent={'a'} href={RouteMap.UserProfile}>
                    <AccountBoxIcon />
                    <Typography variant="caption">{mobileBottomAppBarTranslation("appBar.toolBar.profile")}</Typography>
                </IconButton>
                {showMembership ??
                    <>
                        <Divider orientation="vertical" variant="middle" flexItem />
                        <IconButton color="inherit" className={styles.toolButton} LinkComponent={'a'} href={RouteMap.MembershipSubscription}>
                            <BadgeIcon />
                            <Typography variant="caption">{mobileBottomAppBarTranslation("appBar.toolBar.membershipSubscription")}</Typography>
                        </IconButton>
                    </>
                }
                <StyledFab color="secondary" className={styles.fab} sx={{ flexDirection: 'column' }} LinkComponent={'a'} href={RouteMap.BoundTickets}>
                    <BookOnlineIcon />
                </StyledFab>
                <Box sx={{ flexGrow: 1 }} />         
                <IconButton color="inherit" className={styles.toolButton} LinkComponent={'a'} href={RouteMap.ticketOrders}>
                    <ReceiptLongIcon />
                    <Typography variant="caption">{mobileBottomAppBarTranslation("appBar.toolBar.myOrders")}</Typography>
                </IconButton>
                {showMembership ?? 
                    <>
                        <Divider orientation="vertical" variant="middle" flexItem />
                        <IconButton color="inherit" className={styles.toolButton} LinkComponent={'a'} href={RouteMap.MembershipReservation} disabled={!isMembership}>
                            <InsertInvitationIcon />
                            <Typography variant="caption">{mobileBottomAppBarTranslation("appBar.toolBar.membershipReservation")}</Typography>
                        </IconButton>
                    </>
                }
            </Toolbar>
        </AppBar>
    );
};

export default MobileBottomAppBar;