import { TextButton } from "@/components/_CommonElements/Button";
import Form from "@/components/_CommonElements/Form";
import { TextInput } from "@/components/_CommonElements/Input";
import ModalForgotPasswordWindow from "@/components/ModalWindow/ForgotPassword";
import { BACKEND, FRONTEND } from "@/constants/config";
import RouteMap from "@/constants/config/RouteMap";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import ENDPOINT from "@/models/api/endpoint";
import LoginInfo from "@/models/api/models/LoginInfo";
import UserInfoAPIResult from "@/models/api/result/user/info";
import UserLoginAPIResult from "@/models/api/result/user/login";
import { setExpiry, setId, setName } from "@/redux/slices/identitySlice";
import { ModalActions } from "@/redux/slices/uiSlice";
import { setIsMembership, setUserIdHash, setUserVerified } from "@/redux/slices/userInfoSlice";
import { dispatch } from "@/redux/store";
import API from "@/utils/API";
import InputValidation from "@/utils/regex/InputValidation";
import Grid from "@mui/material/Grid";
import { setCookie } from 'cookies-next';
import { useTranslation } from "next-i18next";
import Router from "next/router";
import { enqueueSnackbar } from "notistack";
import { useCallback, useMemo } from "react";
import { useState } from "react";

const useLogin = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit();
    const requestAsync = async (email: string, password: string) => {
        const formData = new FormData();
        formData.append("email", email);
        formData.append("recaptcha", "123");
        formData.append("password", password);
        return await fetchAsync<UserLoginAPIResult>(
            ENDPOINT.Login(),
            {
                method: "POST",
                data: formData
            }
        );
    };
    return requestAsync;
};
const useGetUserInfo = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async () => {
        return await fetchAsync<UserInfoAPIResult>(
            ENDPOINT.GetUserInfo(),
            {
                method: "GET"
            }
        );
    };
    return requestAsync;
};

const IsInvalidEmailInput = (email: string | undefined) => {
    if (email === undefined) {
        return false;
    }
    return !InputValidation.validateEmail(email);
}
const IsInvalidPasswordInput = (password: string | undefined) => {
    if (password === undefined) {
        return false;
    }
    return !InputValidation.validatePassword(password) || password.length < FRONTEND.PasswordMinLength;
};
interface LoginFormProps {
    noFormHead?: boolean;
}
const LoginForm = (props: LoginFormProps) => {
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { t: accountTranslation } = useTranslation(EnumTranslationJson.Account);
    const { t: validationTranslation } = useTranslation(EnumTranslationJson.Validation);
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);

    const [forgotPasswordModalVisible, setForgotPasswordModalVisible] = useState(false);

    const [ email, setEmail ] = useState<string>();
    const [ password, setPassword ] =  useState<string>();

    const invalidEmail = useMemo(() => IsInvalidEmailInput(email), [email])
    const invalidPassword = useMemo(() => IsInvalidPasswordInput(password), [password])

    const loginAsync = useLogin();
    const getUserInfoAsync = useGetUserInfo();

    const onLoginSuccess = useCallback(async (data: LoginInfo) => {
        setCookie(EnumCookieKey.USER_JWT, data.userJWT, {
            maxAge: Math.floor((Number(data.tokenExpiryDateTime) - Date.now()) / 1000),
            sameSite: "lax",
        });
        dispatch(setName(data.nickname));
        dispatch(setId(data.userId));
        dispatch(setExpiry(new Date(data.tokenExpiryDateTime)));
        dispatch(ModalActions.closeLoginModal());
        const res = await getUserInfoAsync();
        const _user = res.data!;
        dispatch(setUserVerified(_user.isVerified));
        dispatch(setUserIdHash(_user.userIdHash));
        if (_user.membershipSubscription?.state === "VERIFIED") dispatch(setIsMembership(true));

        let goto = Router.asPath;
        switch (Router.asPath) {
            case RouteMap.Login:
            case RouteMap.SignUp:
                goto = RouteMap.Main;
                break;
            default:
                goto = Router.asPath;
                break;
        }
        void Router.push(goto); 
    }, [getUserInfoAsync]);
    const disabledSubmit = useMemo(() => {
        if (email === undefined || password === undefined) {
            return true;
        }
        if (invalidEmail || invalidPassword) {
            return false;
        }
    }, [email, password, invalidEmail, invalidPassword]);
    const onLogin = useCallback(() => {
        void (async () => {
        if (disabledSubmit) {
            return;
        }
        try {
            const res = (await loginAsync(email!, password!)).data!;
            void onLoginSuccess(res);
            enqueueSnackbar(snackbarTranslation("messages.login.success", {
                USERNAME: res.nickname
            }), { variant: "success" });
        } catch (error) {
            const errorMessage = API.GetErrorMessage(error);
            enqueueSnackbar(errorMessage, { variant: "error" });
        }})();
    }, [disabledSubmit, email, password, loginAsync, onLoginSuccess, snackbarTranslation]);

    const showForgotPasswordModal = useCallback(() => {
        setForgotPasswordModalVisible(true);
    }, []);
    const hideForgotPasswordModal = useCallback(() => {
        setForgotPasswordModalVisible(false);
    }, []);

    const onChangeEmail = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setEmail(e.target.value);
    }, []);
    const onChangePassword = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setPassword(e.target.value);
    }, []);

    return (
        <>
            <Form.Container size="small">
                <Form.Title noFormHead={props.noFormHead}>{accountTranslation("login")}</Form.Title>
                <Form.Body onEnter={onLogin} rowSpacing={2} columnSpacing={2}>
                    <Grid container item>
                        <TextInput
                            name="email"
                            label={accountTranslation("email")}
                            inputType="email"
                            error={invalidEmail}
                            helperText={invalidEmail ? validationTranslation("email.errorMessage.format") : ""}
                            value={email}
                            onChange={onChangeEmail}
                        />
                    </Grid>

                    <Grid container item>
                        <TextInput
                            name="password"
                            label={accountTranslation("password")}
                            value={password}
                            error={invalidPassword}
                            helperText={invalidPassword ? validationTranslation("password.errorMessage.format") : ""}
                            type="password"
                            onChange={onChangePassword}
                        />
                    </Grid>
                    <Grid container item justifyContent="center">
                        <TextButton
                            disabled={disabledSubmit}
                            label={modalTranslation("buttons.submit")}
                            onClick={onLogin}
                        />
                    </Grid>

                    <Grid container item justifyContent="center">
                        <TextButton
                            label={accountTranslation("forgotPassword")}
                            size="small"
                            variant="text"
                            onClick={showForgotPasswordModal}
                        />
                        <ModalForgotPasswordWindow
                            visible={forgotPasswordModalVisible}
                            onClose={hideForgotPasswordModal}
                        />
                    </Grid>
                </Form.Body>
            </Form.Container>
        </>
    );
};

export default LoginForm;
