export default class InputValidation {
  static PasswordRegex = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z\d!@#$%^&-=_+"',/:;?`~|<>(){}[\]\*\.\\]{8,}$/;
  static EmailRegex = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  static NameRegex = /^\w{4,16}$/;
  static AddressRegex = /^[A-Za-z0-9'\.\-\s\,]{1,}$/;

  static HiraganaRegex = /[\u3041-\u3096\u309D-\u309F]/;
  static KatakanaRegex = /[\u30A0-\u30FF]/;

  static ChineseRegex = /[\u4e00-\u9fa5]/;
  static EnglishRegex = /[a-zA-Z]/;
  static NumberRegex = /[0-9]/;

  static isNumber(string: string): boolean {
    return !isNaN(+string);
  }

  static validate(regex: RegExp, input: string) {
    return regex.test(input);
  }

  static validateEmail(email: string): boolean {
    return InputValidation.validate(InputValidation.EmailRegex, email);
  }

  static validatePassword(password: string): boolean {
    return InputValidation.validate(InputValidation.PasswordRegex, password);
  }
  static validateName(name: string): boolean {
    return InputValidation.validate(InputValidation.NameRegex, name);
  }

  static validateNickName(name: string): boolean {
    const minLen: number = 3;
    const maxLen: number = 16;

    if (name.length > maxLen) return false; // each Chinese or Japanese character counted as 2 length
    if (name.startsWith(" ") || name.endsWith(" ")) return false;

    let currentLen = 0;
    for (let i = 0; i < name.length; i++) {
      const char = name[i];
      if (
        InputValidation.validate(InputValidation.EnglishRegex, char) ||
        InputValidation.validate(InputValidation.NumberRegex, char) ||
        InputValidation.validate(/[\s]/, char)
      ) {
        currentLen++;
      } else if (
        InputValidation.validate(InputValidation.ChineseRegex, char) ||
        InputValidation.validate(InputValidation.HiraganaRegex, char) ||
        InputValidation.validate(InputValidation.KatakanaRegex, char)
      ) {
        currentLen = currentLen + 2;
      } else {
        return false;
      }
    }
    const pass = currentLen >= minLen && currentLen <= maxLen;
    return pass;
  }

  static validateAddress(address: string): boolean {
    return InputValidation.validate(InputValidation.AddressRegex, address);
  }

  static validatePhoneNumber(phone: string, count: number): boolean {
    const re = new RegExp("^[0-9]{" + count.toString() + "}$");
    return InputValidation.validate(re, phone);
  }
}