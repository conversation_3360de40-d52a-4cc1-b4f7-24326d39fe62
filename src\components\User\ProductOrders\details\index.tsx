import Maths from "@/../lib/maths";
import { ChipLabel } from "@/components/_CommonElements/Display";
import styles from "@/components/User/TicketOrders/orders.module.scss";
import RouteMap from "@/constants/config/RouteMap";
import { EnumDiscountCodeType } from "@/constants/enum/DiscountCodeType";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import UtilityHooks from "@/hooks/Utility";
import { UserProductOrderItem, UserProductOrder } from "@/models/api/models/UserProductOrder";
import i18n from "@/utils/i18n";
import { generateMarketOrderUrl } from "@/utils/marketUrl";
import { ExpandLess, ExpandMore } from "@mui/icons-material";
import { Box, Button, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import Collapse from "@mui/material/Collapse";
import ListItem from '@mui/material/ListItem';
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useMemo, useState } from "react";

interface OrderDetailsProps{
  order: UserProductOrder;
  listType?: string;
}

const OrderDetails = (props: OrderDetailsProps) => {
  const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
  const {
    order,
    listType,
  } = props;
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const handleClick = () => {
    setOpen(!open);
  };

  const { localTime } = UtilityHooks.useBrowserLocalTime(order.createdDateTime);

  const paymentOrderId = useMemo(() => order.paymentOrderId, [order.paymentOrderId]);
  const internalPaymentOrderState = useMemo(() => order.paymentInfo ? order.paymentInfo.internalPaymentOrderState : listType ?? '', [listType, order.paymentInfo]);
  const currency = useMemo(() => order.currency, [order.currency]);
  const isDiscounted = useMemo(() => {
    if (!order.paymentInfo) return false;
    if (!order.paymentInfo?.isDiscounted) return false;
    return true;
  }, [order.paymentInfo]);
  const discountValueAndType = useMemo(() => {
    if (!order.paymentInfo) return undefined;
    if (!order?.paymentInfo.isDiscounted) return undefined;
    let _result = "";
    const _discountValueType: EnumDiscountCodeType = order.paymentInfo.discountValueType as EnumDiscountCodeType;
    const _discountValue: number = Number(order.paymentInfo.discountValue);
    switch (_discountValueType) {
        case EnumDiscountCodeType.FIXED:
            _result = i18n.GetCurrency(order.paymentInfo.currency , _discountValue, router.locale);
            break;
        case EnumDiscountCodeType.PERCENTAGE:
            _result = _discountValue * 100 + "%";
            break;
        default:
            _result = _discountValue.toString();
    }
    return " -" + _result;
  }, [order.paymentInfo, router.locale]);
  const orderTotalFee = useMemo(() => {
    if (order.paymentInfo) {
      return order.paymentInfo.isDiscounted ? order.paymentInfo.orderTotalFee : order.paymentInfo.orderOriginalFee;
    }
    return order.orderTotalFee;
  }, [order.orderTotalFee, order.paymentInfo]);

  return (
    <ListItem alignItems="flex-start" component="div" className={styles.itemContainer}>
      <ListItemButton className={styles.itemButtonFullWidth} onClick={handleClick}>
        <ListItemText 
          primary={`${profileTranslation("order.detail.orderNo", { orderId: paymentOrderId })} — ${ profileTranslation(`order.${internalPaymentOrderState.toLowerCase()}`)}`}
          secondary={profileTranslation("order.detail.date", { date: localTime })}
          primaryTypographyProps={{
            width: '100%',
          }}
        />
        {open ? <ExpandLess /> : <ExpandMore />}
      </ListItemButton>
      <Collapse in={open} timeout="auto" unmountOnExit sx={{ width: "100%" }}>
        <TableContainer component={Paper} sx={{ wordBreak: "keep-all" }}>
          <Table sx={{ width: "100%" }} aria-label="order table">
            <TableHead>
              <TableRow>
                <TableCell>{profileTranslation("order.detail.item.name")}</TableCell>
                <TableCell align="right">{profileTranslation("order.detail.item.quantity")}</TableCell>
                <TableCell align="right">{profileTranslation("order.detail.item.unitPrice")}</TableCell>
                <TableCell align="right">{profileTranslation("order.detail.item.subtotal")}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {order.itemList.map((item: UserProductOrderItem) => {
                if (item.itemType) {
                  return (
                    <TableRow key={`order-${paymentOrderId}-${item.itemName}`}>
                      <TableCell sx={{maxWidth: 250}}>
                        <Stack direction="row" sx={{ alignItems: "center" }}>
                          <ChipLabel className={styles.itemChipLabel}>{profileTranslation("order.detail.label.product")}</ChipLabel>
                          <span>{item.itemName}</span>
                        </Stack>
                      </TableCell>
                      <TableCell align="right">{item.quantity}</TableCell>
                      <TableCell align="right">
                          { i18n.GetCurrency(currency, Maths.truncDigits(item.pricePerUnit, 1), router.locale) }
                      </TableCell>
                      <TableCell align="right">
                          { i18n.GetCurrency(currency, Maths.truncDigits(item.pricePerUnit * item.quantity, 1), router.locale) }
                      </TableCell>
                    </TableRow>
                  )
                }
                return (
                  <TableRow key={`order-${paymentOrderId}-${item.eventBundleId}`}>
                    <TableCell sx={{maxWidth: 250}}>
                      <div><b><a href={`${RouteMap.Event}/${item.eventId}`}>{item.eventName}</a></b></div>
                      <br />
                      <Stack direction="row" sx={{ alignItems: "center" }}>
                        <ChipLabel className={styles.itemChipLabel}>{profileTranslation("order.detail.label.ticket")}</ChipLabel>
                        <span>{item.eventBundleName}</span>
                      </Stack>
                    </TableCell>
                    <TableCell align="right">{item.quantity == null ? 1 : item.quantity}</TableCell>
                    <TableCell align="right">
                        { i18n.GetCurrency(currency, Maths.truncDigits(item.pricePerUnit, 1), router.locale) }
                    </TableCell>
                    <TableCell align="right">
                        { i18n.GetCurrency(currency, Maths.truncDigits(item.pricePerUnit * (item.quantity == null ? 1 : item.quantity), 1), router.locale) }
                    </TableCell>
                  </TableRow>
                )
              })}
              {isDiscounted ? 
                <TableRow>
                  <TableCell colSpan={3} align="right">{profileTranslation("order.detail.item.discount")}</TableCell>
                  <TableCell align="right" className={styles.grandTotal}>
                      {/* <span className={styles.grandTotalValue}>{ i18n.GetCurrency(order.paymentInfo.currency, Maths.truncDigits(order.paymentInfo.discountCode, 1), router.locale) }</span> */}
                      <span className={styles.discountCaption}>{discountValueAndType}</span>
                  </TableCell>
                </TableRow> : null
              }              
              <TableRow>
                <TableCell colSpan={3} align="right">{profileTranslation("order.detail.item.grandTotal")}</TableCell>
                <TableCell align="right" className={styles.grandTotal}>
                    <span className={styles.grandTotalValue}>{ i18n.GetCurrency(currency, Maths.truncDigits(orderTotalFee, 1), router.locale) }</span>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, p: 2 }}>
          <Button
            variant="contained"
            onClick={() => {
              const marketOrderUrl = generateMarketOrderUrl(paymentOrderId, router.locale);
              window.open(marketOrderUrl, '_blank');
            }}
            sx={{
              backgroundColor: '#ff7802',
              color: 'white',
              '&:hover': {
                backgroundColor: '#e66a02',
              },
            }}
          >
            {profileTranslation("order.detail.viewDetails")}
          </Button>
        </Box>
      </Collapse>
  </ListItem>);
};

export default OrderDetails;