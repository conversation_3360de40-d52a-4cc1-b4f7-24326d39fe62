import { useState, useEffect } from 'react'
// import axios from 'axios'

export interface ipData {
    country_code: string;
    country_name: string;
    city: string;
    postal: string;
    latitude: string;
    longitude: string;
    IPv4: string;
    state: string;
}
export const getIpData = async () => {
    const res: string = await fetch('https://www.cloudflare.com/cdn-cgi/trace')
        .then(r => r.text())
    const ipD: { ip: string } = { ip: '' };
    const resArray = res.split('\r\n');
    for (let i = 0; i < resArray.length; i++) {
        const itemKV = resArray[i];
        const item = itemKV.split('=');
        if (item.length == 2 && item[0].toLowerCase() == 'ip') {
            ipD.ip = item[1];
        }
        // for (let o = 0; o < item.length; o++) {

        // }
        if (ipD.ip !== '')
            break;
    }

    return ipD;
}

const GetIp = () => {
    const [ip, setIP] = useState('');
    useEffect(() => {
        void (async () => {
            const asyncTask = async () => {
                return await getIpData();
            }
            const res = await asyncTask();
            setIP(res.ip);
        })();
    }, []);

    return (<div><h1>ip:{ip}</h1></div>)
}
export default GetIp;
