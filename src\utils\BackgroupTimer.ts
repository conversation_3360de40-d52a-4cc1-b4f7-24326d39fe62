export const getNextTargetTime=(targetMinute:number)=>{
    let now = new Date();
    const second = 1000;
    const minute = 60 * second;  
    const targetDT = new Date(now.getTime() + minute * targetMinute);
    // const targetDT = new Date(now.getTime() + 5*second);
    return targetDT;
  }

  export const getNextTargetTimeSecond=(targetSecond:number)=>{
    let now = new Date();
    const second = 1000;
    const targetDT = new Date(now.getTime() + second * targetSecond);
    // const targetDT = new Date(now.getTime() + 5*second);
    return targetDT;
  }
  
  const backgroundTimer = async(targetDT:Date,callback:Function)=>{
    let currentDT = new Date();
    const delay =(time:number)=> {
      return new Promise(resolve => setTimeout(resolve, time));
    }
    while(currentDT.getTime()<targetDT.getTime()){
      await delay(1000);
      currentDT = new Date();
    }
    callback();
    return;
  }

  export default backgroundTimer;