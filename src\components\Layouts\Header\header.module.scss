@use "@/styles/utils/viewport.module.scss" as viewport;

.contentbox {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    gap: 10px;
    @include viewport.within("tablet") {
        display: none;
    }
}
.header {
    color: black;
    background-color: white;
    min-height: auto !important;
    height: auto !important;
    box-shadow: none !important;
    z-index: 10000 !important;
    border-bottom: 1px solid hsla(0,0%,84%,.5);
    & .right {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: 20px;
    }
}
.bar {
    padding-right: 0 !important;
}