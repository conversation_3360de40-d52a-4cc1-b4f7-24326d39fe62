import { ShoppingCartSummarizedItem } from "../result/user/shoppingCart";

interface EventSessionBundlesByDateSessionGroupSalesDetails extends ShoppingCartSummarizedItem {
    forSell: boolean;
    saleStartDateTime: number;
    saleEndDateTime: number;
    soldOut: boolean;
}

interface EventSessionBundlesByDateSessionGroup {
    sessionStartDateTime: number;
    sessionEndDateTime: number;
    items: EventSessionBundlesByDateSessionGroupSalesDetails[];
}

export default EventSessionBundlesByDateSessionGroup;