import Pagination from "@/components/_CommonElements/Pagination";
import OrderDetails from "@/components/User/ProductOrders/details";
import styles from "@/components/User/TicketOrders/orders.module.scss";
import { OrderType } from '@/constants/enum/OrderType';
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { UserProductOrder } from "@/models/api/models/UserProductOrder";
import { Alert } from "@mui/material";
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import { useTranslation } from "next-i18next";

interface OrdersProps {
  userProductOrder: UserProductOrder[];
  listType: string;  
  pageNum: number;
  maxPageNum: number;
  setPageNum: (pageNum: number) => void;
}

const ProductOrders = (props: OrdersProps) => {
    const { t: commonTranslation } = useTranslation(EnumTranslationJson.Common);
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
    const {
        userProductOrder,
        listType,
        pageNum,
        maxPageNum,
        setPageNum,
    } = props;

  let emptyMessage: string;

  switch (listType) {
    case OrderType.COMPLETED:
      emptyMessage = commonTranslation("noRecord");
      break;
    case OrderType.PROCESSING:
      emptyMessage = commonTranslation("noRecord");
      break;
    case OrderType.PAID:
      emptyMessage = commonTranslation("noRecord");
      break;
    case OrderType.PENDING:
      emptyMessage = commonTranslation("noRecord");
      break;
    case OrderType.EXPIRED:
    emptyMessage = commonTranslation("noRecord");
    break;
    default:
      return null;
  }

  return (
  <>
    <Alert severity="info">{profileTranslation("order.reminder")}</Alert>
    <List className={styles.list} sx={{ width: '100%', minWidth: 360, bgcolor: 'background.paper' }} >

      {!userProductOrder.length && 
        <ListItem className={styles.emptyRecord}>
          <h2>{emptyMessage}</h2>
        </ListItem>
      }

      {OrderDetails && userProductOrder.map((each, index:number) => {
        return <OrderDetails key={index} order={each} listType={listType} />
      })}
    </List>
    {maxPageNum > 1 &&
      <div className={styles.pagination}>
        <Pagination currentPage={pageNum} maxPage={maxPageNum} onClick={setPageNum}/>
      </div>
    }
  </>);
};

export default ProductOrders;
