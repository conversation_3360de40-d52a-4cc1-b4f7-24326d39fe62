import Label from "@/components/_CommonElements/Label";
import classNames from "classnames";
import React from "react";
import { ReactNode } from "react";
import styles from "./eventMetadata.module.scss";

interface EventMetadataProps {
    content: ReactNode;
    title?: string;
    icon?: ReactNode;
    classes?: {
        title?: string;
        content?: string;
    }
}
const EventMetadata = React.memo((props: EventMetadataProps) => {
    const { title, icon, content, classes = {} } = props;
    return (
        <>
            <div className={styles.metadata}>
                {title ? <Label className={classes.title} title={title} /> : null}
                {icon ? icon : null}
                <div className={classNames(
                    styles.content,
                    {[classes.content!]: classes.content !== undefined })}>
                    {content}
                </div>
            </div>
        </>
    );
});
EventMetadata.displayName = "EventMetadata";
export default EventMetadata;