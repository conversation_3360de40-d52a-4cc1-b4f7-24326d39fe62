import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import EventBundle from '@/models/api/models/EventBundle';
import styles from "./rateCard.module.scss";
import { useTranslation } from 'next-i18next';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import i18n from '@/utils/i18n';
import { useRouter } from 'next/router';

interface RateCardProps {
    dataSource: EventBundle[];
}
const RateCard = ({dataSource}: RateCardProps) => {
    const router = useRouter();
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);
    return (
        <TableContainer component={Paper}>
            <Table aria-label="Rate Card">
                <TableHead>
                    <TableRow>
                        <TableCell>{eventTranslation("detail.packageLabel")}</TableCell>
                        <TableCell>{eventTranslation("detail.priceLabel")}</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {
                        dataSource.map(each => (
                            <TableRow key={each.eventBundleId}>
                                <TableCell className={styles.name}>
                                    {each.eventBundleName}
                                </TableCell>
                                <TableCell className={styles.price}>
                                    <span>
                                        {i18n.GetCurrency(each.currency, each.price, router.locale)}
                                    </span>
                                </TableCell>
                            </TableRow>
                        ))
                    }
                </TableBody>
            </Table>
        </TableContainer>
    );
};
export default RateCard;