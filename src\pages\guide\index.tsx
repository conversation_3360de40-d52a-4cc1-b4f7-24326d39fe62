import type { GetServerSideProps, NextPage } from 'next';
import Guide from '@/components/Guide';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import i18n from '@/utils/i18n';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import PageContent from '@/components/_PageComponents/PageContent';

const GuidePage: NextPage = () => {
  const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
  const title = useMemo(() => seoTranslation("page.guide.title"), [seoTranslation]);
  return (
    <PageContent
        title={title}
        content={<Guide />}
    />
  );
};
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [EnumTranslationJson.Guide],
    context
});
export default GuidePage;