import { dispatch, useSelector } from "@/redux/store";
import { Badge } from "@mui/material";
import Mu<PERSON><PERSON><PERSON>on from "@mui/material/Button";
import { useCallback, useEffect, useMemo, useState } from "react";
import Router, { useRouter }  from "next/router";
import { deleteCookie } from "cookies-next";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import ShoppingCartLayout from "@/components/Event/ShoppingCart/ShoppingCartMenu/Layout";
import { useSnackbar } from "notistack";
import { BACKEND } from "@/constants/config";
import ENDPOINT from "@/models/api/endpoint";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import { clearCart, selectCheckoutItemsByIdSelector } from "@/redux/slices/cartSlice";
import AuthenicationHooks from "@/hooks/Authentication";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import API from "@/utils/API";
import { ModalActions } from "@/redux/slices/uiSlice";
import CreateOrderAPIResult from "@/models/api/result/order/create";
import styles from "../button.module.scss";
import ShoppingCartMenu from "@/components/Event/ShoppingCart/ShoppingCartMenu";
import classNames from "classnames";
import { useShoppingCart } from "@/hooks/Order/ShoppingCart";
import RouteMap from "@/constants/config/RouteMap";

const useCheckout = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION, EnumRequestHeader.LANGUAGE] });
    const checkoutAsync = async ({ inCartItemEventSessionBundleIdList, userComment, paymentMethod }: 
        { 
            inCartItemEventSessionBundleIdList: string[], 
            userComment: string,
            paymentMethod: { id: string, gateway: string } 
        } ) => {
        const formData = new FormData();
        formData.append("PaymentGatewayId", paymentMethod.gateway);
        formData.append("PaymentType", paymentMethod.id);
        for (let i = 0; i < inCartItemEventSessionBundleIdList.length; i++) {
            const eventSessionBundleId = inCartItemEventSessionBundleIdList[i];
            if (eventSessionBundleId) {
                formData.append(`Items[${i}].eventSessionBundleId`, eventSessionBundleId);
            }
        }
        formData.append("UserComment", userComment);
        return await requestAsync<CreateOrderAPIResult>(
            ENDPOINT.OrderPayment(),
            {
                method: RequestMethod.POST,
                data: formData
            }
        );
    };
    return { checkoutAsync };
};

const ShoppingCartFab = () => {
    const router = useRouter();
    const { enqueueSnackbar } = useSnackbar();
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { isAuthorized } = AuthenicationHooks.useUserInfo();
    const { deleteAllAsync } = useShoppingCart();
    const { checkoutAsync } = useCheckout();

    const [checkoutVisible, setCheckoutVisible] = useState<boolean>(false);
    const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
    const [ isCreating, setIsCreating ] = useState(false);
    const [ isDeleting, setIsDeleting ] = useState(false);

    const cartItems = useSelector(state => state.cart.items);
    const eventName = useSelector(state => state.cart.eventName);
    const inCartItemEventSessionBundleIdList = useSelector(selectCheckoutItemsByIdSelector);
    
    const isLoading = useMemo(() => isCreating || isDeleting, [isCreating, isDeleting]);
    const numOfItemsInCart = useMemo(() => cartItems.reduce((numInCart: number, item) => numInCart += item.quantity, 0), [cartItems]);
    const eventCurrency = useMemo(() => (cartItems.length > 0) ? cartItems[0].currency : undefined, [cartItems]);
    const sortAndGroupBySessionDateTime = useMemo(() => {
        const groupBySessionDateTime = {};
        cartItems.reduce((group: { [char: string]: (typeof item)[] }, item) => {
            const groupName = `s_${item.sessionStartDateTime}_e_${item.sessionEndDateTime}`;
            if (!Object.keys(group).includes(groupName)) {
                group[groupName] = [];
            }
            group[groupName].push(item);
            return group;
        }, groupBySessionDateTime);
        const sortedGroupBySessionDateTime: { [char: string]: (typeof cartItems) } = (Object.keys(groupBySessionDateTime) as Array<keyof typeof groupBySessionDateTime>)
            .sort()
            .reduce((r, k) => ({ ...r, [k]: groupBySessionDateTime[k] }), {});
        return sortedGroupBySessionDateTime;
    }, [cartItems]);

    const openViewCheckOut = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(e.currentTarget);
        setCheckoutVisible(true);
    }, []);
    const closeViewCheckout = useCallback(() => {
        setCheckoutVisible(false);
        setAnchorEl(null);
    }, []);
    const onClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
        // if (!checkoutVisible) {
        //     openViewCheckOut(e);
        // }
        // else {
        //     closeViewCheckout();
        // }
        void router.push(RouteMap.Checkout);
    }, [checkoutVisible, closeViewCheckout, openViewCheckOut]);

    useEffect(() => {
        if (cartItems.length === 0 && checkoutVisible) {
            closeViewCheckout();   
        }
    }, [cartItems.length, checkoutVisible, closeViewCheckout]);

    const clearCartItems = useCallback(async () => {
        setIsDeleting(true);
        try {
            await deleteAllAsync();
            dispatch(clearCart());
        } catch (error) {
            const errorMessage = API.GetErrorMessage(error);
            enqueueSnackbar(errorMessage, { variant: "error" });
        }
        setIsDeleting(false);
    }, [deleteAllAsync, enqueueSnackbar]);
    const finishCheckoutAndclearCartItems = useCallback(() => {
        setIsDeleting(true);
        try {
            dispatch(clearCart());
        } catch (error) {
            const errorMessage = API.GetErrorMessage(error);
            void enqueueSnackbar(errorMessage, { variant: "error" });
        }
        setIsDeleting(false);
    }, [enqueueSnackbar]);

    const submitOrder = useCallback(async (
        isAcceptedAgreement: boolean, 
        userComment: string, 
        paymentMethod: { id: string, gateway: string }) => {
        try {
            setIsCreating(true);
            if (!isAcceptedAgreement) {
                enqueueSnackbar(snackbarTranslation("messages.purchase.error.acceptAgreement"), { variant: "error" })
                throw new Error("IsAcceptedAgreement: False");
            }
            if (!isAuthorized) {
                dispatch(ModalActions.openLoginModal());
                throw new Error("IS_AUTHORIZED: False");
            }
            if (!cartItems) {
                enqueueSnackbar(snackbarTranslation("messages.purchase.error.unknowItem"), { variant: "error" })
                await clearCartItems();
                throw new Error("EMPTY_CART_ITEMS");
            }
            try {
                const res = (await checkoutAsync({inCartItemEventSessionBundleIdList, userComment, paymentMethod})).data!;
                void finishCheckoutAndclearCartItems();
                deleteCookie(EnumCookieKey.TRACED_REFERRAL_CODE);
                if (res?.paymentUrl) {
                    void Router.push(res.paymentUrl);
                }
            }
            catch (error) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
                throw error;
            }
        } catch {
            setIsCreating(false);
        }
    }, [isAuthorized, cartItems, enqueueSnackbar, snackbarTranslation, clearCartItems, checkoutAsync, inCartItemEventSessionBundleIdList, finishCheckoutAndclearCartItems]);

    return (numOfItemsInCart > 0 ?
        <>
            <MuiButton
                aria-label="cart"
                className={classNames(styles.button, styles.contained)}
                onClick={onClick}
            >
                {numOfItemsInCart ?
                    <Badge 
                        badgeContent={numOfItemsInCart} 
                        color="secondary"
                        sx={{
                            '& .MuiBadge-badge': {
                                top: "-0.5rem",
                                right: "-0.5rem",
                                backgroundColor: "#4db848"
                            }
                        }}
                    >
                        <ShoppingCartIcon />
                    </Badge>
                    :
                    <ShoppingCartIcon />
                }
            </MuiButton>
            <ShoppingCartLayout
                    eventName={eventName}
                    eventCurrency={eventCurrency}
                    cartDataSource={sortAndGroupBySessionDateTime}
                    anchorEl={anchorEl}
                    open={checkoutVisible}
                    onOpen={openViewCheckOut}
                    onClose={closeViewCheckout}
                    submitOrder={submitOrder}
                    isLoading={isLoading}
            />
        </> :
        <ShoppingCartMenu />
    );
}
export default ShoppingCartFab;