import Modal from "@/components/_CommonElements/Modal";
import styles from "./ticketRedemptionConfirmation.module.scss";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import ENDPOINT from "@/models/api/endpoint";
import RedeemTicketAPIResult from "@/models/api/result/user/redeem";
import Markdown from "markdown-to-jsx";
import { BACKEND } from "@/constants/config";
import { TextInput } from "@/components/_CommonElements/Input";
import dynamic from "next/dynamic";

const SuccessDisplay = dynamic(() => import("./SuccessDisplay"));
const FailureDisplay = dynamic(() => import("./FailureDisplay"));
interface Props {
    visible: boolean;
    ticketToken: string;
    onSuccess?: () => void;
    onCancel: () => void;
}

const useRequestRedeem = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async (code: string) => {
        const formData = new FormData();
        formData.append("redeemCode", code);

        return await fetchAsync<RedeemTicketAPIResult>(
            ENDPOINT.redeemTicket(),
            {
                method: "POST",
                data: formData
            }
        );
    };
    return requestAsync;
};
const TicketRedemptionConfirmation = (props: Props) => {
    const {
        visible,
        ticketToken,
        onSuccess,
        onCancel
    } = props;

    const [ success, setSuccess ] = useState<boolean>(false);
    const [ failed, setFailed ] = useState<boolean>(false);
    const { t: redeemTranslation } = useTranslation(EnumTranslationJson.Redeem);
    useEffect(() => {
        // from invisible to visible
        if (visible) {
            setFailed(false);
            setSuccess(false);
        }
    }, [visible]);
    const requestRedeemAsync = useRequestRedeem();
    const onConfirm = useCallback(async () => {
        try {
            const redeemResponse = await requestRedeemAsync(ticketToken);
            if (!redeemResponse.result) throw redeemResponse;
            if (onSuccess) {
                onSuccess();
            }
            setSuccess(true);
        } catch {
            setFailed(true);
        }
    }, [ticketToken, onSuccess, requestRedeemAsync]);
    
    const completed = useMemo(() => success || failed, [success, failed]);
    return (
        <Modal
            visible={visible}
            onConfirm={completed ? onCancel : onConfirm}
            onCancel={onCancel}
            hideCancelButton
            title={redeemTranslation("modal.confirm")}>
            
            { !completed ?
                <div className={styles.form}>
                    <TextInput
                        label={redeemTranslation("form.label")}
                        value={ticketToken}
                        disabled
                    />
                    <div className={styles.confirmClaim}>
                        <Markdown
                            options={{
                                wrapper: Fragment,
                                forceWrapper: false
                            }}
                        >
                            {redeemTranslation("modal.claim")}   
                        </Markdown>
                    </div>
                </div> :
                <>
                    { success && <SuccessDisplay />}
                    { failed && <FailureDisplay /> }
                </>
            }
        </Modal>
    );
};

export default TicketRedemptionConfirmation;