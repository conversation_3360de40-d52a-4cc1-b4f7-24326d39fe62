import styles from "@/components/User/EventList/eventList.module.scss";

import { EventListType } from "@/constants/enum/EventListType";

import Pagination from "@/components/_CommonElements/Pagination";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import TicketWithEventDetail from "@/models/api/models/TicketWithEventDetail";
import dynamic from "next/dynamic";
import { useMemo } from "react";
import LoadingButton from "@/components/_CommonElements/Button/LoadingButton";

const PurchasedEvent = dynamic(() => import("@/components/User/EventList/PurchasedEvent"), { loading: () => <LoadingButton /> });
const RedeemedEvent = dynamic(() => import("@/components/User/EventList/RedeemedEvent"), { loading: () => <LoadingButton /> });
const UpcomingEvent = dynamic(() => import("@/components/User/EventList/UpcomingEvent"), { loading: () => <LoadingButton /> });

interface EventListProps {
    userEvents: TicketWithEventDetail[];
    listType: EventListType;
    pageNum: number;
    maxPageNum: number;
    setPageNum: (pageNum: number) => void;
}

const EventList = (props: EventListProps) => {
    const { userEvents, listType, pageNum, maxPageNum, setPageNum } = props;
    const { t: commonTranslation } = useTranslation(EnumTranslationJson.Common);

    const EventItem = useMemo(() => {
        switch (listType) {
            case EventListType.PURCHASED:
                return PurchasedEvent;
            case EventListType.REDEEMED:
                return RedeemedEvent;
            case EventListType.UPCOMING:
                return UpcomingEvent;
        }
        return null;
    }, [listType]);
    return (
        <div className={styles.list}>
            {!userEvents.length && (
                <div className={styles.emptyRecord}>
                    <h2>{commonTranslation("noRecord")}</h2>
                </div>
            )}

            {EventItem &&
                userEvents.map((item, index) => {
                    return <EventItem key={index} event={item} />;
                })}

            {maxPageNum > 1 && (
                <div className={styles.paginationContainer}>
                    <Pagination
                        currentPage={pageNum}
                        maxPage={maxPageNum}
                        onClick={setPageNum}
                    />
                </div>
            )}
        </div>
    );
};

export default EventList;
