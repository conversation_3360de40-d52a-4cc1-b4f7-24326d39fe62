import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import Section from "@/components/_CommonElements/Section";

const Terms = () => {
    const { t: termsTranslation } = useTranslation(EnumTranslationJson.Terms);
    return (
        <Section content={termsTranslation("sections.termsAndCondition.content", {
            UPDATE_DATE: "2024/05/20",
            PLATFORM_NAME: "INCUTix",
            COMPANY_NAME: "INCUTix Limited",
            PLATFORM_DOMAIN: "incutix.com",
            EMAIL_URL: "<EMAIL>",
            GUIDE_URL: "https://incutix.com/guide",
            FAQ_URL: "https://incutix.com/faq"
        })} />
    );
}

export default Terms;