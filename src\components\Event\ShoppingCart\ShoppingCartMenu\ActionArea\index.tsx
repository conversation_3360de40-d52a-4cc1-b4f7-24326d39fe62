import React, { useCallback, ReactNode } from "react";
import { useTranslation } from "next-i18next";
import { Box, Button, ButtonGroup, Divider } from "@mui/material";
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';

import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import shoppingCartMenuStyles from "@/components/Event/ShoppingCart/ShoppingCartMenu/shoppingCartMenu.module.scss";
import { dispatch } from "@/redux/store";
import { openClearShoppingCartConfirmDialog } from "@/redux/slices/cartSlice";

export type ActionComponent = {
    label: string;
    onClick: () => void;
    icon?: ReactNode;
    disableCheckingOn?: () => boolean;
};

interface ActionAreaProps {
    agreementComponent?: ReactNode;
    actions: ActionComponent[];
}

const ActionArea = (props: ActionAreaProps) => {
    const {
        agreementComponent,
        actions
    } = props;

    const { t: shoppingCartTranslation } = useTranslation(EnumTranslationJson.ShoppingCart);

    const requestClearShoppingCart = useCallback(() => {
        dispatch(openClearShoppingCartConfirmDialog({ titleI18nKey: "modals.clearShoppingCart_userrequest.title", contentI18nKey: "modals.clearShoppingCart_userrequest.content"}));
    }, []);
    
    return (
        <Box sx={{ position: 'relative' }}>
            <Divider />
            <div className={shoppingCartMenuStyles.confirmClaim}>
                {agreementComponent}
            </div>
            <Divider />
            <ButtonGroup 
                variant="text"
                size={'large'}
                sx={{
                    right: 0
                }}
                fullWidth
            >
                <Button
                    color="error"
                    sx={{ fontSize: "1.2rem" }}
                    startIcon={<DeleteForeverIcon />}
                    onClick={requestClearShoppingCart}>
                    {shoppingCartTranslation('popover.button.clearAll')}
                </Button>
                {actions?.map((action, index) =>
                    <Button 
                        key={`checkout-action-button-${action.label}`}
                        sx={{ fontSize: "1.2rem" }} 
                        {...(index >= actions.length-1 ? { endIcon: action.icon } :  { startIcon: action.icon })}
                        disabled={action?.disableCheckingOn ? !action?.disableCheckingOn() : false}
                        onClick={action.onClick}>
                        {shoppingCartTranslation(action.label)}
                    </Button>  
                )}
            </ButtonGroup>
        </Box>
    );
};

ActionArea.displayName = "ShoppingCartLayoutActionArea";
export default ActionArea;