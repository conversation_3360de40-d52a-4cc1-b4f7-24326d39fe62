import { GetServerSideProps, GetServerSidePropsContext } from "next";

import ENDPOINT from "@/models/api/endpoint";
import RouteMap from "@/constants/config/RouteMap";

import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import { convertToQueryString } from "@/utils/QueryString";
import API from "../API";
import { GenericAPI } from "@stoneleigh/api-lib";
import UserInfoAPIResult from "@/models/api/result/user/info";

interface serverSideAuthOptions {
  redirectTo?: string;
  permission?: "userOnly" | "guestOnly" | "all";
}

interface authStatus {
  isLoggedIn: boolean;
  emailVerified: boolean;
  walletConnected: boolean;
}

type CustomGetServerSideProps =
  GetServerSideProps extends (...params: any[]) => infer R
  ? (...params: [...params: Parameters<GetServerSideProps>, auth: authStatus]) => R
  : never;


const getRedirectUrl = (permission: string, redirectTo: string | undefined, queryString: string) => {
    switch (permission) {
        case "guestOnly":
          return redirectTo ? `${redirectTo}/${queryString}` : `${RouteMap.UserProfile}/${queryString}`;
        case "userOnly":
          return redirectTo ? `${redirectTo}/${queryString}` : RouteMap.Login;
        default:
          return "/";
      }
}
const serverSideAuth = (options: serverSideAuthOptions, getServerSideProps: CustomGetServerSideProps) => {
  return async (context: GetServerSidePropsContext) => {
    const { req, res, query, locale, resolvedUrl } = context;
    const { permission = "all", redirectTo } = options || {};
    const queryString = convertToQueryString(query);
    
    const userJWT = API.GetServerSideCookies(context)[EnumCookieKey.USER_JWT];
    const auth = { isLoggedIn: false, emailVerified: false, walletConnected: false };

    const redirectUrl = getRedirectUrl(permission, redirectTo, queryString);

    if (userJWT) {
      try {
        const response = await GenericAPI.requestAsync<UserInfoAPIResult>(
          ENDPOINT.GetUserInfo(), {
          method: "GET",
          headers: {
            [EnumRequestHeader.AUTHORIZATION as string]: userJWT,
          },
        });
        auth.isLoggedIn = true;
        auth.emailVerified = response.data!.isVerified;
      } catch (error: any) {
      }
    }

    const loginRequired = permission === "userOnly" && !auth.isLoggedIn;
    const logoutRequired = permission === "guestOnly" && auth.isLoggedIn;

    if (loginRequired || logoutRequired) {
      return {
        props: {},
        redirect: {
          permanent: false,
          destination: redirectUrl,
        },
      };
    }

    return await getServerSideProps(context, auth);
  };
};

export default serverSideAuth;