import type { GetServerSideProps, NextPage } from 'next';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import i18n from '@/utils/i18n';
import ForgotPassword from '@/components/User/ForgotPassword';
import { useRouter } from "next/router";
import { useTranslation } from 'next-i18next';
import RouteMap from '@/constants/config/RouteMap';
import { useEffect, useMemo } from 'react';
import PageContent from '@/components/_PageComponents/PageContent';

const FAQPage: NextPage = () => {
    const router = useRouter();
    const param = router.query
    const { t: accountTranslation } = useTranslation(EnumTranslationJson.Account);

    useEffect(() => {
        if (!param.hasOwnProperty("code")) {
            void router.push(RouteMap.Main);
        }
    }, [param, router]);
    const title = useMemo(() => accountTranslation("forgotpasswordText.changePassword"), [accountTranslation]);
    return (
        <PageContent
            title={title}
            content={<ForgotPassword token={param.code?.toString() || ""} email={param.email?.toString() || ""} />}
        />
    );
};
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [
        EnumTranslationJson.Account, EnumTranslationJson.Validation
    ],
    context
});
export default FAQPage;