import { useState } from "react";
import { useEventListener } from "usehooks-ts";

const useFlutterInAppWebViewPlatformReadyState = () => {
    const [isFlutterInAppWebViewPlatformReady, setIsFlutterInAppWebViewPlatformReady] = useState(false);

    const useAddListener = () => useEventListener("flutterInAppWebViewPlatformReady", () => setIsFlutterInAppWebViewPlatformReady(true));

    const getFlutterInAppWebViewPlatformReadyState = () => isFlutterInAppWebViewPlatformReady;

    return { useAddListener, getFlutterInAppWebViewPlatformReadyState };
};

export { useFlutterInAppWebViewPlatformReadyState };