import { LoadingCircle } from '@/components/_CommonElements/LoadingBar';
import Tabs from '@/components/_CommonElements/Tabs';
import PageContent from '@/components/_PageComponents/PageContent';
import { ProfileSectionWrapper } from '@/components/User/ProfileSections';
import TicketOrders from '@/components/User/TicketOrders';
import { BACKEND } from '@/constants/config';
import { OrderType } from '@/constants/enum/OrderType';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import ENDPOINT from '@/models/api/endpoint';
import { UserOrder } from '@/models/api/models/UserOrder';
import UserTicketOrdersAPIResult from '@/models/api/result/user/orders';
import i18n from "@/utils/i18n";
import serverSideAuth from '@/utils/serverSideAuth';
import { GetServerSideProps } from 'next';
import { useTranslation } from 'next-i18next';
import { useCallback, useEffect, useMemo, useState } from 'react';

interface GetUserTicketOrdersProps {
    type: OrderType;
}
const GetUserTicketOrders = ({ type }: GetUserTicketOrdersProps) => {   
    const [ pageNum, setPageNum ] = useState(1);
    const [ isLoading, setIsLoading ] = useState<boolean>(false);
    const [ ticketOrderData, setTicketOrderData ] = useState<UserTicketOrdersAPIResult>();
    
    const userPaidTicketOrdersResponse = useCallback(async (page: number) => {
        setIsLoading(true);
        const res = await (BACKEND.Gateway.fetchQuery<UserTicketOrdersAPIResult>({
            url: `${ENDPOINT.GetUserTicketOrdersByStatus()}/${type}`,
            query: {
                page: page.toString(),
            },
            params: {
                queryKey: `userPaidOrders_${type}`,
            },
        }));
        setTicketOrderData(res);
        setIsLoading(false);
    }, []);


    const goToPage = useCallback((page: number) => {
        if (page != pageNum) {
            userPaidTicketOrdersResponse(page);
            setPageNum(page);
        }
    }, [pageNum]);

    useEffect(() => {
        if (!ticketOrderData) {
            userPaidTicketOrdersResponse(pageNum);
        }
    }, [pageNum]);


    const orders = ticketOrderData?.data;
    const maxPageNum = useMemo(() => {
        const normalOrderMaxPage = orders?.maxPage || 0;
        return normalOrderMaxPage;
    }, [orders?.maxPage]);

    const orderList: UserOrder[] = orders?.list ?? [];

    return (
        <>
            {isLoading ? 
            <LoadingCircle /> :
            <TicketOrders
                userOrders={orderList}
                listType={type}
                pageNum={pageNum}
                maxPageNum={maxPageNum}
                setPageNum={goToPage}
            />}
        </>
    );
}

const UserTicketOrdersPage = () => {
    const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
    const tabs = useMemo(() => [
        {
            name: profileTranslation("order.completed"),
            contents: <GetUserTicketOrders type={OrderType.COMPLETED} />,
        },
        {
            name: profileTranslation("order.processing"),
            contents: <GetUserTicketOrders type={OrderType.PROCESSING} />
        },
        {
            name: profileTranslation("order.paid"),
            contents: <GetUserTicketOrders type={OrderType.PAID} />
        },
        {
            name: profileTranslation("order.pending"),
            contents: <GetUserTicketOrders type={OrderType.PENDING} />
        },
        {
            name: profileTranslation("order.expired"),
            contents: <GetUserTicketOrders type={OrderType.EXPIRED} />
        }
    ], [profileTranslation]);
    const title = useMemo(() => seoTranslation("page.ticketOrders.title"), [seoTranslation]);
    return (
        <PageContent
            title={title}
            content={
                <ProfileSectionWrapper>
                    <Tabs tabs={tabs} />
                </ProfileSectionWrapper>
            }
        />
    );
};

export const getServerSideProps: GetServerSideProps = serverSideAuth(
    {
        permission: "userOnly",
    },
    (context) =>
        i18n.GetServerSidePropsAsync({
            additionalFiles: [EnumTranslationJson.Account, EnumTranslationJson.Profile],
            context,
    })
);
export default UserTicketOrdersPage;
