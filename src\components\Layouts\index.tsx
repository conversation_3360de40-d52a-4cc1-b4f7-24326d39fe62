import classNames from "classnames";

import { useTheme } from "@mui/material/styles";
import { useMediaQuery } from "@mui/material";
import { useRouter } from "next/router";
import { ReactNode, useEffect, useState } from "react";
import AuthenicationHooks from "@/hooks/Authentication";
import styles from "./layouts.module.scss";
import Footer from "./Footer";
import Header from "./Header";
import MobileBottomAppBar from "./MobileBottomAppBar";

interface LayoutProps {
    children: ReactNode
}
const Layouts = (props: LayoutProps) => {
  const { route } = useRouter();
  const theme = useTheme();
  const matches = useMediaQuery(theme.breakpoints.down("lg"));
  const { isAuthorized } = AuthenicationHooks.useUserInfo();
  const [isKioskMode, setIsKioskMode] = useState(false);
  
  useEffect(() => {
    if (route.includes('/kiosk')) {
      setIsKioskMode(true);
    }
  }, [route]);
  
  return (<div className={classNames(styles.layouts)}>
    <Header/> 
    <div className={styles.headerPlaceholder} />
    <main className={classNames(styles.mainBody)}>
      {props.children}
    </main>

    {!isKioskMode && matches && isAuthorized && <MobileBottomAppBar />}
    {!isKioskMode && <Footer/>}
  </div>);
};

export default Layouts;