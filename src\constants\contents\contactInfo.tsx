interface ContactInfo {
    name: string;
    url: string;
}
type SupportedContactInfo = "email" | "whatsapp" | "facebook" | "twitter" | "instagram" | "youtube";

const ContactInfos: { [key in SupportedContactInfo]?: ContactInfo } = {
    email: {
        name: "<EMAIL>",
        url: "<EMAIL>",
    },
    facebook: {
        name: "INCUTix",
        url: "https://www.facebook.com/incutix/",
    },
    instagram: {
        name: "INCUTix",
        url: "https://www.instagram.com/incutix/",
    },
    youtube: {
        name: "INCUTix",
        url: "https://www.youtube.com/@Incutix/videos",
    }
}
export default ContactInfos;