import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import styles from "./successDisplay.module.scss";

const SuccessDisplay = () => {
    const { t: redeemTranslation } = useTranslation(EnumTranslationJson.Redeem);
    return (
        <div className={styles.message}>
            {redeemTranslation("modal.success")}
        </div>
    );
};
export default SuccessDisplay;