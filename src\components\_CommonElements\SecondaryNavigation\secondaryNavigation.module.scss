@use 'sass:map';
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/animations.module.scss" as animations;

$primary-color: map.get(theme.$color, "primary");
$secondary-color: map.get(theme.$color, "secondary");
$background-color: map.get(theme.$color, "background");
$text-color: map.get(theme.$color, "text");
$desktop-header-height: map.get(theme.$height, "desktop-header");
$tablet-header-height: map.get(theme.$height, "tablet-header");

.navigation {
    list-style-type: none;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    font-size: 1.25rem;
    font-weight: bold;
    letter-spacing: 3px;
    background-color: $background-color;
    width: min(1140px, 95vw);

    @include viewport.within("mobile") {
        gap: 10px;
        padding: 0 30px;
        justify-content: flex-start;
    }

    li {
        padding: 0 16px;
        @include viewport.within("mobile") {
            border-left: 3px solid $secondary-color;
        }
        & + li {
            border-left: 3px solid $secondary-color;
        }
    }

    li a {
        text-decoration: none;
        display: inline-block;
        vertical-align: middle;
        font-size: 1rem;
        line-height: 2rem;
        line-height: 30px;
        transition: color animations.$fast-animation-speed;

        &:hover {
            color: $primary-color;
        }
        @include viewport.within("mobile") {
            font-size: 1rem;
        }
    }
}