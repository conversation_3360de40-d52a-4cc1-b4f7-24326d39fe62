import styles from "@/components/User/AccountInfo/account.module.scss";
import LegendFieldSet from "@/components/_CommonElements/Display/LegendFieldSet";
import { ReactNode } from "react";
interface InfoFieldProps {
    label?: string;
    children: ReactNode;
}

const InfoField = (props: InfoFieldProps) => {
    const {
        children,
        label,
    } = props;

    return (
        <LegendFieldSet legend={label}>
            <p className={styles.content}>
                {children}
            </p>
        </LegendFieldSet>
    );
};

export default InfoField;