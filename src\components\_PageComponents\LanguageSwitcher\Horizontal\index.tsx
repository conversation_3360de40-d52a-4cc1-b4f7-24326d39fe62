import styles from "./horizontal.module.scss";
import { useRouter } from "next/router";

import Select from "@/components/_CommonElements/Input/Select";
import LanguageList from "@/constants/languages";
import { useCallback } from "react";
import { setCookie } from "cookies-next";
import { EnumCookieKey } from "@/constants/enum/Cookies";

const HorizontalLanguageSwitcher = () => {
  const router = useRouter();
  const ChangeLocale = useCallback((locale: string) => {
    const { pathname, query, asPath } = router;
    void router.replace({ pathname, query }, asPath, {
        locale,
        scroll: false,
    });
    setCookie(EnumCookieKey.PREFERRED_LANGUAGE, locale);
  }, [router]);

  return (
    <div className={styles.horizontal}>
        <Select.Box className={styles.select} value={router.locale}>
            {LanguageList.items.map(item => (
                <Select.Option
                    key={item.locale}
                    value={item.locale}
                    onClick={() => ChangeLocale(item.locale)}
                >
                    {item.displayName}
                </Select.Option>
            ))}
        </Select.Box>
        </div>
  );
};

export default HorizontalLanguageSwitcher;