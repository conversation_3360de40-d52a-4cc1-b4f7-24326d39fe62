import Head from 'next/head';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import { useTranslation } from "next-i18next";
import { FRONTEND } from '@/constants/config';

const DefaultMeta = () => {
    const { t: seoTranslation }  = useTranslation(EnumTranslationJson.SEO);

    const websiteName = seoTranslation("title");
    const websiteUrl = FRONTEND.URL;
    const description = `${seoTranslation("description")}`;
    const faviconPath = "/favicon.ico";
    const faviconType = "image/png";

    const imagePath = "https://assets.incutix.com/logo.png";
    const imageType = "image/png";
    const imageWidth = "318";
    const imageHeight = "62";

    const keywords = seoTranslation("keywords");

    return (
        <Head>
            <meta charSet="utf-8" />

            <link rel="icon" type={faviconType} href={faviconPath} ></link>

            <title>{websiteName}</title>
            <meta property="og:title" content={websiteName} key="og:title" />
            <meta property="twitter:title" content={websiteName} key="twitter:title" />

            <meta property="og:type" content="website" key="og:type" />
            <meta property="og:url" content={websiteUrl} key="og:url" />

            <meta property="twitter:card" content="summary" key="twitter:card" />
            <meta property="twitter:url" content={websiteUrl} key="twitter:url" />

            <meta name="description" content={description} key="description" />
            <meta property="og:description" content={description} key="og:description" />
            <meta property="twitter:description" content={description} key="twitter:description" />

            <meta name="keywords" content={keywords} key="keywords" />

            <meta property="og:image" content={imagePath} key="og:image" />
            <meta property="og:image:type" content={imageType} key="og:image:type" />
            <meta property="og:image:width" content={imageWidth} key="og:image:width" />
            <meta property="og:image:height" content={imageHeight} key="og:image:height" />

            <meta property="twitter:image" content={imagePath} key="twitter:image" />

            <meta name="viewport" content="width=device-width, minimum-scale=1, initial-scale=1, viewport-fit=cover" />

            <meta name="theme-color" content="#121212" />

            <meta name="url" content={websiteUrl} key="url" />

            <meta property="og:site_name" content={websiteName} key="og:site_name" />

            {/* <meta httpEquiv="expires" content="1209600"/> */}

        </Head>
    );
};

export default DefaultMeta;
