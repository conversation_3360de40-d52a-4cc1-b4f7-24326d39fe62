import MembershipSubscription from "@/models/api/models/MembershipSubscription";
import { useTranslation } from "next-i18next";
import { useCallback, useEffect, useMemo, useState } from "react";

import styles from "@/components/User/MembershipReservationSection/membershipReservation.module.scss";
import { TextButton } from "@/components/_CommonElements/Button";
import { Select } from "@/components/_CommonElements/Input";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useReservableSession } from "@/hooks/MembershipReservation/ReservableSession";
import Region from "@/models/api/models/Region";
import ReservableSession from "@/models/api/models/ReservableSession";
import ReservableSite from "@/models/api/models/ReservableSite";
import ReservableSessionsAPIResult from "@/models/api/result/reserve/reservableSessions";
import CancelIcon from '@mui/icons-material/Cancel';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SearchIcon from '@mui/icons-material/Search';
import { Box, Card, CardActions, CardContent, CardHeader, Chip, CircularProgress, Container, Divider, Drawer, IconButton, Paper, SelectChangeEvent, Stack, TextField, Typography, useMediaQuery } from "@mui/material";
import classNames from "classnames";
import dayjs from "dayjs";
import { useRouter } from "next/router";
import { DateRange, DayPicker, Matcher, Modifiers, getDefaultClassNames } from "react-day-picker";
import { zhHK } from "date-fns/locale";
import "react-day-picker/style.css";
import { eachDayOfInterval, endOfMonth, isAfter, isBefore, isSameDay } from "date-fns";
import ReserveConfirmDialog from "./ReserveConfirmDialog";
import UserReservedSession from "@/models/api/models/UserReservedSession";
import UserReservedSessionsAPIResult from "@/models/api/result/user/reservedSessions";
import { EnumUserReservedSessionStatus } from "@/constants/enum/UserReservedSessionStatus";
import ReserveSessionAPIResult from "@/models/api/result/reserve/reserveSession";
import Accordion from "@/components/_CommonElements/Accordion";
import ReserveCancelDialog from "./ReserveCancelDialog";

interface MembershipReservationProps {
    userMemberhsip: MembershipSubscription;
    reservableSites: ReservableSite[];
}

const MembershipReservationSection = (props: MembershipReservationProps) => {
    const {
      userMemberhsip,
      reservableSites
    } = props;

    const now = dayjs();
    const addDays = now.add(2, 'month');
    const defaultClassNames = getDefaultClassNames();
    const screenBreakpointUpDetect = useMediaQuery('(min-width: 1440px)');
    const screenBreakpointDownDetect = useMediaQuery('(max-width: 468px)');
    const { t: commonTranslation } = useTranslation(EnumTranslationJson.Common);
    const { t: reservationTranslation } = useTranslation(EnumTranslationJson.Reservation);
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const { getSessionsAsync, getUserReservedSessionAsync, cancelReservedSessionAsync } = useReservableSession();
    
    const [ isAPILoading, setIsAPILoading ] = useState<boolean>(false);
    const [ numOfMonthsShow, setNumOfMonthsShow ] = useState(2);
    const [ isOpenDayPickerDialog, setIsOpenDayPickerDialog ] = useState<boolean>(false);
    const [ isOpenReserveConfirmDialog, setIsOpenReserveConfirmDialog ] = useState<boolean>(false);
    const [ isOpenReserveConfirmCanelDialog, setIsOpenReserveConfirmCancelDialog ] = useState<boolean>(false);
    const [ enqueryDateSelected, setEnqueryDateSelected ] = useState<DateRange | undefined>();
    const [ enqueryStartDate, setEnqueryStartDate ] = useState<string>("");
    const [ enqueryEndDate, setEnqueryEndDate ] = useState<string>("");
    const [ selectedRegion, setSelectedRegion ] = useState("");
    const [ selectedLocation, setSelectedLocation ] = useState("");
    const [ pickedDate, setPickedDate ] = useState<Date | undefined>();
    const [ pickedDateSessionId, setPickedDateSessionId ] = useState<string>("");
    const [ startMonth, setStartMonth ] = useState<Date | undefined>(new Date(now.get('year'), now.get('month')));
    const [ endMonth, setEndMonth ] = useState<Date | undefined>(endOfMonth(new Date(addDays.get('year'), addDays.get('month'))));
    const [ searchResult, setSearchResult ] = useState<ReservableSession[]>([]);
    const [ userBookedResult, setUserBookedResult ] = useState<UserReservedSession[] | null>(null);
    
    const router = useRouter();

    const getUserBookedResult = useCallback(() => {
        void (async () => {
            setIsAPILoading(true);
            const { data: responseData } : UserReservedSessionsAPIResult = await getUserReservedSessionAsync(EnumUserReservedSessionStatus.RESERVE);
            if (responseData) setUserBookedResult(responseData);
            setIsAPILoading(false);
        })();
    }, []);

    const regions = useMemo(() => {
        return reservableSites ? reservableSites.reduce((_regions, site) => {
            if (site?.region) _regions.push(site.region);
            return _regions;
        }, [] as Region[]) : [];
    }, [reservableSites]);

    const reservableSessions = useMemo(() => {
        if (searchResult.length <= 0) return [];
        return searchResult.reduce((dateArr, session) => {
            if (session.isReservable && (session.limit === -1 || session.limit > 0)) {
                const flatInEachDay = eachDayOfInterval({
                    start: new Date(session.sessionStartDate),
                    end: new Date(session.sessionEndDate)
                });
                dateArr = dateArr.concat(flatInEachDay);
            }
            return dateArr;
        }, [] as Matcher[]);
    }, [searchResult]);

    const userReservedSession = useMemo(() => {
        if (!userBookedResult) return null;
        if (userBookedResult.length <= 0) return null;
        return userBookedResult.at(0) ?? null;
    }, [userBookedResult]);

    const userReservedSessionsMatcher = useMemo(() => {
        return userReservedSession ? eachDayOfInterval({ start: new Date(userReservedSession.sessionStartDate), end: new Date(userReservedSession.sessionEndDate) }) : [];
    }, [userReservedSession]);

    const disableAllDaysButReservable = useMemo(() => {
        const days = eachDayOfInterval({ start: startMonth as Date, end: endMonth as Date });
        return days.filter(day => 
            (reservableSessions.findIndex(sessionDay => isSameDay(sessionDay as Date, day)) === -1)
            ||
            (userReservedSessionsMatcher.findIndex(sessionDay => isSameDay(sessionDay as Date, day)) > -1)
        );
    }, [startMonth, endMonth, reservableSessions, userReservedSessionsMatcher]);

    const regionTranslation = useMemo(() => {
        if (regions.length <= 0) return selectedRegion;
        const targetRegion = regions.find(r => r.regionId === selectedRegion);
        const regionName = targetRegion ? targetRegion.regionName.replaceAll(' ', '').toLowerCase() : "";
        return (/^[a-zA-Z]+(\.[a-zA-z]+)?$/).test(regionName) ? commonTranslation(`location.${regionName}`) : regionName;
    }, [commonTranslation, regions, selectedRegion]);

    const handleOpenDayPickerDialog = useCallback(() => {
      let passedRequired = true;
      setIsOpenDayPickerDialog(true);
    }, []);
  
    const handleCloseDayPickerDialog = useCallback(() => {
      setIsOpenDayPickerDialog(false);
    }, []);

    const handleOpenReserveConfirmDialog = useCallback(() => {
        setIsOpenReserveConfirmDialog(true);
    }, [pickedDateSessionId]);

    const handleOpenReserveConfirmCancelDialog = useCallback(() => {
        setIsOpenReserveConfirmCancelDialog(true);
    }, []);

    const handleCloseReserveConfirmDialog = useCallback(() => {
        setIsOpenReserveConfirmDialog(false);
        void getUserBookedResult();
    }, []);

    const handleCloseReserveConfirmCancelDialog = useCallback(() => {
        setIsOpenReserveConfirmCancelDialog(false);
    }, []);

    const handleSelect = useCallback((newSelected: DateRange) => {
        // console.log(newSelected);
        // Update the selected dates
        setEnqueryDateSelected(newSelected);
    }, []);

    const handleConfirmDaySelectForEnquery = useCallback(() => {
        const regionTimeZone = regions.find(region => region.regionId === selectedRegion)?.regionTimeZone ?? "Asia/Hong_Kong";
        if (enqueryDateSelected?.from) {
            const selectedRegionFromDateTime = dayjs(enqueryDateSelected.from).tz(regionTimeZone);
            setEnqueryStartDate(selectedRegionFromDateTime.format('YYYY-MM-DD'));
        }
        if (enqueryDateSelected?.to) {
            const selectedRegionToDateTime = dayjs(enqueryDateSelected.to).tz(regionTimeZone);
            setEnqueryEndDate(selectedRegionToDateTime.format('YYYY-MM-DD'));
        }
        setIsOpenDayPickerDialog(false);
    }, [enqueryDateSelected, reservableSites]);

    const onSelectedRegionChanged = useCallback((e: SelectChangeEvent<unknown>) => {
        const id = e.target.value as string;
        setSelectedRegion(id);
    }, []);
    const onSelectedLocationChanged = useCallback((e: SelectChangeEvent<unknown>) => {
        const id = e.target.value as string;
        setSelectedLocation(id);
    }, []);

    const onClickSearchSession = useCallback(() => {
        void (async () => {
            setIsAPILoading(true);
            const { data: responseData } : ReservableSessionsAPIResult = await getSessionsAsync(selectedLocation);
            if (responseData) setSearchResult(responseData);
            void getUserBookedResult();
            setIsAPILoading(false);
        })();
    }, [selectedLocation]);

    const handlePickedDate = useCallback((newSelected: Date, modifiers: Modifiers) => {
        console.log(newSelected);
        // Update the selected date
        setPickedDate(newSelected);
        const reservableSessionData = searchResult.find(session => +newSelected >= +(new Date(`${session.sessionStartDate} ${session.sessionStartTime}`)) && +newSelected <= +(new Date(`${session.sessionEndDate} ${session.sessionEndTime}`)));
        if (reservableSessionData) {
            setPickedDateSessionId(reservableSessionData.membershipReservableSessionId);
        }
    }, [searchResult]);

    const handleCancelReservation = useCallback(() => {
        if (userBookedResult) {
            void handleOpenReserveConfirmCancelDialog();
        }
    }, [userBookedResult]);

    useEffect(() => {
        if (screenBreakpointUpDetect) {
            setNumOfMonthsShow(3);
        } else {
            setNumOfMonthsShow(2);
        }

        if (regions && selectedRegion === "") {
            setSelectedRegion(regions[0].regionId);
            const siteId = reservableSites.find(site => site.region?.regionId === regions[0].regionId)?.siteId ?? "";
            setSelectedLocation(siteId);
        }

        if (!userBookedResult) {
            void getUserBookedResult();
        }
    }, [screenBreakpointUpDetect, regions, selectedRegion, userBookedResult]);
  
    return (
      <>
        <Container className={styles.membershipReservationContainer}>
            <Stack 
                direction={{ xs: 'column', sm: 'column' }} 
                spacing={1}
                justifyContent={'center'}
            >
                <Accordion 
                    title={reservationTranslation("userReserved.label.title")}
                    className={styles.userReservedContainer}
                    defaultExpanded
                >
                    <Stack>
                        <Card elevation={0}>
                            {userReservedSession ? 
                                <>
                                    <CardContent>
                                        <Stack direction={"column"} spacing={1}>
                                            <Stack direction={"row"} spacing={1}>
                                                <Chip label={reservationTranslation("userReserved.label.date")} size="small" />
                                                <Typography variant="subtitle2" className={styles.eventDate}>{dayjs(userReservedSession.sessionStartDate).format("YYYY-MM-DD")}</Typography>
                                            </Stack>
                                            <Stack direction={"row"} spacing={1}>
                                                <Chip label={reservationTranslation("userReserved.label.region")} size="small" />
                                                <Typography>{regionTranslation}</Typography>
                                            </Stack>
                                            <Stack direction={"row"} spacing={1}>
                                                <Chip label={reservationTranslation("userReserved.label.location")} size="small" />
                                                <Typography>{selectedLocation}</Typography>
                                            </Stack>
                                        </Stack>
                                    </CardContent>
                                    <CardActions>
                                        <TextButton 
                                            onClick={handleCancelReservation}
                                            disabled={isAPILoading}
                                        >
                                            {reservationTranslation("userReserved.label.cancel")}
                                        </TextButton>
                                    </CardActions>
                                </>
                                :
                                <CardContent>
                                    <Typography>{reservationTranslation("userReserved.label.noRecard")}</Typography>
                                </CardContent>
                            }
                        </Card>
                    </Stack>
                </Accordion>
                <Paper className={styles.enqueryContainer}>
                    <Stack 
                        direction={{ xs: 'column', sm: 'row' }} 
                        spacing={1}
                        className={styles.enqueryContent}
                    >
                        <Select.Box
                            size="medium"
                            variant="filled"
                            label={reservationTranslation("Region")}
                            value={selectedRegion}
                            className={styles.selectBox}
                            onChange={onSelectedRegionChanged}
                            disabled
                            {...(screenBreakpointDownDetect ? {fullWidth: true} : {})}
                        >
                            <Select.Option disabled value="disable">{reservationTranslation("enqueryBar.label.region")}</Select.Option>
                            {regions.map(region =>
                                <Select.Option key={`region-${region.regionId}`} value={region.regionId}>{region.regionName}</Select.Option>
                            )}
                        </Select.Box>
                        <Divider orientation="vertical" flexItem />
                        <Select.Box
                            size="small"
                            variant="filled"
                            label={"Location"}
                            value={selectedLocation}
                            className={styles.selectBox}
                            onChange={onSelectedLocationChanged}
                            disabled
                            {...(screenBreakpointDownDetect ? {fullWidth: true} : {})}
                        >
                            <Select.Option disabled value="disable">{reservationTranslation("enqueryBar.label.location")}</Select.Option>
                            <Select.Option disabled value="CFE9A6F918C74B5680AB825C5497071A">{"INCUBASE Arena"}</Select.Option>
                        </Select.Box>
                        <Divider orientation="vertical" flexItem />
                        <Box className={styles.daypickerDisplayBox}>
                            <TextField
                                size="small"
                                name="enqueryStartDate"
                                variant="standard"
                                label={reservationTranslation("enqueryBar.label.start")}
                                placeholder="YYYY-MM-DD"
                                value={enqueryStartDate}
                                onClick={handleOpenDayPickerDialog}
                                InputLabelProps={{
                                    shrink: true,
                                }}
                            />
                            <Divider children={<Typography>-</Typography>} className={styles.divider} />
                            <TextField
                                size="medium"
                                name="enqueryEndDate"
                                variant="standard"
                                label={reservationTranslation("enqueryBar.label.end")}
                                placeholder="YYYY-MM-DD"
                                value={enqueryEndDate}
                                onClick={handleOpenDayPickerDialog}
                                InputLabelProps={{
                                    shrink: true,
                                }}
                            />
                        </Box>
                        <IconButton type="button" sx={{ p: '10px' }} aria-label="search" onClick={onClickSearchSession}>
                            <SearchIcon />
                        </IconButton>
                    </Stack>
                </Paper>
                {isAPILoading ? <Box sx={{ display: "flex", justifyContent: "center" }}><CircularProgress /></Box> : null}
                {searchResult.length > 0 ?
                    <Paper elevation={0}>
                        <Stack direction={'column'} justifyContent={"center"} spacing={1}>
                            <Paper elevation={0}>
                                <Stack direction={'row'} spacing={1}>
                                    <Box className={styles.modifierBox}>
                                        <div className={classNames(styles.sessionModifierLabel, styles.sessionReservable)}></div>
                                        <Typography component="span">{reservationTranslation("enqueryResult.modifierLabel.reservable")}</Typography>
                                    </Box>
                                    <Box className={styles.modifierBox}>
                                        <div className={classNames(styles.sessionModifierLabel, styles.userBooked)}></div>
                                        <Typography component="span">{reservationTranslation("enqueryResult.modifierLabel.booked")}</Typography>
                                    </Box>
                                </Stack>
                            </Paper>
                            <DayPicker
                                locale={zhHK}
                                mode="single"
                                numberOfMonths={numOfMonthsShow}
                                startMonth={startMonth}
                                endMonth={endMonth}
                                selected={pickedDate}
                                disabled={disableAllDaysButReservable}
                                onDayClick={handlePickedDate}
                                classNames={{
                                    'root': classNames(defaultClassNames.root, styles.daypickerOverrideRoot),
                                    'months': classNames(defaultClassNames.months, styles.daypickerOverrideMonths),
                                    'month': classNames(defaultClassNames.month, styles.daypickerOverrideMonth),
                                    'day': classNames(defaultClassNames.day, styles.daypickerOverrideDay),
                                    'day_button': classNames(defaultClassNames.day_button, styles.daypickerOverrideDayButton),
                                    'selected': classNames(styles.daypickerOverrideSelected),
                                }}
                                required
                                hideNavigation={numOfMonthsShow >= 3}
                                modifiers={{
                                    reservable: reservableSessions,
                                    booked: userReservedSessionsMatcher
                                }}
                                modifiersClassNames={{
                                    reservable: styles.reservableSessionModifier,
                                    booked: styles.userBookedSessionModifier
                                }}
                            />
                            <TextButton 
                                disabled={pickedDate ? false : true}
                                onClick={handleOpenReserveConfirmDialog}
                            >{reservationTranslation("dayPicker.button.reserve")}</TextButton>
                        </Stack>
                    </Paper>
                    : null
                }
            </Stack>

            <Drawer
                open={isOpenDayPickerDialog}
                anchor="bottom"
                onClose={handleCloseDayPickerDialog}
                classes={{
                    'paper': styles.daypickerDrawer
                }}
            >
                <Stack direction={"column"}>
                    <DayPicker
                        mode="range"
                        numberOfMonths={numOfMonthsShow}
                        startMonth={startMonth}
                        endMonth={endMonth}
                        selected={enqueryDateSelected}
                        onSelect={handleSelect}
                        classNames={{
                            'root': classNames(defaultClassNames.root, styles.daypickerOverrideRoot),
                            'months': classNames(defaultClassNames.months, styles.daypickerOverrideMonths),
                            'month': classNames(defaultClassNames.month, styles.daypickerOverrideMonth),
                            'day': classNames(defaultClassNames.day, styles.daypickerOverrideDay),
                            'day_button': classNames(defaultClassNames.day_button, styles.daypickerOverrideDayButton)
                        }}
                        required
                        hideNavigation={numOfMonthsShow >= 3}
                    />
                    <Stack direction={"row"} className={styles.drawerActionArea}>
                        <TextButton
                            startIcon={<CancelIcon />}
                            size="medium"
                            onClick={handleCloseDayPickerDialog}
                        >
                            &nbsp; {modalTranslation("buttons.cancel")}
                        </TextButton>
                        <TextButton
                            startIcon={<CheckCircleIcon />}
                            size="medium"
                            onClick={handleConfirmDaySelectForEnquery}
                        >
                            &nbsp; {modalTranslation("buttons.confirm")}
                        </TextButton>
                    </Stack>
                </Stack>
            </Drawer>
        </Container>

        <ReserveConfirmDialog 
            open={isOpenReserveConfirmDialog} 
            onClose={handleCloseReserveConfirmDialog} 
            pickedDate={pickedDate} 
            pickedDateSessionId={pickedDateSessionId}
        />

        <ReserveCancelDialog
            open={isOpenReserveConfirmCanelDialog}
            onClose={handleCloseReserveConfirmCancelDialog}
            userReservedSession={userReservedSession}
            setUserBookedResult={setUserBookedResult}
        />
      </>
    );
  };
  
  export default MembershipReservationSection;