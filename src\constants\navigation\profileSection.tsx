import AccountBoxIcon from '@mui/icons-material/AccountBox';
import BadgeIcon from '@mui/icons-material/Badge';
import BookOnlineIcon from '@mui/icons-material/BookOnline';
import InsertInvitationIcon from '@mui/icons-material/InsertInvitation';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import Package2Icon from '@mui/icons-material/Inventory2Outlined';
import RouteMap from "../config/RouteMap";

export interface ProfileSectionItem {
  displayName: string;
  path?: string;
  action?(): void;
  active: boolean; 
  visible?: boolean;
  requiredMembership?: boolean;
  icon?: JSX.Element | null;
}

export default class ProfileSectionArray {
  private readonly items: ProfileSectionItem[];

  constructor(currentPath?: string) {
    this.items = [
      {
        displayName: "myProfile",
        path: RouteMap.UserProfile,
        icon: <AccountBoxIcon />,
        visible: true,
        requiredMembership: false,
      },
      {
        displayName: "membershipSubscription.title",
        path: RouteMap.MembershipSubscription,
        icon: <BadgeIcon />,
        visible: false,
        requiredMembership: false,
      },
      {
        displayName: "membershipReservation.title",
        path: RouteMap.MembershipReservation,
        icon: <InsertInvitationIcon />,
        visible: false,
        requiredMembership: true,
      },
      {
        displayName: "boundTickets.title",
        path: RouteMap.BoundTickets,
        icon: <BookOnlineIcon />,
        visible: true,
        requiredMembership: false,
      },
      {
        displayName: "ticketOrders",
        path: RouteMap.ticketOrders,
        icon: <ReceiptLongIcon />,
        visible: true,
        requiredMembership: false,
      },
      {
        displayName: "productOrders",
        path: RouteMap.productOrders,
        icon: <Package2Icon />,
        visible: true,
        requiredMembership: false,
      }
    ].map(item => ({
        ...item,
        active: currentPath === item.path
    }));
  }

  public getItems() {return this.items}
}