import { <PERSON><PERSON><PERSON><PERSON> } from "@/constants/config";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import ENDPOINT from "@/models/api/endpoint";
import ApplyDiscountCodeAPIResult from "@/models/api/result/order/applyDiscountCode";
import RemoveApplyDiscountCodeAPIResult from "@/models/api/result/order/removeAppliedDiscountCode";
import UserAppliedDiscountCodeAPIResult from "@/models/api/result/order/userAppliedDiscountCode";

const useDiscountCode = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION, EnumRequestHeader.LANGUAGE] });
    const applyDiscountCodeAsync = async (promoteCode: string) => {
        const formData = new FormData();
        formData.append("DiscountCode", promoteCode);
        return await requestAsync<ApplyDiscountCodeAPIResult>(
            ENDPOINT.OrderApplyDiscountCode(),
            {
                method: RequestMethod.POST,
                data: formData
            }
        );
    };

    const getAppliedDiscountCodeAsync = async () => {
        return await requestAsync<UserAppliedDiscountCodeAPIResult>(
            ENDPOINT.GetUserAppliedDiscountCode(),
            {
                method: RequestMethod.GET,
            }
        );
    };

    const removeAppliedDiscountCodeAsync = async () => {
        return await requestAsync<RemoveApplyDiscountCodeAPIResult>(
            ENDPOINT.OrderRemoveDiscountCode(),
            {
                method: RequestMethod.DELETE,
            }
        );
    };

    return { applyDiscountCodeAsync, getAppliedDiscountCodeAsync, removeAppliedDiscountCodeAsync };
};

export { useDiscountCode };