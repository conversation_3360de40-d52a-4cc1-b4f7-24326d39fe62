import { <PERSON><PERSON><PERSON><PERSON> } from "@/constants/config";
import API from "@/utils/API";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import ENDPOINT from "@/models/api/endpoint";
import { DeleteAllShoppingCartItemAPIResult, DeleteShoppingCartItemAPIResult, FetchShoppingCartAPIResult, UpdateShoppingCartItemAPIResult } from "@/models/api/result/user/shoppingCart";

const useShoppingCart = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.LANGUAGE, EnumRequestHeader.AUTHORIZATION] });
    const fetchAsync = async () => {
        return await requestAsync<FetchShoppingCartAPIResult>(
            ENDPOINT.GetShoppingCart(),
            {
                method: "GET"
            }
        );
    };

    const updateAsync = async ({eventSessionBundleId, quantity} : {eventSessionBundleId: string, quantity: number}) => {
        const formData = API.ToFormData({eventSessionBundleId, quantity});
        return await requestAsync<UpdateShoppingCartItemAPIResult>(
            ENDPOINT.UpdateShoppingCartItem(),
            {
                method: "POST",
                data: formData
            }
        );
    };

    const deleteAsync = async (eventSessionBundleId: string) => {
        const formData = API.ToFormData({"eventSessionBundleId": eventSessionBundleId});
        return await requestAsync<DeleteShoppingCartItemAPIResult>(
            ENDPOINT.DeleteShoppingCartItem(eventSessionBundleId),
            {
                method: RequestMethod.DELETE,
                data: formData
            }
        );
    };

    const deleteAllAsync = async () => {
        return await requestAsync<DeleteAllShoppingCartItemAPIResult>(
            ENDPOINT.DeleteAllShoppingCartItem(),
            {
                method: RequestMethod.DELETE
            }
        );
    };

    return { fetchAsync, updateAsync, deleteAsync, deleteAllAsync };
};

export { useShoppingCart };