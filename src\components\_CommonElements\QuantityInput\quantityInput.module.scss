@use "sass:map";
@use "@/styles/utils/theme.module.scss" as theme;

@mixin common-styles {
    user-select: none;
    border: map.get(theme.$presets, "light-border");
    max-height: 56px;

    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8em;
    @content;
}

.container {
    display: flex;
    justify-content: center;
    min-width: 150px;

    .decrement {
        @include common-styles;
        padding: 0 15px;
        cursor: pointer;
        border-radius: map.get(theme.$border, "radius") 0 0 map.get(theme.$border, "radius");
    };
    .increment {
        @include common-styles;
        padding: 0 15px;
        cursor: pointer;
        border-radius: 0 map.get(theme.$border, "radius") map.get(theme.$border, "radius") 0;
    }
    .amount {
        @include common-styles;
        flex-grow: 1;
        text-align: center;
        border-left: 0;
        border-right: 0;
        font-size: 1em;
        user-select: auto;
    }
}