@use "@/styles/utils/viewport.module.scss" as viewport;

.emptyRecord {
    padding-bottom: 20px;
    color: #5b5b5b;
    font-size: 1.5rem;
    margin: 0 auto;
}

.eventDisplayContainer {
    display: flex;
    flex-direction: row;
    $gap_size: 20px;
    flex-wrap: wrap;
    & > div {
        $grid_column: 4;
        margin: $gap_size / 2;
        width: calc(100% / $grid_column - $gap_size);
    }
    @include viewport.within("tablet") {
        $grid_column: 3;
        & > div {
            width: calc(100% / $grid_column - $gap_size);
        }
    }
    @include viewport.within("mobile") {
        $grid_column: 2;
        & > div {
            width: calc(100% / $grid_column - $gap_size);
        }
    }
}