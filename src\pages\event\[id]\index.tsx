import type { NextPage } from "next";
import { PageMeta } from "@/components/_PageComponents";
import EventInfoDisplay from "@/components/Event/InfoDisplay";
import { GetServerSideProps } from "next";
import ENDPOINT from "@/models/api/endpoint";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import EventDetailsAPIResult from "@/models/api/result/events/details";
import { useMemo } from "react";
import EnumEventPreviewPosition from "@/constants/enum/EventPreviewPosition";
import RouteMap from "@/constants/config/RouteMap";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import i18n from "@/utils/i18n";
import { GenericAPI } from "@stoneleigh/api-lib";
import Section from "@/components/_CommonElements/Section";
import EventBundlesAPIResult from "@/models/api/result/events/bundles";

interface Props {
    fallback: {
        EventDetails: EventDetailsAPIResult,
        EventBundles: EventBundlesAPIResult
    }
}
const EventPage: NextPage<Props> = (props) => {
    const { fallback } = props;
    const eventDetails = fallback.EventDetails.data!;
    const eventBundles = fallback.EventBundles.data!;
    const metaImages = eventDetails.previewList.filter(preview => preview.position === EnumEventPreviewPosition.META);
    const metaImage = useMemo(() => {
        return metaImages.length === 0 ? undefined : metaImages[0];        
    }, [metaImages]);
    
    return (
        <>
            <PageMeta
                websiteUrl={RouteMap.ShareEvent(eventDetails.eventId)}
                title={eventDetails.eventName}
                keywords={eventDetails.eventName}
                description={eventDetails.eventMetaDescription.substring(0, 160) + (eventDetails.eventMetaDescription.length > 160 ? "…" : "")}
                imagePath={metaImage?.previewContent || ""}
                imageType={`image/${metaImage?.previewType || ""}`}
                imageHeight={metaImage?.height?.toString?.() || ""}
                imageWidth={metaImage?.width?.toString?.() || ""}
            />
            {/* <Section
                containerSize="thin"
                content={<EventInfoDisplayTest eventDetails={eventDetails} />}
            /> */}
            <Section
                containerSize="thin"
                labelSize="xlarge"
                label={eventDetails.eventName}
            />
            <Section
                containerSize="tight"
                content={<EventInfoDisplay eventDetails={eventDetails} eventBundles={eventBundles} />}
            />
        </>
    );
};
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [EnumTranslationJson.Event, EnumTranslationJson.Guide],
    context,
    getServerSideProps: async context => {
        const { params, locale } = context;
        const [EventDetails, EventBundles] = await Promise.all([
            GenericAPI.requestAsync<EventDetailsAPIResult>(
                ENDPOINT.EventDetilsById(params!.id as string),
                {
                    method: "GET",
                    headers: {
                        [EnumRequestHeader.LANGUAGE]: locale || "en-US"
                    }
                }
            ),
            GenericAPI.requestAsync<EventBundlesAPIResult>(
                ENDPOINT.EventBundlesById(params!.id as string),
                {
                    method: "GET",
                    headers: {
                        [EnumRequestHeader.LANGUAGE]: locale || "en-US"
                    }
                }
            )
        ]);
        return {
            props: {
                fallback: {
                    EventDetails,
                    EventBundles
                }
            }
        }
    }
});
export default EventPage;
