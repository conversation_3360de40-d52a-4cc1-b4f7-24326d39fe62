@use "sass:map";
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;
@import "rfs/scss";

.head {
  position: relative;
  margin: 0 auto;
}

$underscore-width: 4px;

.titleContainer {
    margin: 20px 0 30px 0;
    .title {
        @include font-size(40px);
        position: relative;
        // word-break: keep-all;
        min-width: 150px;
        white-space: pre-line;
        letter-spacing: 4px;
        font-weight: 800;
        padding-bottom: 10px;

        @include viewport.within("tablet") {
            margin-left: 15px;
        }
        &:after {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 6px;
            content: '';
            background: linear-gradient(90deg, #F48121 40%, #429b3f 40%) left 0px bottom 3px no-repeat;
        }
    &.noUnderline {
    
    }

    &.letterSpacing {
        letter-spacing: 10px;

        &.small {
        letter-spacing: 5px;
        }
    }

    &.small {
        font-weight: 500;
    }
    }
}
