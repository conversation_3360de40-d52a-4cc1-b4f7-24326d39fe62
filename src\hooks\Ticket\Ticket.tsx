import { BACKEND } from "@/constants/config";
import { EventType } from "@/constants/enum/EventType";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import ENDPOINT from "@/models/api/endpoint";
import UserEventTicketsAPIResult from "@/models/api/result/user/tickets";

const useTicket = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION, EnumRequestHeader.LANGUAGE] });

    const getTicketsAsync = async (type: EventType, pageNum: number) => {
        return await requestAsync<UserEventTicketsAPIResult>(
            ENDPOINT.GetUserEventsByTicketStatus(type),
            {
                method: RequestMethod.GET,
                params: {
                    page: pageNum.toString(),
                    queryKey: `user_owned_bounded_tickets-${type}`,
                },
            }
        );
    };

    return { getTicketsAsync };
};

export { useTicket };
