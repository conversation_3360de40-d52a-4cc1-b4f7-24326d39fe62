import MuiButton from "@mui/material/Button";
import { useCallback, useEffect, useState } from "react";
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import throttle from "lodash.throttle";
import styles from "../button.module.scss";
import classNames from "classnames";

const ScrollToTopFab = () => {
    const scrollToTop = useCallback(() => {
        window.scrollTo({
            top: 0,
            behavior: "smooth"
        });
    }, []);

    const [ visible, setVisible ] = useState(true);

    const onWindowScrolled = useCallback(() => {
        setVisible(window.scrollY !== 0);
    }, []);

    const onWindowScrolledThrottle = useCallback(throttle(onWindowScrolled, 100), [onWindowScrolled]);
    
    useEffect(() => {
        window.addEventListener("scroll", onWindowScrolledThrottle);
        return () => {
            window.removeEventListener("scroll", onWindowScrolledThrottle);
        };
    }, [onWindowScrolledThrottle]);
    if (!visible) {
        return null;
    }
    return (
        <MuiButton className={classNames(styles.mobile, styles.button, styles.outline)} onClick={scrollToTop}>
            <ArrowDropUpIcon />
        </MuiButton>
    );
};

export default ScrollToTopFab;