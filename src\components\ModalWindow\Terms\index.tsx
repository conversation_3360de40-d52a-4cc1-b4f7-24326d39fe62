import ModalContainer from "@/components/_CommonElements/Modal/Container";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import PrivacyPolicy from "@/components/PrivacyPolicy";
import Terms from "@/components/Terms";
import { useMemo } from "react";
import styles from "./terms.module.scss";

interface ModalTermsWindowProps {
  visible?: boolean;
  onClickClose?(): void;
  onBackdropClick?(): void;
  disableBackdropClickClose?: boolean;
};

const ModalTermsWindow = (props: ModalTermsWindowProps) => {
  const {
    visible,
    onClickClose,
    onBackdropClick,
    disableBackdropClickClose,
  } = props;
  const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
  const tabsArray = useMemo(() => [{
    name: seoTranslation("page.termsAndCondition.title"),
    contents: <Terms />,
    active: true
  },
  {
    name: seoTranslation("page.privacyPolicy.title"),
    contents: <PrivacyPolicy />
  }].map(tab => {
    const { contents, ...restProps } = tab;
    return {
        ...restProps,
        contents: <div className={styles.modalContent}>{contents}</div>
    }
  }), [seoTranslation]);

  return (
    <ModalContainer
      onClickClose={onClickClose}
      onBackdropClick={onBackdropClick}
      disableBackdropClickClose={disableBackdropClickClose}
      open={visible}
      modalTabs={tabsArray}
    >
    </ModalContainer>
  );
};

export default ModalTermsWindow;