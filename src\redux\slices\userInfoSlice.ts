import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface UserInfoSliceState {
    userVerified: boolean;
    userIdHash: string;
    sessionId: string;
    permissionGroup: string;
    isMembership: boolean;
}

const initialState: UserInfoSliceState = {
    userVerified: false,
    userIdHash: '',
    sessionId: '',
    permissionGroup: '',
    isMembership: false
};

export const userInfoSlice = createSlice({
    name: "userInfo",
    initialState,
    reducers: {
        setUserVerified:(state, action: PayloadAction<boolean>) => {
            state.userVerified = action.payload;
        },
        setUserIdHash:(state, action:PayloadAction<string>)=>{
            state.userIdHash = action.payload;
        }   ,     
        setSessionId:(state, action: PayloadAction<string>) => {
            state.sessionId = action.payload;
        },
        setIsMembership:(state, action: PayloadAction<boolean>) => {
            state.isMembership = action.payload;
        },
        clearUserInfo:(state) => {
            state.userIdHash = '';
            state.sessionId = '';
            state.permissionGroup = '';
            state.userVerified = false;
            state.isMembership = false;
        },
    }
});
export const { setUserVerified, setUserIdHash, setSessionId, setIsMembership, clearUserInfo } = userInfoSlice.actions;

export default userInfoSlice.reducer;