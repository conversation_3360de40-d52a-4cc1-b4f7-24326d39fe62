@use 'sass:math';
@use 'sass:map';

@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;

$container-height: 40px;
$letter-spacing: 3px;

.container {
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 0.8rem;

    @include viewport.within("tablet") {
        margin-top: 10px;
        max-width: 220px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }
}
.outlined {
    min-width: 85px;
    word-break: keep-all;
    position: relative;
    height: $container-height;
    color: map.get(theme.$color, "text");
    font-weight: 700;
    background-color: transparent;
    padding: 5px 15px;
    border: map.get(theme.$border, "width") solid map.get(theme.$color, "primary");
    border-radius: $container-height * 0.5;
    margin: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform animations.$fast-animation-speed, background-color animations.$fast-animation-speed, box-shadow animations.$fast-animation-speed, color animations.$fast-animation-speed;
    cursor: pointer;

    &:hover {
        background: map.get(theme.$color, "primary");
        color: map.get(theme.$color, "contrast-text");
        box-shadow: 0 0 10px map.get(theme.$color, "primary");
    }

    ;

    & .letterSpace {
        letter-spacing: $letter-spacing;
        display: inline-block;
        transform: translate($letter-spacing * 0.5, 1px);
    }
}
.contained {
    min-width: 85px;
    word-break: keep-all;
    position: relative;
    height: $container-height;
    color: map.get(theme.$color, "background");
    font-weight: 700;
    background-color: map.get(theme.$color, "primary");
    padding: 5px 15px;
    border-radius: $container-height * 0.5;
    margin: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: map.get(theme.$border, "width") solid map.get(theme.$color, "primary");
    transition: transform animations.$fast-animation-speed, box-shadow animations.$fast-animation-speed;

    &:hover {
        box-shadow: 0 0 5px map.get(theme.$color, "primary");
    }
    & .letterSpace {
        letter-spacing: $letter-spacing;
        display: inline-block;
        transform: translate($letter-spacing * 0.5, 1px);
    }
}

.userDropdown {
    // & button[class~="MuiButton-root"] {
    //   border-radius: 12px;
    //   border: map.get(theme.$border, "width") solid map.get(theme.$color, "primary");
    //   &::before {
    //     content: none;
    //   }
    // }
    button[class~="MuiButton-root"] {
        border-radius: 12px !important;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .userName {
        font-size: 0.8rem;
        max-width: 100px;
        letter-spacing: 1px;
        padding-right: 1px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &>span:nth-child(2) {
            @include viewport.within("mobile") {
                display: none;
            }
        }
    }
}