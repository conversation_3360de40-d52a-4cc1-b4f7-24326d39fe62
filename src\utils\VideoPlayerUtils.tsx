const parseVideoSource = (videoUrl: string): string => {
    const regex = /(youtube|tiktok|youku|vimeo|youtu\\.?be)/i;
    const match = videoUrl.match(regex);

    if (match) {
        if (match[0] === "youtu.be") {
            return "youtube";
        }
        return match[0];
    }

    return "";
};

const getBannerVideoThumbnailUrl = (src: string): string => {
    const _default = "";
    const source = parseVideoSource(src);
    switch (source?.toLowerCase()) {
        case 'youtube': {
            const videoId = src.substring(9).split("/").at(2) || src.split("be/").at(1) || src.split("v=").at(1);
            return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        }
        default: {
            return _default;
        }
    }
};
  
export { getBannerVideoThumbnailUrl, parseVideoSource };