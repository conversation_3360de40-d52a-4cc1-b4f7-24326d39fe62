import styles from "./accordion.module.scss";
import classNames from "classnames";

import MUIAccordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { ReactNode } from "react";

interface AccordionProps {
    className?: string;
    children: ReactNode;
    title: string;
    defaultExpanded?: boolean;
    expanded?: boolean;
    disabled?: boolean;
    disableGutters?: boolean;
}

const Accordion = (props: AccordionProps) => {
    const {
        className,
        children,
        title,
        defaultExpanded,
        expanded,
        disabled,
        disableGutters,
    } = props;

    return (<>
        <MUIAccordion
            classes={{ root: classNames(styles.accordionRoot, className) }}
            defaultExpanded={defaultExpanded}
            expanded={expanded}
            disabled={disabled}
            disableGutters={disableGutters}
        >
            <AccordionSummary
                expandIcon={<ExpandMoreIcon className={styles.expandIcon} />}
                className={classNames(styles.accordionSummary, styles.title)}
            >
                {title}
            </AccordionSummary>

            <AccordionDetails>
                {children}
            </AccordionDetails>
        </MUIAccordion>
    </>);
};

export default Accordion;