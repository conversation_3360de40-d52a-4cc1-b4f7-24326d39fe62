import classNames from "classnames";
import Markdown from "markdown-to-jsx";
import React, { Fragment, ReactNode, useMemo } from "react";
import Accordion from "@/components/_CommonElements/Accordion";
import AutoOpenedModal from "@/components/ModalWindow/AutoOpened";
import DetailMore from "@/components/_CommonElements/Display/DetailMore";
import styles from "./section.module.scss";
import { List, ListItem as MUIListItemType, Paper, Typography } from "@mui/material";

interface SectionLabelProps {
    size?: "small" | "medium" | "large" | "xlarge"
    children: string;
    className: string;
    innerPadding?: number;
}
interface SectionContentProps {
    content: string | ReactNode;
    className: string;
}
interface Props {
    containerSize?: "wide" | "thin" | "tight"
    labelSize?: SectionLabelProps["size"]
    label?: string;
    content?: string | ReactNode;
    className?: {
        root?: string;
        label?: string;
        content?: string;
    },
    id?:string;
}

const SectionLabel = (props: SectionLabelProps) => {
    const { children, size, className, innerPadding } = props;
    return (
        <Typography component="div" sx={(innerPadding) && {
            padding: (innerPadding > 0) ? `0 ${innerPadding}rem` : "0"
        } || {}} className={classNames(styles.label,
            {
                [styles.small]: size === "small",
                [styles.medium]: size === "medium",
                [styles.large]: size === "large",
                [styles.xlarge]: size === "xlarge"
            }, className)}>
            <span>{children}</span>
        </Typography>
    );
}
interface ListItemProps {
    children: ReactNode;
}
const ListItem = (props: ListItemProps) => {
    const { children } = props;
    return (
        <div className={styles.listItem}>{children}</div>
    );
}

interface MUIListProps {
    children: ReactNode;
    listType?: string;
}
const MUIList = (props: MUIListProps) => {
    const { children, listType } = props;
    const _listType = listType ?? 'disc';
    return (
        <List sx={{
            listStyle: _listType,
            listStyleType: _listType,
            listStylePosition: 'inside',
            pl: '1rem'
        }}>{children}</List>
    );
}

interface MUIListItemProps {
    children: ReactNode;
    listType?: string;
}
const MUIListItem = (props: MUIListItemProps) => {
    const { children, listType } = props;
    const _listType = listType ?? 'disc';
    return (
        <MUIListItemType disablePadding sx={{
            listStyle: _listType,
            listStyleType: _listType,
            display: 'list-item'
        }}>{children}</MUIListItemType>
    );
}

interface ParagraphProps {
    children: ReactNode;
}
const Paragraph = (props: ParagraphProps) => {
    const { children } = props;
    return <div className={styles.paragraph}>{children}</div>
};
const SafeLink = (props: React.HTMLProps<HTMLAnchorElement>) => {
    const { rel, target, ...restProps } = props;
    const safeRel = useMemo(() => {
        if (!rel) {
            return "noopener";
        }
        const relValues = rel.split(" ");
        if (relValues.find(value => value === "noopener")) {
            return rel;
        }
        return `${rel} noopener`;
    }, [rel]);
    return <a rel={safeRel} target="_blank" {...restProps} />;
};
const SectionContent = (props: SectionContentProps) => {
    const { content, className } = props;
    const Content = useMemo(() => {
        if (typeof content === "string") {
            return (
                <div className={styles.markdown}>
                    <Markdown
                        options={{
                            wrapper: Fragment,
                            overrides: {
                                a: {
                                    component: SafeLink,
                                },
                                p: {
                                    component: Paragraph
                                },
                                h1: {
                                    component: SectionLabel,
                                    props: {
                                        size: "xlarge"
                                    }
                                },
                                h2: {
                                    component: SectionLabel,
                                    props: {
                                        size: "large"
                                    }
                                },
                                h3: {
                                    component: SectionLabel,
                                    props: {
                                        size: "medium"
                                    }
                                },
                                h4: {
                                    component: SectionLabel,
                                    props: {
                                        size: "small"
                                    }
                                },
                                Accordion: {
                                    component: Accordion,
                                },
                                SectionLabel: {
                                    component: SectionLabel,
                                },
                                ListItem: {
                                    component: ListItem
                                },
                                Paragraph: {
                                    component: Paragraph
                                },
                                Modal: {
                                    component: AutoOpenedModal
                                },
                                More: {
                                    component: DetailMore
                                },
                                List: {
                                    component: MUIList
                                },
                                MUIListItem: {
                                    component: MUIListItem
                                },
                                Paper: {
                                    component: Paper
                                },
                            }
                        }}
                    >
                        {content}
                    </Markdown>
                </div>
            );
        }
        return content;
    }, [content]);
    return (
        <div className={classNames(styles.content, className)}>
            {Content}
        </div>
    );
};
const Section = React.memo((props: Props) => {
    const { containerSize = "thin", labelSize = "medium", label, content, className = {},id } = props;
    return (
        <section
            className={classNames(
                styles.section,
                className.root,
                {
                    [styles.wideSection]: containerSize === "wide",
                    [styles.thinSection]: containerSize === "thin",
                    [styles.tightSection]: containerSize === "tight"
                }
            )}
            id={id}
        >
            { label && <SectionLabel size={labelSize} className={className.label || ""}>{label}</SectionLabel> }
            { content && <SectionContent className={className.content || ""} content={content} /> }
        </section>
    );
});
Section.displayName = "Section";
export default Section;