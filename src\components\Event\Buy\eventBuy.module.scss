@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

$tablet-header-height: map.get(theme.$height, "tablet-header");
$desktop-header-height: map.get(theme.$height, "desktop-header");
$background-color: map.get(theme.$color, "background");
$primary-color: map.get(theme.$color, "primary");
$secondary-color: map.get(theme.$color, "secondary");

.label {
    font-size: 0.7rem !important;
    background-color: $primary-color;
    color: white;
    padding: 4px 8px;
    border-radius: 16px;
}
div[class~="MuiMenu-paper"] .optionValue
{
    padding-left: 8px;
}