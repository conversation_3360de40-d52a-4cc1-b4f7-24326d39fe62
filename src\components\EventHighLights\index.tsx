import { useCallback } from "react";
import Link from "next/link";
import { Card, CardMedia } from "@mui/material";
import Carousel from "react-multi-carousel";

import RouteMap from "@/constants/config/RouteMap";
import EnumEventPreviewPosition from "@/constants/enum/EventPreviewPosition";
import { EventPreviewType } from "@/constants/enum/EventPreviewType";
import Event from "@/models/api/models/Event";

import "react-multi-carousel/lib/styles.css";
import styles from "@/components/EventHighLights/eventHighLights.module.scss";

interface EventHighLightsProps {
    events: Event[]
}

const EventHighLights = (props: EventHighLightsProps) => {
    const { events } = props;

    const responsive = {
        desktop: {
            breakpoint: { max: 3000, min: 1024 },
            items: 1
        },
        tablet: {
            breakpoint: { max: 1024, min: 464 },
            items: 1
        },
        mobile: {
            breakpoint: { max: 464, min: 0 },
            items: 1
        }
    };

    const banners = useCallback((event: Event) => {
        const banners = event.previewList.filter(preview => preview.position === EnumEventPreviewPosition.THUMBNAIL);
        const banner = banners.length > 0 && banners.find((banner) => banner.previewType === EventPreviewType.JPEG) ? banners.find((banner) => banner.previewType === EventPreviewType.JPEG) : undefined;
        return (
            <div key={event.eventName} className={styles.highLightCarouselSliderItem}>
                {banner ? 
                    <Card sx={{ position: "relative" }} className={styles.boundTickets}>
                        <div className={styles.imageHeader}>
                            <Link target="_blank" href={`${RouteMap.Event}/${event.eventId}`}>
                                <CardMedia
                                    component="img"
                                    image={banner.previewContent}
                                    alt={event.eventName}
                                    className={styles.eventBannerImage}
                                />
                            </Link>
                            <div className={styles.eventBannerImageBlurredBackground} style={{ backgroundImage: `url(${encodeURI(banner.previewContent)})` }}></div>
                        </div>
                    </Card>
                :
                null
                }
            </div>
        );
    }, []);

    return (
        <div>
            <Carousel 
                responsive={responsive} 
                autoPlay={true}
                showDots={false}
                infinite={true}
                partialVisible={true}
                containerClass={styles.highLightCarouselContainer}
                sliderClass={styles.highLightCarouselSlider}
            >
                {events.map((event) => banners(event))}
            </Carousel>
        </div>
    );
};

export default EventHighLights;