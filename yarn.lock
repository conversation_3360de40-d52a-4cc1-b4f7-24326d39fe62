# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@aashutoshrathi/word-wrap@npm:^1.2.3":
  version: 1.2.6
  resolution: "@aashutoshrathi/word-wrap@npm:1.2.6"
  checksum: ada901b9e7c680d190f1d012c84217ce0063d8f5c5a7725bb91ec3c5ed99bb7572680eb2d2938a531ccbaec39a95422fcd8a6b4a13110c7d98dd75402f66a0cd
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.2.1
  resolution: "@ampproject/remapping@npm:2.2.1"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.0
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 03c04fd526acc64a1f4df22651186f3e5ef0a9d6d6530ce4482ec9841269cf7a11dbb8af79237c282d721c5312024ff17529cd72cc4768c11e999b58e2302079
  languageName: node
  linkType: hard

"@babel/cli@npm:^7.21.0":
  version: 7.23.0
  resolution: "@babel/cli@npm:7.23.0"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.17
    "@nicolo-ribaudo/chokidar-2": 2.1.8-no-fsevents.3
    chokidar: ^3.4.0
    commander: ^4.0.1
    convert-source-map: ^2.0.0
    fs-readdir-recursive: ^1.1.0
    glob: ^7.2.0
    make-dir: ^2.1.0
    slash: ^2.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  dependenciesMeta:
    "@nicolo-ribaudo/chokidar-2":
      optional: true
    chokidar:
      optional: true
  bin:
    babel: ./bin/babel.js
    babel-external-helpers: ./bin/babel-external-helpers.js
  checksum: beeb189560bf9c4ea951ef637eefa5214654678fb09c4aaa6695921037059c1e1553c610fe95fbd19a9cdfd9f5598a812fc13df40a6b9a9ea899e43fc6c42052
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.22.13":
  version: 7.22.13
  resolution: "@babel/code-frame@npm:7.22.13"
  dependencies:
    "@babel/highlight": ^7.22.13
    chalk: ^2.4.2
  checksum: 22e342c8077c8b77eeb11f554ecca2ba14153f707b85294fcf6070b6f6150aae88a7b7436dd88d8c9289970585f3fe5b9b941c5aa3aa26a6d5a8ef3f292da058
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.20.5, @babel/compat-data@npm:^7.22.20, @babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.22.9":
  version: 7.22.20
  resolution: "@babel/compat-data@npm:7.22.20"
  checksum: efedd1d18878c10fde95e4d82b1236a9aba41395ef798cbb651f58dbf5632dbff475736c507b8d13d4c8f44809d41c0eb2ef0d694283af9ba5dd8339b6dab451
  languageName: node
  linkType: hard

"@babel/core@npm:^7.21.0":
  version: 7.23.0
  resolution: "@babel/core@npm:7.23.0"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.22.13
    "@babel/generator": ^7.23.0
    "@babel/helper-compilation-targets": ^7.22.15
    "@babel/helper-module-transforms": ^7.23.0
    "@babel/helpers": ^7.23.0
    "@babel/parser": ^7.23.0
    "@babel/template": ^7.22.15
    "@babel/traverse": ^7.23.0
    "@babel/types": ^7.23.0
    convert-source-map: ^2.0.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.3
    semver: ^6.3.1
  checksum: cebd9b48dbc970a7548522f207f245c69567e5ea17ebb1a4e4de563823cf20a01177fe8d2fe19b6e1461361f92fa169fd0b29f8ee9d44eeec84842be1feee5f2
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/generator@npm:7.23.0"
  dependencies:
    "@babel/types": ^7.23.0
    "@jridgewell/gen-mapping": ^0.3.2
    "@jridgewell/trace-mapping": ^0.3.17
    jsesc: ^2.5.1
  checksum: 8efe24adad34300f1f8ea2add420b28171a646edc70f2a1b3e1683842f23b8b7ffa7e35ef0119294e1901f45bfea5b3dc70abe1f10a1917ccdfb41bed69be5f1
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-annotate-as-pure@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 53da330f1835c46f26b7bf4da31f7a496dee9fd8696cca12366b94ba19d97421ce519a74a837f687749318f94d1a37f8d1abcbf35e8ed22c32d16373b2f6198d
  languageName: node
  linkType: hard

"@babel/helper-builder-binary-assignment-operator-visitor@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-builder-binary-assignment-operator-visitor@npm:7.22.15"
  dependencies:
    "@babel/types": ^7.22.15
  checksum: 639c697a1c729f9fafa2dd4c9af2e18568190299b5907bd4c2d0bc818fcbd1e83ffeecc2af24327a7faa7ac4c34edd9d7940510a5e66296c19bad17001cf5c7a
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.20.7, @babel/helper-compilation-targets@npm:^7.22.15, @babel/helper-compilation-targets@npm:^7.22.5, @babel/helper-compilation-targets@npm:^7.22.6":
  version: 7.22.15
  resolution: "@babel/helper-compilation-targets@npm:7.22.15"
  dependencies:
    "@babel/compat-data": ^7.22.9
    "@babel/helper-validator-option": ^7.22.15
    browserslist: ^4.21.9
    lru-cache: ^5.1.1
    semver: ^6.3.1
  checksum: ce85196769e091ae54dd39e4a80c2a9df1793da8588e335c383d536d54f06baf648d0a08fc873044f226398c4ded15c4ae9120ee18e7dfd7c639a68e3cdc9980
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.18.6, @babel/helper-create-class-features-plugin@npm:^7.22.11, @babel/helper-create-class-features-plugin@npm:^7.22.15, @babel/helper-create-class-features-plugin@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-create-class-features-plugin@npm:7.22.15"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-member-expression-to-functions": ^7.22.15
    "@babel/helper-optimise-call-expression": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 52c500d8d164abb3a360b1b7c4b8fff77bc4a5920d3a2b41ae6e1d30617b0dc0b972c1f5db35b1752007e04a748908b4a99bc872b73549ae837e87dcdde005a3
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.22.15"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    regexpu-core: ^5.3.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 0243b8d4854f1dc8861b1029a46d3f6393ad72f366a5a08e36a4648aa682044f06da4c6e87a456260e1e1b33c999f898ba591a0760842c1387bcc93fbf2151a6
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.4.2":
  version: 0.4.2
  resolution: "@babel/helper-define-polyfill-provider@npm:0.4.2"
  dependencies:
    "@babel/helper-compilation-targets": ^7.22.6
    "@babel/helper-plugin-utils": ^7.22.5
    debug: ^4.1.1
    lodash.debounce: ^4.0.8
    resolve: ^1.14.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 1f6dec0c5d0876d278fe15b71238eccc5f74c4e2efa2c78aaafa8bc2cc96336b8e68d94cd1a78497356c96e8b91b8c1f4452179820624d1702aee2f9832e6569
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.22.20, @babel/helper-environment-visitor@npm:^7.22.5":
  version: 7.22.20
  resolution: "@babel/helper-environment-visitor@npm:7.22.20"
  checksum: d80ee98ff66f41e233f36ca1921774c37e88a803b2f7dca3db7c057a5fea0473804db9fb6729e5dbfd07f4bed722d60f7852035c2c739382e84c335661590b69
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.22.5, @babel/helper-function-name@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/helper-function-name@npm:7.23.0"
  dependencies:
    "@babel/template": ^7.22.15
    "@babel/types": ^7.23.0
  checksum: e44542257b2d4634a1f979244eb2a4ad8e6d75eb6761b4cfceb56b562f7db150d134bc538c8e6adca3783e3bc31be949071527aa8e3aab7867d1ad2d84a26e10
  languageName: node
  linkType: hard

"@babel/helper-hoist-variables@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-hoist-variables@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 394ca191b4ac908a76e7c50ab52102669efe3a1c277033e49467913c7ed6f7c64d7eacbeabf3bed39ea1f41731e22993f763b1edce0f74ff8563fd1f380d92cc
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.22.15":
  version: 7.23.0
  resolution: "@babel/helper-member-expression-to-functions@npm:7.23.0"
  dependencies:
    "@babel/types": ^7.23.0
  checksum: 494659361370c979ada711ca685e2efe9460683c36db1b283b446122596602c901e291e09f2f980ecedfe6e0f2bd5386cb59768285446530df10c14df1024e75
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.16.7, @babel/helper-module-imports@npm:^7.18.6, @babel/helper-module-imports@npm:^7.22.15, @babel/helper-module-imports@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-module-imports@npm:7.22.15"
  dependencies:
    "@babel/types": ^7.22.15
  checksum: ecd7e457df0a46f889228f943ef9b4a47d485d82e030676767e6a2fdcbdaa63594d8124d4b55fd160b41c201025aec01fc27580352b1c87a37c9c6f33d116702
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.22.5, @babel/helper-module-transforms@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/helper-module-transforms@npm:7.23.0"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.20
    "@babel/helper-module-imports": ^7.22.15
    "@babel/helper-simple-access": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    "@babel/helper-validator-identifier": ^7.22.20
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 6e2afffb058cf3f8ce92f5116f710dda4341c81cfcd872f9a0197ea594f7ce0ab3cb940b0590af2fe99e60d2e5448bfba6bca8156ed70a2ed4be2adc8586c891
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-optimise-call-expression@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: c70ef6cc6b6ed32eeeec4482127e8be5451d0e5282d5495d5d569d39eb04d7f1d66ec99b327f45d1d5842a9ad8c22d48567e93fc502003a47de78d122e355f7c
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.20.2, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.8.0, @babel/helper-plugin-utils@npm:^7.8.3":
  version: 7.22.5
  resolution: "@babel/helper-plugin-utils@npm:7.22.5"
  checksum: c0fc7227076b6041acd2f0e818145d2e8c41968cc52fb5ca70eed48e21b8fe6dd88a0a91cbddf4951e33647336eb5ae184747ca706817ca3bef5e9e905151ff5
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.22.5, @babel/helper-remap-async-to-generator@npm:^7.22.9":
  version: 7.22.20
  resolution: "@babel/helper-remap-async-to-generator@npm:7.22.20"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-environment-visitor": ^7.22.20
    "@babel/helper-wrap-function": ^7.22.20
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 2fe6300a6f1b58211dffa0aed1b45d4958506d096543663dba83bd9251fe8d670fa909143a65b45e72acb49e7e20fbdb73eae315d9ddaced467948c3329986e7
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.22.5, @babel/helper-replace-supers@npm:^7.22.9":
  version: 7.22.20
  resolution: "@babel/helper-replace-supers@npm:7.22.20"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.20
    "@babel/helper-member-expression-to-functions": ^7.22.15
    "@babel/helper-optimise-call-expression": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a0008332e24daedea2e9498733e3c39b389d6d4512637e000f96f62b797e702ee24a407ccbcd7a236a551590a38f31282829a8ef35c50a3c0457d88218cae639
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-simple-access@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: fe9686714caf7d70aedb46c3cce090f8b915b206e09225f1e4dbc416786c2fdbbee40b38b23c268b7ccef749dd2db35f255338fb4f2444429874d900dede5ad2
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 1012ef2295eb12dc073f2b9edf3425661e9b8432a3387e62a8bc27c42963f1f216ab3124228015c748770b2257b4f1fda882ca8fa34c0bf485e929ae5bc45244
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/helper-split-export-declaration@npm:7.22.6"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: e141cace583b19d9195f9c2b8e17a3ae913b7ee9b8120246d0f9ca349ca6f03cb2c001fd5ec57488c544347c0bb584afec66c936511e447fd20a360e591ac921
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-string-parser@npm:7.22.5"
  checksum: 836851ca5ec813077bbb303acc992d75a360267aa3b5de7134d220411c852a6f17de7c0d0b8c8dcc0f567f67874c00f4528672b2a4f1bc978a3ada64c8c78467
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-validator-identifier@npm:7.22.20"
  checksum: 136412784d9428266bcdd4d91c32bcf9ff0e8d25534a9d94b044f77fe76bc50f941a90319b05aafd1ec04f7d127cd57a179a3716009ff7f3412ef835ada95bdc
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/helper-validator-option@npm:7.22.15"
  checksum: 68da52b1e10002a543161494c4bc0f4d0398c8fdf361d5f7f4272e95c45d5b32d974896d44f6a0ea7378c9204988879d73613ca683e13bd1304e46d25ff67a8d
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-wrap-function@npm:7.22.20"
  dependencies:
    "@babel/helper-function-name": ^7.22.5
    "@babel/template": ^7.22.15
    "@babel/types": ^7.22.19
  checksum: 221ed9b5572612aeb571e4ce6a256f2dee85b3c9536f1dd5e611b0255e5f59a3d0ec392d8d46d4152149156a8109f92f20379b1d6d36abb613176e0e33f05fca
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.23.0":
  version: 7.23.1
  resolution: "@babel/helpers@npm:7.23.1"
  dependencies:
    "@babel/template": ^7.22.15
    "@babel/traverse": ^7.23.0
    "@babel/types": ^7.23.0
  checksum: acfc345102045c24ea2a4d60e00dcf8220e215af3add4520e2167700661338e6a80bd56baf44bb764af05ec6621101c9afc315dc107e18c61fa6da8acbdbb893
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.22.13":
  version: 7.22.20
  resolution: "@babel/highlight@npm:7.22.20"
  dependencies:
    "@babel/helper-validator-identifier": ^7.22.20
    chalk: ^2.4.2
    js-tokens: ^4.0.0
  checksum: 84bd034dca309a5e680083cd827a766780ca63cef37308404f17653d32366ea76262bd2364b2d38776232f2d01b649f26721417d507e8b4b6da3e4e739f6d134
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.22.15, @babel/parser@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/parser@npm:7.23.0"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 453fdf8b9e2c2b7d7b02139e0ce003d1af21947bbc03eb350fb248ee335c9b85e4ab41697ddbdd97079698de825a265e45a0846bb2ed47a2c7c1df833f42a354
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 8910ca21a7ec7c06f7b247d4b86c97c5aa15ef321518f44f6f490c5912fdf82c605aaa02b90892e375d82ccbedeadfdeadd922c1b836c9dd4c596871bf654753
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/plugin-transform-optional-chaining": ^7.22.15
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: fbefedc0da014c37f1a50a8094ce7dbbf2181ae93243f23d6ecba2499b5b20196c2124d6a4dfe3e9e0125798e80593103e456352a4beb4e5c6f7c75efb80fdac
  languageName: node
  linkType: hard

"@babel/plugin-external-helpers@npm:^7.18.6":
  version: 7.22.5
  resolution: "@babel/plugin-external-helpers@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3cd44da2eff95a83ea3d763737c0fb3ed92fcaf534c120bb550e064b1c9c70c3f45366b65b096584f0fbe759b2225860ff824108398e4fa7c76b041b2f529397
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-properties@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-class-properties@npm:7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 49a78a2773ec0db56e915d9797e44fd079ab8a9b2e1716e0df07c92532f2c65d76aeda9543883916b8e0ff13606afeffa67c5b93d05b607bc87653ad18a91422
  languageName: node
  linkType: hard

"@babel/plugin-proposal-object-rest-spread@npm:^7.20.7":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-object-rest-spread@npm:7.20.7"
  dependencies:
    "@babel/compat-data": ^7.20.5
    "@babel/helper-compilation-targets": ^7.20.7
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-transform-parameters": ^7.20.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1329db17009964bc644484c660eab717cb3ca63ac0ab0f67c651a028d1bc2ead51dc4064caea283e46994f1b7221670a35cbc0b4beb6273f55e915494b5aa0b2
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d97745d098b835d55033ff3a7fb2b895b9c5295b08a5759e4f20df325aa385a3e0bc9bd5ad8f2ec554a44d4e6525acfc257b8c5848a1345cb40f26a30e277e91
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": ^7.12.13
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3e80814b5b6d4fe17826093918680a351c2d34398a914ce6e55d8083d72a9bdde4fbaf6a2dcea0e23a03de26dc2917ae3efd603d27099e2b98380345703bf948
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ce307af83cf433d4ec42932329fad25fa73138ab39c7436882ea28742e1c0066626d224e0ad2988724c82644e41601cef607b36194f695cb78a1fcdc959637bd
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-namespace-from@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-export-namespace-from@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85740478be5b0de185228e7814451d74ab8ce0a26fcca7613955262a26e99e8e15e9da58f60c754b84515d4c679b590dbd3f2148f0f58025f4ae706f1c5a5d4a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2b8b5572db04a7bef1e6cd20debf447e4eef7cb012616f5eceb8fa3e23ce469b8f76ee74fd6d1e158ba17a8f58b0aec579d092fb67c5a30e83ccfbc5754916c1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 197b3c5ea2a9649347f033342cb222ab47f4645633695205c0250c6bf2af29e643753b8bb24a2db39948bef08e7c540babfd365591eb57fc110cb30b425ffc47
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 166ac1125d10b9c0c430e4156249a13858c0366d38844883d75d27389621ebe651115cb2ceb6dc011534d5055719fa1727b59f39e1ab3ca97820eef3dcab5b9b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-jsx@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8829d30c2617ab31393d99cec2978e41f014f4ac6f01a1cecf4c4dd8320c3ec12fdc3ce121126b2d8d32f6887e99ca1a0bad53dedb1e6ad165640b92b24980ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aff33577037e34e515911255cdbb1fd39efee33658aa00b8a5fd3a4b903585112d037cce1cc9e4632f0487dc554486106b79ccd5ea63a2e00df4363f6d4ff886
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87aca4918916020d1fedba54c0e232de408df2644a425d153be368313fdde40d96088feed6c4e5ab72aac89be5d07fef2ddf329a15109c5eb65df006bf2580d1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 01ec5547bd0497f76cc903ff4d6b02abc8c05f301c88d2622b6d834e33a5651aa7c7a3d80d8d57656a4588f7276eba357f6b7e006482f5b564b7a6488de493a1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: eef94d53a1453361553c1f98b68d17782861a04a392840341bc91780838dd4e695209c783631cf0de14c635758beafb6a3a65399846ffa4386bff90639347f30
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b317174783e6e96029b743ccff2a67d63d38756876e7e5d0ba53a322e38d9ca452c13354a57de1ad476b4c066dbae699e0ca157441da611117a47af88985ecda
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbd1a56b095be7820029b209677b194db9b1d26691fe999856462e66b25b281f031f3dfd91b1619e9dcf95bebe336211833b854d0fb8780d618e35667c2d0d7e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-typescript@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8ab7718fbb026d64da93681a57797d60326097fd7cb930380c8bffd9eb101689e90142c760a14b51e8e69c88a73ba3da956cb4520a3b0c65743aee5c71ef360a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 35abb6c57062802c7ce8bd96b2ef2883e3124370c688bbd67609f7d2453802fb73944df8808f893b6c67de978eb2bcf87bbfe325e46d6f39b5fcb09ece11d01a
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.22.15"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-remap-async-to-generator": ^7.22.9
    "@babel/plugin-syntax-async-generators": ^7.8.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fad98786b446ce63bde0d14a221e2617eef5a7bbca62b49d96f16ab5e1694521234cfba6145b830fbf9af16d60a8a3dbf148e8694830bd91796fe333b0599e73
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.22.5"
  dependencies:
    "@babel/helper-module-imports": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-remap-async-to-generator": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b95f23f99dcb379a9f0a1c2a3bbea3f8dc0e1b16dc1ac8b484fe378370169290a7a63d520959a9ba1232837cf74a80e23f6facbe14fd42a3cda6d3c2d7168e62
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 416b1341858e8ca4e524dee66044735956ced5f478b2c3b9bc11ec2285b0c25d7dbb96d79887169eb938084c95d0a89338c8b2fe70d473bd9dc92e5d9db1732c
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.22.15":
  version: 7.23.0
  resolution: "@babel/plugin-transform-block-scoping@npm:7.23.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0cfe925cc3b5a3ad407e2253fab3ceeaa117a4b291c9cb245578880872999bca91bd83ffa0128ae9ca356330702e1ef1dcb26804f28d2cef678239caf629f73e
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-class-properties@npm:7.22.5"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b830152dfc2ff2f647f0abe76e6251babdfbef54d18c4b2c73a6bf76b1a00050a5d998dac80dc901a48514e95604324943a9dd39317073fe0928b559e0e0c579
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-class-static-block@npm:7.22.11"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.11
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-class-static-block": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 69f040506fad66f1c6918d288d0e0edbc5c8a07c8b4462c1184ad2f9f08995d68b057126c213871c0853ae0c72afc60ec87492049dfacb20902e32346a448bcb
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-classes@npm:7.22.15"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-compilation-targets": ^7.22.15
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-optimise-call-expression": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.9
    "@babel/helper-split-export-declaration": ^7.22.6
    globals: ^11.1.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d3f4d0c107dd8a3557ea3575cc777fab27efa92958b41e4a9822f7499725c1f554beae58855de16ddec0a7b694e45f59a26cea8fbde4275563f72f09c6e039a0
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-computed-properties@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/template": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c2a77a0f94ec71efbc569109ec14ea2aa925b333289272ced8b33c6108bdbb02caf01830ffc7e49486b62dec51911924d13f3a76f1149f40daace1898009e131
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.22.15":
  version: 7.23.0
  resolution: "@babel/plugin-transform-destructuring@npm:7.23.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cd6dd454ccc2766be551e4f8a04b1acc2aa539fa19e5c7501c56cc2f8cc921dd41a7ffb78455b4c4b2f954fcab8ca4561ba7c9c7bd5af9f19465243603d18cc3
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 409b658d11e3082c8f69e9cdef2d96e4d6d11256f005772425fb230cc48fd05945edbfbcb709dab293a1a2f01f9c8a5bb7b4131e632b23264039d9f95864b453
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bb1280fbabaab6fab2ede585df34900712698210a3bd413f4df5bae6d8c24be36b496c92722ae676a7a67d060a4624f4d6c23b923485f906bfba8773c69f55b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 78fc9c532210bf9e8f231747f542318568ac360ee6c27e80853962c984283c73da3f8f8aebe83c2096090a435b356b092ed85de617a156cbe0729d847632be45
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.22.5"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f2d660c1b1d51ad5fec1cd5ad426a52187204068c4158f8c4aa977b31535c61b66898d532603eef21c15756827be8277f724c869b888d560f26d7fe848bb5eae
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 73af5883a321ed56a4bfd43c8a7de0164faebe619287706896fc6ee2f7a4e69042adaa1338c0b8b4bdb9f7e5fdceb016fb1d40694cb43ca3b8827429e8aac4bf
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-for-of@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f395ae7bce31e14961460f56cf751b5d6e37dd27d7df5b1f4e49fec1c11b6f9cf71991c7ffbe6549878591e87df0d66af798cf26edfa4bfa6b4c3dba1fb2f73a
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-function-name@npm:7.22.5"
  dependencies:
    "@babel/helper-compilation-targets": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cff3b876357999cb8ae30e439c3ec6b0491a53b0aa6f722920a4675a6dd5b53af97a833051df4b34791fe5b3dd326ccf769d5c8e45b322aa50ee11a660b17845
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-json-strings@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-json-strings": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 50665e5979e66358c50e90a26db53c55917f78175127ac2fa05c7888d156d418ffb930ec0a109353db0a7c5f57c756ce01bfc9825d24cbfd2b3ec453f2ed8cba
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ec37cc2ffb32667af935ab32fe28f00920ec8a1eb999aa6dc6602f2bebd8ba205a558aeedcdccdebf334381d5c57106c61f52332045730393e73410892a9735b
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c664e9798e85afa7f92f07b867682dee7392046181d82f5d21bae6f2ca26dfe9c8375cdc52b7483c3fc09a983c1989f60eff9fbc4f373b0c0a74090553d05739
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ec4b0e07915ddd4fda0142fd104ee61015c208608a84cfa13643a95d18760b1dc1ceb6c6e0548898b8c49e5959a994e46367260176dbabc4467f729b21868504
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.22.5":
  version: 7.23.0
  resolution: "@babel/plugin-transform-modules-amd@npm:7.23.0"
  dependencies:
    "@babel/helper-module-transforms": ^7.23.0
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5d92875170a37b8282d4bcd805f55829b8fab0f9c8d08b53d32a7a0bfdc62b868e489b52d329ae768ecafc0c993eed0ad7a387baa673ac33211390a9f833ab5d
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.22.15, @babel/plugin-transform-modules-commonjs@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.23.0"
  dependencies:
    "@babel/helper-module-transforms": ^7.23.0
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-simple-access": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7fb25997194053e167c4207c319ff05362392da841bd9f42ddb3caf9c8798a5d203bd926d23ddf5830fdf05eddc82c2810f40d1287e3a4f80b07eff13d1024b5
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.22.11":
  version: 7.23.0
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.23.0"
  dependencies:
    "@babel/helper-hoist-variables": ^7.22.5
    "@babel/helper-module-transforms": ^7.23.0
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-identifier": ^7.22.20
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2d481458b22605046badea2317d5cc5c94ac3031c2293e34c96f02063f5b02af0979c4da6a8fbc67cc249541575dc9c6d710db6b919ede70b7337a22d9fd57a7
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-modules-umd@npm:7.22.5"
  dependencies:
    "@babel/helper-module-transforms": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 46622834c54c551b231963b867adbc80854881b3e516ff29984a8da989bd81665bd70e8cba6710345248e97166689310f544aee1a5773e262845a8f1b3e5b8b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 3ee564ddee620c035b928fdc942c5d17e9c4b98329b76f9cefac65c111135d925eb94ed324064cd7556d4f5123beec79abea1d4b97d1c8a2a5c748887a2eb623
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-new-target@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6b72112773487a881a1d6ffa680afde08bad699252020e86122180ee7a88854d5da3f15d9bca3331cf2e025df045604494a8208a2e63b486266b07c14e2ffbf3
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 167babecc8b8fe70796a7b7d34af667ebbf43da166c21689502e5e8cc93180b7a85979c77c9f64b7cce431b36718bd0a6df9e5e0ffea4ae22afb22cfef886372
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: af064d06a4a041767ec396a5f258103f64785df290e038bba9f0ef454e6c914f2ac45d862bbdad8fac2c7ad47fa4e95356f29053c60c100a0160b02a995fe2a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.22.15"
  dependencies:
    "@babel/compat-data": ^7.22.9
    "@babel/helper-compilation-targets": ^7.22.15
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-transform-parameters": ^7.22.15
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 62197a6f12289c1c1bd57f3bed9f0f765ca32390bfe91e0b5561dd94dd9770f4480c4162dec98da094bc0ba99d2c2ebba68de47c019454041b0b7a68ba2ec66d
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-object-super@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b71887877d74cb64dbccb5c0324fa67e31171e6a5311991f626650e44a4083e5436a1eaa89da78c0474fb095d4ec322d63ee778b202d33aa2e4194e1ed8e62d7
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f17abd90e1de67c84d63afea29c8021c74abb2794d3a6eeafb0bbe7372d3db32aefca386e392116ec63884537a4a2815d090d26264d259bacc08f6e3ed05294c
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.22.15":
  version: 7.23.0
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.23.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f702634f2b97e5260dbec0d4bde05ccb6f4d96d7bfa946481aeacfa205ca846cb6e096a38312f9d51fdbdac1f258f211138c5f7075952e46a5bf8574de6a1329
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.20.7, @babel/plugin-transform-parameters@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-parameters@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 541188bb7d1876cad87687b5c7daf90f63d8208ae83df24acb1e2b05020ad1c78786b2723ca4054a83fcb74fb6509f30c4cacc5b538ee684224261ad5fb047c1
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-private-methods@npm:7.22.5"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 321479b4fcb6d3b3ef622ab22fd24001e43d46e680e8e41324c033d5810c84646e470f81b44cbcbef5c22e99030784f7cac92f1829974da7a47a60a7139082c3
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.22.11"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-create-class-features-plugin": ^7.22.11
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4d029d84901e53c46dead7a46e2990a7bc62470f4e4ca58a0d063394f86652fd58fe4eea1eb941da3669cd536b559b9d058b342b59300026346b7a2a51badac8
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-property-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 796176a3176106f77fcb8cd04eb34a8475ce82d6d03a88db089531b8f0453a2fb8b0c6ec9a52c27948bc0ea478becec449893741fc546dfc3930ab927e3f9f2e
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-display-name@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a12bfd1e4e93055efca3ace3c34722571bda59d9740dca364d225d9c6e3ca874f134694d21715c42cc63d79efd46db9665bd4a022998767f9245f1e29d5d204d
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.22.5"
  dependencies:
    "@babel/plugin-transform-react-jsx": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 36bc3ff0b96bb0ef4723070a50cfdf2e72cfd903a59eba448f9fe92fea47574d6f22efd99364413719e1f3fb3c51b6c9b2990b87af088f8486a84b2a5f9e4560
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.22.15, @babel/plugin-transform-react-jsx@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/plugin-transform-react-jsx@npm:7.22.15"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-module-imports": ^7.22.15
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-jsx": ^7.22.5
    "@babel/types": ^7.22.15
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3899054e89550c3a0ef041af7c47ee266e2e934f498ee80fefeda778a6aa177b48aa8b4d2a8bf5848de977fec564571699ab952d9fa089c4c19b45ddb121df09
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.22.5"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 092021c4f404e267002099ec20b3f12dd730cb90b0d83c5feed3dc00dbe43b9c42c795a18e7c6c7d7bddea20c7dd56221b146aec81b37f2e7eb5137331c61120
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.22.10":
  version: 7.22.10
  resolution: "@babel/plugin-transform-regenerator@npm:7.22.10"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    regenerator-transform: ^0.15.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e13678d62d6fa96f11cb8b863f00e8693491e7adc88bfca3f2820f80cbac8336e7dec3a596eee6a1c4663b7ececc3564f2cd7fb44ed6d4ce84ac2bb7f39ecc6e
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-reserved-words@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3ffd7dbc425fe8132bfec118b9817572799cab1473113a635d25ab606c1f5a2341a636c04cf6b22df3813320365ed5a965b5eeb3192320a10e4cc2c137bd8bfc
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a5ac902c56ea8effa99f681340ee61bac21094588f7aef0bc01dff98246651702e677552fa6d10e548c4ac22a3ffad047dd2f8c8f0540b68316c2c203e56818b
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-spread@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5587f0deb60b3dfc9b274e269031cc45ec75facccf1933ea2ea71ced9fd3ce98ed91bb36d6cd26817c14474b90ed998c5078415f0eab531caf301496ce24c95c
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 63b2c575e3e7f96c32d52ed45ee098fb7d354b35c2223b8c8e76840b32cc529ee0c0ceb5742fd082e56e91e3d82842a367ce177e82b05039af3d602c9627a729
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-template-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 27e9bb030654cb425381c69754be4abe6a7c75b45cd7f962cd8d604b841b2f0fb7b024f2efc1c25cc53f5b16d79d5e8cfc47cacbdaa983895b3aeefa3e7e24ff
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 82a53a63ffc3010b689ca9a54e5f53b2718b9f4b4a9818f36f9b7dba234f38a01876680553d2716a645a61920b5e6e4aaf8d4a0064add379b27ca0b403049512
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-typescript@npm:7.22.15"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-create-class-features-plugin": ^7.22.15
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-typescript": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c5d96cdbf0e1512707aa1c1e3ac6b370a25fd9c545d26008ce44eb13a47bd7fd67a1eb799c98b5ccc82e33a345fda55c0055e1fe3ed97646ed405dd13020b226
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.22.10":
  version: 7.22.10
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.22.10"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 807f40ed1324c8cb107c45358f1903384ca3f0ef1d01c5a3c5c9b271c8d8eec66936a3dcc8d75ddfceea9421420368c2e77ae3adef0a50557e778dfe296bf382
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2495e5f663cb388e3d888b4ba3df419ac436a5012144ac170b622ddfc221f9ea9bdba839fa2bc0185cb776b578030666406452ec7791cbf0e7a3d4c88ae9574c
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6b5d1404c8c623b0ec9bd436c00d885a17d6a34f3f2597996343ddb9d94f6379705b21582dfd4cec2c47fd34068872e74ab6b9580116c0566b3f9447e2a7fa06
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: c042070f980b139547f8b0179efbc049ac5930abec7fc26ed7a41d89a048d8ab17d362200e204b6f71c3c20d6991a0e74415e1a412a49adc8131c2a40c04822e
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.20.2":
  version: 7.22.20
  resolution: "@babel/preset-env@npm:7.22.20"
  dependencies:
    "@babel/compat-data": ^7.22.20
    "@babel/helper-compilation-targets": ^7.22.15
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-option": ^7.22.15
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": ^7.22.15
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": ^7.22.15
    "@babel/plugin-proposal-private-property-in-object": 7.21.0-placeholder-for-preset-env.2
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-class-properties": ^7.12.13
    "@babel/plugin-syntax-class-static-block": ^7.14.5
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
    "@babel/plugin-syntax-import-assertions": ^7.22.5
    "@babel/plugin-syntax-import-attributes": ^7.22.5
    "@babel/plugin-syntax-import-meta": ^7.10.4
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
    "@babel/plugin-syntax-top-level-await": ^7.14.5
    "@babel/plugin-syntax-unicode-sets-regex": ^7.18.6
    "@babel/plugin-transform-arrow-functions": ^7.22.5
    "@babel/plugin-transform-async-generator-functions": ^7.22.15
    "@babel/plugin-transform-async-to-generator": ^7.22.5
    "@babel/plugin-transform-block-scoped-functions": ^7.22.5
    "@babel/plugin-transform-block-scoping": ^7.22.15
    "@babel/plugin-transform-class-properties": ^7.22.5
    "@babel/plugin-transform-class-static-block": ^7.22.11
    "@babel/plugin-transform-classes": ^7.22.15
    "@babel/plugin-transform-computed-properties": ^7.22.5
    "@babel/plugin-transform-destructuring": ^7.22.15
    "@babel/plugin-transform-dotall-regex": ^7.22.5
    "@babel/plugin-transform-duplicate-keys": ^7.22.5
    "@babel/plugin-transform-dynamic-import": ^7.22.11
    "@babel/plugin-transform-exponentiation-operator": ^7.22.5
    "@babel/plugin-transform-export-namespace-from": ^7.22.11
    "@babel/plugin-transform-for-of": ^7.22.15
    "@babel/plugin-transform-function-name": ^7.22.5
    "@babel/plugin-transform-json-strings": ^7.22.11
    "@babel/plugin-transform-literals": ^7.22.5
    "@babel/plugin-transform-logical-assignment-operators": ^7.22.11
    "@babel/plugin-transform-member-expression-literals": ^7.22.5
    "@babel/plugin-transform-modules-amd": ^7.22.5
    "@babel/plugin-transform-modules-commonjs": ^7.22.15
    "@babel/plugin-transform-modules-systemjs": ^7.22.11
    "@babel/plugin-transform-modules-umd": ^7.22.5
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.22.5
    "@babel/plugin-transform-new-target": ^7.22.5
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.22.11
    "@babel/plugin-transform-numeric-separator": ^7.22.11
    "@babel/plugin-transform-object-rest-spread": ^7.22.15
    "@babel/plugin-transform-object-super": ^7.22.5
    "@babel/plugin-transform-optional-catch-binding": ^7.22.11
    "@babel/plugin-transform-optional-chaining": ^7.22.15
    "@babel/plugin-transform-parameters": ^7.22.15
    "@babel/plugin-transform-private-methods": ^7.22.5
    "@babel/plugin-transform-private-property-in-object": ^7.22.11
    "@babel/plugin-transform-property-literals": ^7.22.5
    "@babel/plugin-transform-regenerator": ^7.22.10
    "@babel/plugin-transform-reserved-words": ^7.22.5
    "@babel/plugin-transform-shorthand-properties": ^7.22.5
    "@babel/plugin-transform-spread": ^7.22.5
    "@babel/plugin-transform-sticky-regex": ^7.22.5
    "@babel/plugin-transform-template-literals": ^7.22.5
    "@babel/plugin-transform-typeof-symbol": ^7.22.5
    "@babel/plugin-transform-unicode-escapes": ^7.22.10
    "@babel/plugin-transform-unicode-property-regex": ^7.22.5
    "@babel/plugin-transform-unicode-regex": ^7.22.5
    "@babel/plugin-transform-unicode-sets-regex": ^7.22.5
    "@babel/preset-modules": 0.1.6-no-external-plugins
    "@babel/types": ^7.22.19
    babel-plugin-polyfill-corejs2: ^0.4.5
    babel-plugin-polyfill-corejs3: ^0.8.3
    babel-plugin-polyfill-regenerator: ^0.5.2
    core-js-compat: ^3.31.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 99357a5cb30f53bacdc0d1cd6dff0f052ea6c2d1ba874d969bba69897ef716e87283e84a59dc52fb49aa31fd1b6f55ed756c64c04f5678380700239f6030b881
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/types": ^7.4.4
    esutils: ^2.0.2
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 4855e799bc50f2449fb5210f78ea9e8fd46cf4f242243f1e2ed838e2bd702e25e73e822e7f8447722a5f4baa5e67a8f7a0e403f3e7ce04540ff743a9c411c375
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.18.6":
  version: 7.22.15
  resolution: "@babel/preset-react@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-option": ^7.22.15
    "@babel/plugin-transform-react-display-name": ^7.22.5
    "@babel/plugin-transform-react-jsx": ^7.22.15
    "@babel/plugin-transform-react-jsx-development": ^7.22.5
    "@babel/plugin-transform-react-pure-annotations": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c3ef99dfa2e9f57d2e08603e883aa20f47630a826c8e413888a93ae6e0084b5016871e463829be125329d40a1ba0a89f7c43d77b6dab52083c225cb43e63d10e
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.21.0":
  version: 7.23.0
  resolution: "@babel/preset-typescript@npm:7.23.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-option": ^7.22.15
    "@babel/plugin-syntax-jsx": ^7.22.5
    "@babel/plugin-transform-modules-commonjs": ^7.23.0
    "@babel/plugin-transform-typescript": ^7.22.15
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3d5fce83e83f11c07e0ea13542bca181abb3b482b8981ec9c64e6add9d7beed3c54d063dc4bc9fd383165c71114a245abef89a289680833c5a8552fe3e7c4407
  languageName: node
  linkType: hard

"@babel/regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "@babel/regjsgen@npm:0.8.0"
  checksum: 89c338fee774770e5a487382170711014d49a68eb281e74f2b5eac88f38300a4ad545516a7786a8dd5702e9cf009c94c2f582d200f077ac5decd74c56b973730
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.1.2, @babel/runtime@npm:^7.11.2, @babel/runtime@npm:^7.12.1, @babel/runtime@npm:^7.12.5, @babel/runtime@npm:^7.18.3, @babel/runtime@npm:^7.20.13, @babel/runtime@npm:^7.20.7, @babel/runtime@npm:^7.21.0, @babel/runtime@npm:^7.22.15, @babel/runtime@npm:^7.22.5, @babel/runtime@npm:^7.23.1, @babel/runtime@npm:^7.5.5, @babel/runtime@npm:^7.6.2, @babel/runtime@npm:^7.7.2, @babel/runtime@npm:^7.8.4, @babel/runtime@npm:^7.8.7, @babel/runtime@npm:^7.9.2":
  version: 7.23.1
  resolution: "@babel/runtime@npm:7.23.1"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: 0cd0d43e6e7dc7f9152fda8c8312b08321cda2f56ef53d6c22ebdd773abdc6f5d0a69008de90aa41908d00e2c1facb24715ff121274e689305c858355ff02c70
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.23.2, @babel/runtime@npm:^7.3.1, @babel/runtime@npm:^7.8.3":
  version: 7.23.2
  resolution: "@babel/runtime@npm:7.23.2"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: 6c4df4839ec75ca10175f636d6362f91df8a3137f86b38f6cd3a4c90668a0fe8e9281d320958f4fbd43b394988958585a17c3aab2a4ea6bf7316b22916a371fb
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.24.4, @babel/runtime@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/runtime@npm:7.26.0"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: c8e2c0504ab271b3467a261a8f119bf2603eb857a0d71e37791f4e3fae00f681365073cc79f141ddaa90c6077c60ba56448004ad5429d07ac73532be9f7cf28a
  languageName: node
  linkType: hard

"@babel/template@npm:^7.22.15, @babel/template@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/template@npm:7.22.15"
  dependencies:
    "@babel/code-frame": ^7.22.13
    "@babel/parser": ^7.22.15
    "@babel/types": ^7.22.15
  checksum: 1f3e7dcd6c44f5904c184b3f7fe280394b191f2fed819919ffa1e529c259d5b197da8981b6ca491c235aee8dbad4a50b7e31304aa531271cb823a4a24a0dd8fd
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.21.2, @babel/traverse@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/traverse@npm:7.23.0"
  dependencies:
    "@babel/code-frame": ^7.22.13
    "@babel/generator": ^7.23.0
    "@babel/helper-environment-visitor": ^7.22.20
    "@babel/helper-function-name": ^7.23.0
    "@babel/helper-hoist-variables": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    "@babel/parser": ^7.23.0
    "@babel/types": ^7.23.0
    debug: ^4.1.0
    globals: ^11.1.0
  checksum: 0b17fae53269e1af2cd3edba00892bc2975ad5df9eea7b84815dab07dfec2928c451066d51bc65b4be61d8499e77db7e547ce69ef2a7b0eca3f96269cb43a0b0
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.22.15, @babel/types@npm:^7.22.19, @babel/types@npm:^7.22.5, @babel/types@npm:^7.23.0, @babel/types@npm:^7.4.4, @babel/types@npm:^7.8.3":
  version: 7.23.0
  resolution: "@babel/types@npm:7.23.0"
  dependencies:
    "@babel/helper-string-parser": ^7.22.5
    "@babel/helper-validator-identifier": ^7.22.20
    to-fast-properties: ^2.0.0
  checksum: 215fe04bd7feef79eeb4d33374b39909ce9cad1611c4135a4f7fdf41fe3280594105af6d7094354751514625ea92d0875aba355f53e86a92600f290e77b0e604
  languageName: node
  linkType: hard

"@corex/deepmerge@npm:^4.0.43":
  version: 4.0.43
  resolution: "@corex/deepmerge@npm:4.0.43"
  checksum: f0103bfcfb9938ba478868fad522b018b92615ba186b0322c92aa1f57e539b87d4379d88fee3a404264a83bd4b13c384cfb07834377997d6d71cd840a880b4c4
  languageName: node
  linkType: hard

"@date-fns/tz@npm:^1.1.2":
  version: 1.2.0
  resolution: "@date-fns/tz@npm:1.2.0"
  checksum: 2eb37e63fdbd5d72458c528ded6c2f7bdb76bae9c6875d3b827e33600b807cfbd72ab05c9291f258a6e6b7b360ec03e57f7e7d85fe6e0d5e989476dec908910e
  languageName: node
  linkType: hard

"@easylive-show/devtools-detector@npm:2.0.15":
  version: 2.0.15
  resolution: "@easylive-show/devtools-detector@npm:2.0.15"
  dependencies:
    compare-versions: ^3.6.0
  checksum: 55790ff3c7d3210f9fe808ef6f6b902b40042cf17226a90780225fab22703141c9b866f888ee6a1a77a1611fabcb0602a9c1412f23b98b13ec228bfffe588585
  languageName: node
  linkType: hard

"@easylive-show/react-phone-input@npm:^2.14.10":
  version: 2.14.10
  resolution: "@easylive-show/react-phone-input@npm:2.14.10"
  dependencies:
    classnames: ^2.2.6
    lodash.debounce: ^4.0.8
    lodash.memoize: ^4.1.2
    lodash.reduce: ^4.6.0
    lodash.startswith: ^4.2.1
    prop-types: ^15.7.2
  peerDependencies:
    react: ^16.12.0 || ^17.0.0
    react-dom: ^16.12.0 || ^17.0.0
  checksum: 909849991eabb3b6857f9522f07361ed814ae55298c0c95cb293ea70018c1e63846070de1f866ef210ce49e929d41074c351327f48d4a9372f02f91fbfd47b69
  languageName: node
  linkType: hard

"@easylive-show/video-player@npm:^1.3.11":
  version: 1.4.10
  resolution: "@easylive-show/video-player@npm:1.4.10"
  dependencies:
    "@easylive-show/devtools-detector": 2.0.15
    "@easylive-show/videojs-resolution-switcher": ^1.2.12
    classnames: ^2.3.1
    video.js: ^7.17.0
    videojs-contextmenu-ui: ^5.2.0
    videojs-contrib-quality-levels: ^2.1.0
  peerDependencies:
    sass: ^1.43.4
  checksum: 8474b6b11ce9dec58c229cf51096ed18d05958573f42b1cecff8ac04b705acab3a8ba57a79aee9a3354ba95cd99e6c270e471ed33bfeb8eeb096a15f5ad353b3
  languageName: node
  linkType: hard

"@easylive-show/videojs-hls-quality-selector@npm:1.1.5":
  version: 1.1.5
  resolution: "@easylive-show/videojs-hls-quality-selector@npm:1.1.5"
  dependencies:
    global: ^4.3.2
    karma-safaritechpreview-launcher: 0.0.6
    video.js: ^7.5.5
    videojs-contrib-quality-levels: ^2.0.9
  checksum: 4240f536fa931f4bcc13a2b57f974dc71f88bf4f7252900113daa4c16d10859e725ac0ba90aec9f1b3d34b96644cf0bd6d5d8e90b63016138ba77463ab71f2ef
  languageName: node
  linkType: hard

"@easylive-show/videojs-plugin-contextmenu@npm:^1.0.3":
  version: 1.0.3
  resolution: "@easylive-show/videojs-plugin-contextmenu@npm:1.0.3"
  dependencies:
    global: ^4.4.0
  checksum: 632c745dd8874fdae4c42c8aa84f6edc35676965698a2a6ba179ea8367778df2ad8f585f777920dd0ac5c20108df67dc42aea02c2631af03d976bb19a834bda8
  languageName: node
  linkType: hard

"@easylive-show/videojs-resolution-switcher@npm:^1.2.12":
  version: 1.2.12
  resolution: "@easylive-show/videojs-resolution-switcher@npm:1.2.12"
  dependencies:
    global: ^4.4.0
    video.js: ^7.6.0
  checksum: 804f8b23aa52770e2b341e6294182f710f5b281cbf1e04acb31ae40548857696a17e91715b5cf72a075ca19621b11128333ffa33fe7a17c220c5292353f78ca1
  languageName: node
  linkType: hard

"@emotion/babel-plugin@npm:^11.11.0":
  version: 11.11.0
  resolution: "@emotion/babel-plugin@npm:11.11.0"
  dependencies:
    "@babel/helper-module-imports": ^7.16.7
    "@babel/runtime": ^7.18.3
    "@emotion/hash": ^0.9.1
    "@emotion/memoize": ^0.8.1
    "@emotion/serialize": ^1.1.2
    babel-plugin-macros: ^3.1.0
    convert-source-map: ^1.5.0
    escape-string-regexp: ^4.0.0
    find-root: ^1.1.0
    source-map: ^0.5.7
    stylis: 4.2.0
  checksum: 6b363edccc10290f7a23242c06f88e451b5feb2ab94152b18bb8883033db5934fb0e421e2d67d09907c13837c21218a3ac28c51707778a54d6cd3706c0c2f3f9
  languageName: node
  linkType: hard

"@emotion/cache@npm:^11.11.0, @emotion/cache@npm:^11.4.0":
  version: 11.11.0
  resolution: "@emotion/cache@npm:11.11.0"
  dependencies:
    "@emotion/memoize": ^0.8.1
    "@emotion/sheet": ^1.2.2
    "@emotion/utils": ^1.2.1
    "@emotion/weak-memoize": ^0.3.1
    stylis: 4.2.0
  checksum: 8eb1dc22beaa20c21a2e04c284d5a2630a018a9d51fb190e52de348c8d27f4e8ca4bbab003d68b4f6cd9cc1c569ca747a997797e0f76d6c734a660dc29decf08
  languageName: node
  linkType: hard

"@emotion/cache@npm:^11.13.1":
  version: 11.13.1
  resolution: "@emotion/cache@npm:11.13.1"
  dependencies:
    "@emotion/memoize": ^0.9.0
    "@emotion/sheet": ^1.4.0
    "@emotion/utils": ^1.4.0
    "@emotion/weak-memoize": ^0.4.0
    stylis: 4.2.0
  checksum: 94b161786a03a08a1e30257478fad9a9be1ac8585ddca0c6410d7411fd474fc8b0d6d1167d7d15bdb012d1fd8a1220ac2bbc79501ad9b292b83c17da0874d7de
  languageName: node
  linkType: hard

"@emotion/css@npm:^11.9.0":
  version: 11.11.2
  resolution: "@emotion/css@npm:11.11.2"
  dependencies:
    "@emotion/babel-plugin": ^11.11.0
    "@emotion/cache": ^11.11.0
    "@emotion/serialize": ^1.1.2
    "@emotion/sheet": ^1.2.2
    "@emotion/utils": ^1.2.1
  checksum: 1edea109dfc31005243334bc351ba127220ea5c4986225e0f1b8f7aa71fb2f83fb8f51d8f5b8afb8432d4c6397c23f5061038449f2876888eecc6eac1dd2f0da
  languageName: node
  linkType: hard

"@emotion/hash@npm:^0.9.1":
  version: 0.9.1
  resolution: "@emotion/hash@npm:0.9.1"
  checksum: 716e17e48bf9047bf9383982c071de49f2615310fb4e986738931776f5a823bc1f29c84501abe0d3df91a3803c80122d24e28b57351bca9e01356ebb33d89876
  languageName: node
  linkType: hard

"@emotion/hash@npm:^0.9.2":
  version: 0.9.2
  resolution: "@emotion/hash@npm:0.9.2"
  checksum: 379bde2830ccb0328c2617ec009642321c0e009a46aa383dfbe75b679c6aea977ca698c832d225a893901f29d7b3eef0e38cf341f560f6b2b56f1ff23c172387
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^0.8.2":
  version: 0.8.8
  resolution: "@emotion/is-prop-valid@npm:0.8.8"
  dependencies:
    "@emotion/memoize": 0.7.4
  checksum: bb7ec6d48c572c540e24e47cc94fc2f8dec2d6a342ae97bc9c8b6388d9b8d283862672172a1bb62d335c02662afe6291e10c71e9b8642664a8b43416cdceffac
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^1.2.1":
  version: 1.2.1
  resolution: "@emotion/is-prop-valid@npm:1.2.1"
  dependencies:
    "@emotion/memoize": ^0.8.1
  checksum: 8f42dc573a3fad79b021479becb639b8fe3b60bdd1081a775d32388bca418ee53074c7602a4c845c5f75fa6831eb1cbdc4d208cc0299f57014ed3a02abcad16a
  languageName: node
  linkType: hard

"@emotion/memoize@npm:0.7.4":
  version: 0.7.4
  resolution: "@emotion/memoize@npm:0.7.4"
  checksum: 4e3920d4ec95995657a37beb43d3f4b7d89fed6caa2b173a4c04d10482d089d5c3ea50bbc96618d918b020f26ed6e9c4026bbd45433566576c1f7b056c3271dc
  languageName: node
  linkType: hard

"@emotion/memoize@npm:^0.8.1":
  version: 0.8.1
  resolution: "@emotion/memoize@npm:0.8.1"
  checksum: a19cc01a29fcc97514948eaab4dc34d8272e934466ed87c07f157887406bc318000c69ae6f813a9001c6a225364df04249842a50e692ef7a9873335fbcc141b0
  languageName: node
  linkType: hard

"@emotion/memoize@npm:^0.9.0":
  version: 0.9.0
  resolution: "@emotion/memoize@npm:0.9.0"
  checksum: 038132359397348e378c593a773b1148cd0cf0a2285ffd067a0f63447b945f5278860d9de718f906a74c7c940ba1783ac2ca18f1c06a307b01cc0e3944e783b1
  languageName: node
  linkType: hard

"@emotion/react@npm:^11.11.1":
  version: 11.11.1
  resolution: "@emotion/react@npm:11.11.1"
  dependencies:
    "@babel/runtime": ^7.18.3
    "@emotion/babel-plugin": ^11.11.0
    "@emotion/cache": ^11.11.0
    "@emotion/serialize": ^1.1.2
    "@emotion/use-insertion-effect-with-fallbacks": ^1.0.1
    "@emotion/utils": ^1.2.1
    "@emotion/weak-memoize": ^0.3.1
    hoist-non-react-statics: ^3.3.1
  peerDependencies:
    react: ">=16.8.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: aec3c36650f5f0d3d4445ff44d73dd88712b1609645b6af3e6d08049cfbc51f1785fe13dea1a1d4ab1b0800d68f2339ab11e459687180362b1ef98863155aae5
  languageName: node
  linkType: hard

"@emotion/serialize@npm:^1.1.2":
  version: 1.1.2
  resolution: "@emotion/serialize@npm:1.1.2"
  dependencies:
    "@emotion/hash": ^0.9.1
    "@emotion/memoize": ^0.8.1
    "@emotion/unitless": ^0.8.1
    "@emotion/utils": ^1.2.1
    csstype: ^3.0.2
  checksum: 413c352e657f1b5e27ea6437b3ef7dcc3860669b7ae17fd5c18bfbd44e033af1acc56b64d252284a813ca4f3b3e1b0841c42d3fb08e02d2df56fd3cd63d72986
  languageName: node
  linkType: hard

"@emotion/serialize@npm:^1.3.2":
  version: 1.3.2
  resolution: "@emotion/serialize@npm:1.3.2"
  dependencies:
    "@emotion/hash": ^0.9.2
    "@emotion/memoize": ^0.9.0
    "@emotion/unitless": ^0.10.0
    "@emotion/utils": ^1.4.1
    csstype: ^3.0.2
  checksum: 8051bafe32459e1aecf716cdb66a22b090060806104cca89d4e664893b56878d3e9bb94a4657df9b7b3fd183700a9be72f7144c959ddcbd3cf7b330206919237
  languageName: node
  linkType: hard

"@emotion/server@npm:^11.4.0":
  version: 11.11.0
  resolution: "@emotion/server@npm:11.11.0"
  dependencies:
    "@emotion/utils": ^1.2.1
    html-tokenize: ^2.0.0
    multipipe: ^1.0.2
    through: ^2.3.8
  peerDependencies:
    "@emotion/css": ^11.0.0-rc.0
  peerDependenciesMeta:
    "@emotion/css":
      optional: true
  checksum: 2130b51390a6ba4375ec3223c9e4fb476ba30973910ec5196a048d1f832ba2ad23e51eee261058fbd7c3c78938fc3d83b01c3a951e8b6fa83d40f2ac4047c317
  languageName: node
  linkType: hard

"@emotion/sheet@npm:^1.2.2":
  version: 1.2.2
  resolution: "@emotion/sheet@npm:1.2.2"
  checksum: d973273c9c15f1c291ca2269728bf044bd3e92a67bca87943fa9ec6c3cd2b034f9a6bfe95ef1b5d983351d128c75b547b43ff196a00a3875f7e1d269793cecfe
  languageName: node
  linkType: hard

"@emotion/sheet@npm:^1.4.0":
  version: 1.4.0
  resolution: "@emotion/sheet@npm:1.4.0"
  checksum: eeb1212e3289db8e083e72e7e401cd6d1a84deece87e9ce184f7b96b9b5dbd6f070a89057255a6ff14d9865c3ce31f27c39248a053e4cdd875540359042586b4
  languageName: node
  linkType: hard

"@emotion/styled@npm:^11.11.0":
  version: 11.11.0
  resolution: "@emotion/styled@npm:11.11.0"
  dependencies:
    "@babel/runtime": ^7.18.3
    "@emotion/babel-plugin": ^11.11.0
    "@emotion/is-prop-valid": ^1.2.1
    "@emotion/serialize": ^1.1.2
    "@emotion/use-insertion-effect-with-fallbacks": ^1.0.1
    "@emotion/utils": ^1.2.1
  peerDependencies:
    "@emotion/react": ^11.0.0-rc.0
    react: ">=16.8.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 904f641aad3892c65d7d6c0808b036dae1e6d6dad4861c1c7dc0baa59977047c6cad220691206eba7b4059f1a1c6e6c1ef4ebb8c829089e280fa0f2164a01e6b
  languageName: node
  linkType: hard

"@emotion/unitless@npm:^0.10.0":
  version: 0.10.0
  resolution: "@emotion/unitless@npm:0.10.0"
  checksum: d79346df31a933e6d33518e92636afeb603ce043f3857d0a39a2ac78a09ef0be8bedff40130930cb25df1beeee12d96ee38613963886fa377c681a89970b787c
  languageName: node
  linkType: hard

"@emotion/unitless@npm:^0.8.0, @emotion/unitless@npm:^0.8.1":
  version: 0.8.1
  resolution: "@emotion/unitless@npm:0.8.1"
  checksum: 385e21d184d27853bb350999471f00e1429fa4e83182f46cd2c164985999d9b46d558dc8b9cc89975cb337831ce50c31ac2f33b15502e85c299892e67e7b4a88
  languageName: node
  linkType: hard

"@emotion/use-insertion-effect-with-fallbacks@npm:^1.0.1":
  version: 1.0.1
  resolution: "@emotion/use-insertion-effect-with-fallbacks@npm:1.0.1"
  peerDependencies:
    react: ">=16.8.0"
  checksum: 700b6e5bbb37a9231f203bb3af11295eed01d73b2293abece0bc2a2237015e944d7b5114d4887ad9a79776504aa51ed2a8b0ddbc117c54495dd01a6b22f93786
  languageName: node
  linkType: hard

"@emotion/utils@npm:^1.2.1":
  version: 1.2.1
  resolution: "@emotion/utils@npm:1.2.1"
  checksum: e0b44be0705b56b079c55faff93952150be69e79b660ae70ddd5b6e09fc40eb1319654315a9f34bb479d7f4ec94be6068c061abbb9e18b9778ae180ad5d97c73
  languageName: node
  linkType: hard

"@emotion/utils@npm:^1.4.0, @emotion/utils@npm:^1.4.1":
  version: 1.4.1
  resolution: "@emotion/utils@npm:1.4.1"
  checksum: 088f6844c735981f53c84a76101cf261422301e7895cb37fea6a47e7950247ffa8ca174ca2a15d9b29a47f0fa831b432017ca7683bccbb5cfd78dda82743d856
  languageName: node
  linkType: hard

"@emotion/weak-memoize@npm:^0.3.1":
  version: 0.3.1
  resolution: "@emotion/weak-memoize@npm:0.3.1"
  checksum: b2be47caa24a8122622ea18cd2d650dbb4f8ad37b636dc41ed420c2e082f7f1e564ecdea68122b546df7f305b159bf5ab9ffee872abd0f052e687428459af594
  languageName: node
  linkType: hard

"@emotion/weak-memoize@npm:^0.4.0":
  version: 0.4.0
  resolution: "@emotion/weak-memoize@npm:0.4.0"
  checksum: db5da0e89bd752c78b6bd65a1e56231f0abebe2f71c0bd8fc47dff96408f7065b02e214080f99924f6a3bfe7ee15afc48dad999d76df86b39b16e513f7a94f52
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.0
  resolution: "@eslint-community/eslint-utils@npm:4.4.0"
  dependencies:
    eslint-visitor-keys: ^3.3.0
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: cdfe3ae42b4f572cbfb46d20edafe6f36fc5fb52bf2d90875c58aefe226892b9677fef60820e2832caf864a326fe4fc225714c46e8389ccca04d5f9288aabd22
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.5.1, @eslint-community/regexpp@npm:^4.6.1":
  version: 4.9.0
  resolution: "@eslint-community/regexpp@npm:4.9.0"
  checksum: 82411f0643ab9bfd271bf12c8c75031266b13595d9371585ee3b0d680d918d4abf37c7e94d0da22e45817c9bbc59b79dfcbd672050dfb00af88fb89c80fd420f
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.2":
  version: 2.1.2
  resolution: "@eslint/eslintrc@npm:2.1.2"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^9.6.0
    globals: ^13.19.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: bc742a1e3b361f06fedb4afb6bf32cbd27171292ef7924f61c62f2aed73048367bcc7ac68f98c06d4245cd3fabc43270f844e3c1699936d4734b3ac5398814a7
  languageName: node
  linkType: hard

"@eslint/js@npm:8.50.0":
  version: 8.50.0
  resolution: "@eslint/js@npm:8.50.0"
  checksum: 302478f2acaaa7228729ec6a04f56641590185e1d8cd1c836a6db8a6b8009f80a57349341be9fbb9aa1721a7a569d1be3ffc598a33300d22816f11832095386c
  languageName: node
  linkType: hard

"@fingerprintjs/fingerprintjs@npm:^3.3.0":
  version: 3.4.2
  resolution: "@fingerprintjs/fingerprintjs@npm:3.4.2"
  dependencies:
    tslib: ^2.4.1
  checksum: 3b9dc81e4186f1aaa39e208c17939f5747bf9a8eb1c8175264a352e46e263abd81bcf89439240bd1a4755d7e3dfb4a83164e294f940abc290e7f2076d3b603ce
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.4.2":
  version: 1.5.0
  resolution: "@floating-ui/core@npm:1.5.0"
  dependencies:
    "@floating-ui/utils": ^0.1.3
  checksum: 54b4fe26b3c228746ac5589f97303abf158b80aa5f8b99027259decd68d1c2030c4c637648ebd33dfe78a4212699453bc2bd7537fd5a594d3bd3e63d362f666f
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.6.0":
  version: 1.6.8
  resolution: "@floating-ui/core@npm:1.6.8"
  dependencies:
    "@floating-ui/utils": ^0.2.8
  checksum: 82faa6ea9d57e466779324e51308d6d49c098fb9d184a08d9bb7f4fad83f08cc070fc491f8d56f0cad44a16215fb43f9f829524288413e6c33afcb17303698de
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.0.0":
  version: 1.6.12
  resolution: "@floating-ui/dom@npm:1.6.12"
  dependencies:
    "@floating-ui/core": ^1.6.0
    "@floating-ui/utils": ^0.2.8
  checksum: 956514ed100c0c853e73ace9e3c877b7e535444d7c31326f687a7690d49cb1e59ef457e9c93b76141aea0d280e83ed5a983bb852718b62eea581f755454660f6
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.5.1":
  version: 1.5.3
  resolution: "@floating-ui/dom@npm:1.5.3"
  dependencies:
    "@floating-ui/core": ^1.4.2
    "@floating-ui/utils": ^0.1.3
  checksum: 00053742064aac70957f0bd5c1542caafb3bfe9716588bfe1d409fef72a67ed5e60450d08eb492a77f78c22ed1ce4f7955873cc72bf9f9caf2b0f43ae3561c21
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.0.2":
  version: 2.0.2
  resolution: "@floating-ui/react-dom@npm:2.0.2"
  dependencies:
    "@floating-ui/dom": ^1.5.1
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: 4797e1f7a19c1e531ed0d578ccdcbe58970743e5a480ba30424857fc953063f36d481f8c5d69248a8f1d521b739e94bf5e1ffb35506400dea3d914f166ed2f7f
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.0.8":
  version: 2.1.2
  resolution: "@floating-ui/react-dom@npm:2.1.2"
  dependencies:
    "@floating-ui/dom": ^1.0.0
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: 25bb031686e23062ed4222a8946e76b3f9021d40a48437bd747233c4964a766204b8a55f34fa8b259839af96e60db7c6e3714d81f1de06914294f90e86ffbc48
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.1.3":
  version: 0.1.4
  resolution: "@floating-ui/utils@npm:0.1.4"
  checksum: e6195ded5b3a6fd38411a833605184c31f24609b08feab2615e90ccc063bf4d3965383d817642fc7e8ca5ab6a54c29c71103e874f3afb0518595c8bd3390ba16
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.8":
  version: 0.2.8
  resolution: "@floating-ui/utils@npm:0.2.8"
  checksum: deb98bba017c4e073c7ad5740d4dec33a4d3e0942d412e677ac0504f3dade15a68fc6fd164d43c93c0bb0bcc5dc5015c1f4080dfb1a6161140fe660624f7c875
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.11.11":
  version: 0.11.11
  resolution: "@humanwhocodes/config-array@npm:0.11.11"
  dependencies:
    "@humanwhocodes/object-schema": ^1.2.1
    debug: ^4.1.1
    minimatch: ^3.0.5
  checksum: db84507375ab77b8ffdd24f498a5b49ad6b64391d30dd2ac56885501d03964d29637e05b1ed5aefa09d57ac667e28028bc22d2da872bfcd619652fbdb5f4ca19
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^1.2.1":
  version: 1.2.1
  resolution: "@humanwhocodes/object-schema@npm:1.2.1"
  checksum: a824a1ec31591231e4bad5787641f59e9633827d0a2eaae131a288d33c9ef0290bd16fda8da6f7c0fcb014147865d12118df10db57f27f41e20da92369fcb3f1
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.0, @jridgewell/gen-mapping@npm:^0.3.2":
  version: 0.3.3
  resolution: "@jridgewell/gen-mapping@npm:0.3.3"
  dependencies:
    "@jridgewell/set-array": ^1.0.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 4a74944bd31f22354fc01c3da32e83c19e519e3bbadafa114f6da4522ea77dd0c2842607e923a591d60a76699d819a2fbb6f3552e277efdb9b58b081390b60ab
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.1
  resolution: "@jridgewell/resolve-uri@npm:3.1.1"
  checksum: f5b441fe7900eab4f9155b3b93f9800a916257f4e8563afbcd3b5a5337b55e52bd8ae6735453b1b745457d9f6cdb16d74cd6220bbdd98cf153239e13f6cbb653
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.0.1":
  version: 1.1.2
  resolution: "@jridgewell/set-array@npm:1.1.2"
  checksum: 69a84d5980385f396ff60a175f7177af0b8da4ddb81824cb7016a9ef914eee9806c72b6b65942003c63f7983d4f39a5c6c27185bbca88eb4690b62075602e28e
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.4.15
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.15"
  checksum: b881c7e503db3fc7f3c1f35a1dd2655a188cc51a3612d76efc8a6eb74728bef5606e6758ee77423e564092b4a518aba569bbb21c9bac5ab7a35b0c6ae7e344c8
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.17, @jridgewell/trace-mapping@npm:^0.3.9":
  version: 0.3.19
  resolution: "@jridgewell/trace-mapping@npm:0.3.19"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 956a6f0f6fec060fb48c6bf1f5ec2064e13cd38c8be3873877d4b92b4a27ba58289a34071752671262a3e3c202abcc3fa2aac64d8447b4b0fa1ba3c9047f1c20
  languageName: node
  linkType: hard

"@mui/base@npm:5.0.0-beta.18, @mui/base@npm:^5.0.0-beta.18":
  version: 5.0.0-beta.18
  resolution: "@mui/base@npm:5.0.0-beta.18"
  dependencies:
    "@babel/runtime": ^7.23.1
    "@floating-ui/react-dom": ^2.0.2
    "@mui/types": ^7.2.5
    "@mui/utils": ^5.14.12
    "@popperjs/core": ^2.11.8
    clsx: ^2.0.0
    prop-types: ^15.8.1
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 7d4ca1e9d537b7b5850567f1adecd1caa47b8613b43a587cf2f399cfda0a8c17dfda06b030c0bea554b76abe7ac25bb9b1af3c996574def5f860cda0c6ea4a3c
  languageName: node
  linkType: hard

"@mui/base@npm:5.0.0-beta.42":
  version: 5.0.0-beta.42
  resolution: "@mui/base@npm:5.0.0-beta.42"
  dependencies:
    "@babel/runtime": ^7.24.4
    "@floating-ui/react-dom": ^2.0.8
    "@mui/types": ^7.2.14
    "@mui/utils": ^6.0.0-alpha.1
    "@popperjs/core": ^2.11.8
    clsx: ^2.1.0
    prop-types: ^15.8.1
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: f7af6e6003a4a8502062607b7861490eb6dc3a1f617532c92521981329ae077535444d65e378718bbf59ce8b7a52fc32275dc794969c73bdb389e5d69155fc69
  languageName: node
  linkType: hard

"@mui/base@npm:^5.0.0-beta.14":
  version: 5.0.0-beta.17
  resolution: "@mui/base@npm:5.0.0-beta.17"
  dependencies:
    "@babel/runtime": ^7.22.15
    "@floating-ui/react-dom": ^2.0.2
    "@mui/types": ^7.2.4
    "@mui/utils": ^5.14.11
    "@popperjs/core": ^2.11.8
    clsx: ^2.0.0
    prop-types: ^15.8.1
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 96ffb85864fc796783514e089c0011ebcc5174705082dc98197ab035dfb287427069c0338e662a4680951849dfbe7a5231f4f6f2aee710af05c07e8578f93310
  languageName: node
  linkType: hard

"@mui/core-downloads-tracker@npm:^5.14.12":
  version: 5.14.12
  resolution: "@mui/core-downloads-tracker@npm:5.14.12"
  checksum: 1c1576ceecf7cade9e0d7a531632f5f9db24853d9ebbd47bb9ed943a3af7de734ad4f3374bab79880e9591db3ea55ea84cc10df72177f9ca5e32cc7662e04405
  languageName: node
  linkType: hard

"@mui/icons-material@npm:5.x":
  version: 5.14.12
  resolution: "@mui/icons-material@npm:5.14.12"
  dependencies:
    "@babel/runtime": ^7.23.1
  peerDependencies:
    "@mui/material": ^5.0.0
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 55f147242afc47c50b4761e487c1d2a114d97384f94efe915a2ec6822431c0b6f5271118f918b8511a26d5dbd8e858c8ee2cbce622057ce89e3b00b3ea9829e2
  languageName: node
  linkType: hard

"@mui/lab@npm:^6.0.0-beta.15":
  version: 6.0.0-dev.240424162023-9968b4889d
  resolution: "@mui/lab@npm:6.0.0-dev.240424162023-9968b4889d"
  dependencies:
    "@babel/runtime": ^7.24.4
    "@mui/base": 5.0.0-beta.42
    "@mui/system": ^6.0.0-dev.240424162023-9968b4889d
    "@mui/types": ^7.2.14
    "@mui/utils": ^6.0.0-alpha.3
    clsx: ^2.1.0
    prop-types: ^15.8.1
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@mui/material": ^6.0.0-dev.240424162023-9968b4889d
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: 9eb3ff01720e8d4b0d921b50370de9177333c06d1082908bca3dcd5f3a7f0d1470d24d80a097d78618bed93ab3c00952d5329b9365ff8c0230f5282859bbe43b
  languageName: node
  linkType: hard

"@mui/material-nextjs@npm:^6.1.6":
  version: 6.1.6
  resolution: "@mui/material-nextjs@npm:6.1.6"
  dependencies:
    "@babel/runtime": ^7.26.0
  peerDependencies:
    "@emotion/cache": ^11.11.0
    "@emotion/react": ^11.11.4
    "@emotion/server": ^11.11.0
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    next: ^13.0.0 || ^14.0.0 || ^15.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/cache":
      optional: true
    "@emotion/server":
      optional: true
    "@types/react":
      optional: true
  checksum: d472b70f15c49dcf6f1a363da608b51669da01fc0c7d0b04aa420ac5524a574183bc78c6d2863a355b5be253a2e6ac1b63f3adee544098bce571886dca385b75
  languageName: node
  linkType: hard

"@mui/material@npm:5.x":
  version: 5.14.12
  resolution: "@mui/material@npm:5.14.12"
  dependencies:
    "@babel/runtime": ^7.23.1
    "@mui/base": 5.0.0-beta.18
    "@mui/core-downloads-tracker": ^5.14.12
    "@mui/system": ^5.14.12
    "@mui/types": ^7.2.5
    "@mui/utils": ^5.14.12
    "@types/react-transition-group": ^4.4.6
    clsx: ^2.0.0
    csstype: ^3.1.2
    prop-types: ^15.8.1
    react-is: ^18.2.0
    react-transition-group: ^4.4.5
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: a0d3b52ce3cc282da04036db0805f95f27b35a9c899f132f962fe96f05d3eb112e99ccbf6bd9d05cae617b24beda95470aedaff129d6e39d1b52e1ddf80a9e12
  languageName: node
  linkType: hard

"@mui/private-theming@npm:^5.14.12":
  version: 5.14.12
  resolution: "@mui/private-theming@npm:5.14.12"
  dependencies:
    "@babel/runtime": ^7.23.1
    "@mui/utils": ^5.14.12
    prop-types: ^15.8.1
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: f8127347dc29126fece3b530cb156f6ababf747b64bb1c712874375e6efae6c738c014304d9553001d67a59b24ca6a665f2d03bb5ae137f03bdba90815f0ecc1
  languageName: node
  linkType: hard

"@mui/private-theming@npm:^5.14.15":
  version: 5.14.15
  resolution: "@mui/private-theming@npm:5.14.15"
  dependencies:
    "@babel/runtime": ^7.23.2
    "@mui/utils": ^5.14.15
    prop-types: ^15.8.1
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: e4f1365d0610abfd1fbbbd55a3c2454858a133c3c23513b55239c8785ac84d4041b7003d7a9685e9f04e44d11d133e7249a7cc777a59ae137d0565b7b1ca95e8
  languageName: node
  linkType: hard

"@mui/private-theming@npm:^6.1.7":
  version: 6.1.7
  resolution: "@mui/private-theming@npm:6.1.7"
  dependencies:
    "@babel/runtime": ^7.26.0
    "@mui/utils": ^6.1.7
    prop-types: ^15.8.1
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 1b1b508ea6c92018b068bda6ff63f63ff0f479b8f6f97ffab13927f133ecdc3c5141c54f8d0cfb7e450697df8f91bc38c7643fc404cd0c90b45cc24c8b46e10f
  languageName: node
  linkType: hard

"@mui/styled-engine@npm:^5.14.12":
  version: 5.14.12
  resolution: "@mui/styled-engine@npm:5.14.12"
  dependencies:
    "@babel/runtime": ^7.23.1
    "@emotion/cache": ^11.11.0
    csstype: ^3.1.2
    prop-types: ^15.8.1
  peerDependencies:
    "@emotion/react": ^11.4.1
    "@emotion/styled": ^11.3.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
  checksum: c689ccad59e7fd54cd8367838612daa4f132c64d8c6b99ccb7c8f9697b5c940a6bf7edcccd686ce437b565dbcf3bfc12bb0dea47cbd5fbd750ea1553017f9c0d
  languageName: node
  linkType: hard

"@mui/styled-engine@npm:^6.1.7":
  version: 6.1.7
  resolution: "@mui/styled-engine@npm:6.1.7"
  dependencies:
    "@babel/runtime": ^7.26.0
    "@emotion/cache": ^11.13.1
    "@emotion/serialize": ^1.3.2
    "@emotion/sheet": ^1.4.0
    csstype: ^3.1.3
    prop-types: ^15.8.1
  peerDependencies:
    "@emotion/react": ^11.4.1
    "@emotion/styled": ^11.3.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
  checksum: f5df484ba94752083043ddba9e8d2ac1d7a13e0a94ee88c401d19b7ffdbd6712db41563ae82797f959bfb8bddb5d3da5cf7c66b43aca027dc64597da58cb9788
  languageName: node
  linkType: hard

"@mui/styles@npm:5.x":
  version: 5.14.15
  resolution: "@mui/styles@npm:5.14.15"
  dependencies:
    "@babel/runtime": ^7.23.2
    "@emotion/hash": ^0.9.1
    "@mui/private-theming": ^5.14.15
    "@mui/types": ^7.2.7
    "@mui/utils": ^5.14.15
    clsx: ^2.0.0
    csstype: ^3.1.2
    hoist-non-react-statics: ^3.3.2
    jss: ^10.10.0
    jss-plugin-camel-case: ^10.10.0
    jss-plugin-default-unit: ^10.10.0
    jss-plugin-global: ^10.10.0
    jss-plugin-nested: ^10.10.0
    jss-plugin-props-sort: ^10.10.0
    jss-plugin-rule-value-function: ^10.10.0
    jss-plugin-vendor-prefixer: ^10.10.0
    prop-types: ^15.8.1
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: d6674528b841d0eeb06891b065954eb1a9c99fd482510d30c681e68efbca398de9e1d6294eaece0765903a69ac6b0f620540289911ab90cb924c1233319159d6
  languageName: node
  linkType: hard

"@mui/system@npm:5.x, @mui/system@npm:^5.14.12":
  version: 5.14.12
  resolution: "@mui/system@npm:5.14.12"
  dependencies:
    "@babel/runtime": ^7.23.1
    "@mui/private-theming": ^5.14.12
    "@mui/styled-engine": ^5.14.12
    "@mui/types": ^7.2.5
    "@mui/utils": ^5.14.12
    clsx: ^2.0.0
    csstype: ^3.1.2
    prop-types: ^15.8.1
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: 70c3920eadc593395a2d258ddea0f3b28689c7f02fdaf97fc205e16efaeebe462b2ab01c69a20a3bcb011e0d07ea47fa66a433e70d0a1ce15d7b694fb3c52135
  languageName: node
  linkType: hard

"@mui/system@npm:^6.0.0-dev.240424162023-9968b4889d":
  version: 6.1.7
  resolution: "@mui/system@npm:6.1.7"
  dependencies:
    "@babel/runtime": ^7.26.0
    "@mui/private-theming": ^6.1.7
    "@mui/styled-engine": ^6.1.7
    "@mui/types": ^7.2.19
    "@mui/utils": ^6.1.7
    clsx: ^2.1.1
    csstype: ^3.1.3
    prop-types: ^15.8.1
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: 607432db5514a97138f42adde9da170af4cd39105fc34b206cfe61ccd1056cf4fde3bed3f475a4df85a8a12c683d4375aa6563b558991f518ceec55c0a7e2d13
  languageName: node
  linkType: hard

"@mui/types@npm:^7.2.14, @mui/types@npm:^7.2.19":
  version: 7.2.19
  resolution: "@mui/types@npm:7.2.19"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: c3b5723e6f0861d47df834c57878f19347aefecdaf948cf9a25a64b73fbc75791430693d0f540b2bdc01bdfc605dc32bf4ba738113ec415aa9eaf002ce38f064
  languageName: node
  linkType: hard

"@mui/types@npm:^7.2.4":
  version: 7.2.4
  resolution: "@mui/types@npm:7.2.4"
  peerDependencies:
    "@types/react": "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 16bea0547492193a22fd1794382f314698a114f6c673825314c66b56766c3a9d305992cc495684722b7be16a1ecf7e6e48a79caa64f90c439b530e8c02611a61
  languageName: node
  linkType: hard

"@mui/types@npm:^7.2.5":
  version: 7.2.5
  resolution: "@mui/types@npm:7.2.5"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 2807e9a8eb251294eee6384a4d68b2159f7660466625f1781e9efea282aa7c6ff35b42bc7039c2d43e7a5ac80291dcb85c4110022b0b6de4e12b6406b62f3dc1
  languageName: node
  linkType: hard

"@mui/types@npm:^7.2.7":
  version: 7.2.7
  resolution: "@mui/types@npm:7.2.7"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: cf6a770f6f62b662ba8049c830d9506a3a058b87255d33dcab47a70022496c6b82088ce9185558c4ce4744e369593d3c9b7734664ebb2e6e206fb5380b37e93f
  languageName: node
  linkType: hard

"@mui/utils@npm:^5.14.11, @mui/utils@npm:^5.14.8":
  version: 5.14.11
  resolution: "@mui/utils@npm:5.14.11"
  dependencies:
    "@babel/runtime": ^7.22.15
    "@types/prop-types": ^15.7.5
    prop-types: ^15.8.1
    react-is: ^18.2.0
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 84e640dea3589ea7edb7d8f33748d83d561d4ef413c4d3e2216ddf9c0842d8b04d162e3fa5ea59f03846934f17fb446ed8464e318dd4e2e299e3b44b06637d76
  languageName: node
  linkType: hard

"@mui/utils@npm:^5.14.12":
  version: 5.14.12
  resolution: "@mui/utils@npm:5.14.12"
  dependencies:
    "@babel/runtime": ^7.23.1
    "@types/prop-types": ^15.7.7
    prop-types: ^15.8.1
    react-is: ^18.2.0
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 41470b6292b7a46c71fb0a0acc6a5f05a5e080648106b8805555de920e8f748669c7e8d39cbbcf0f52be9053927bb8439a748e24bd02bc1a220c9bded4435f42
  languageName: node
  linkType: hard

"@mui/utils@npm:^5.14.15":
  version: 5.14.15
  resolution: "@mui/utils@npm:5.14.15"
  dependencies:
    "@babel/runtime": ^7.23.2
    "@types/prop-types": ^15.7.8
    prop-types: ^15.8.1
    react-is: ^18.2.0
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 6c4270110eed03a2e9073111eed01555257073ec500f85f6656c9f67ff15019bf12ce632d537e71a2d5872598b62f2d31762a11e1e40ff9e985b7fe2beb6c066
  languageName: node
  linkType: hard

"@mui/utils@npm:^6.0.0-alpha.1, @mui/utils@npm:^6.0.0-alpha.3, @mui/utils@npm:^6.1.7":
  version: 6.1.7
  resolution: "@mui/utils@npm:6.1.7"
  dependencies:
    "@babel/runtime": ^7.26.0
    "@mui/types": ^7.2.19
    "@types/prop-types": ^15.7.13
    clsx: ^2.1.1
    prop-types: ^15.8.1
    react-is: ^18.3.1
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: bfbbac129b9ff6626a21f1e74b390ea943c92c5756ab708c96f215083be3c5227d59ebea29b320c4b1bbde280160ebeb676b1fb996110197bd374765526f51a4
  languageName: node
  linkType: hard

"@mui/x-date-pickers@npm:^6.9.2":
  version: 6.15.0
  resolution: "@mui/x-date-pickers@npm:6.15.0"
  dependencies:
    "@babel/runtime": ^7.22.15
    "@mui/base": ^5.0.0-beta.14
    "@mui/utils": ^5.14.8
    "@types/react-transition-group": ^4.4.6
    clsx: ^2.0.0
    prop-types: ^15.8.1
    react-transition-group: ^4.4.5
  peerDependencies:
    "@emotion/react": ^11.9.0
    "@emotion/styled": ^11.8.1
    "@mui/material": ^5.8.6
    "@mui/system": ^5.8.0
    date-fns: ^2.25.0
    date-fns-jalali: ^2.13.0-0
    dayjs: ^1.10.7
    luxon: ^3.0.2
    moment: ^2.29.4
    moment-hijri: ^2.1.2
    moment-jalaali: ^0.7.4 || ^0.8.0 || ^0.9.0 || ^0.10.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    date-fns:
      optional: true
    date-fns-jalali:
      optional: true
    dayjs:
      optional: true
    luxon:
      optional: true
    moment:
      optional: true
    moment-hijri:
      optional: true
    moment-jalaali:
      optional: true
  checksum: c931bca8b4579c12a4fb7c0cd5bfd8d853ff5da51ebaf2e11e2a293735446e16dc8a60731d0c9eca2c64b9fe19dc92e72ad559a58e51d3bcb105bbfbf88e4c5f
  languageName: node
  linkType: hard

"@next/env@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/env@npm:13.4.12"
  checksum: 2ccb2e271b3c42697c1e807cdf988429fcb563f80fa0ca72512f65f47cbbcc46c44fc53bf055814d4b467f1394de8c1a1ef6aad14d35f9993004faa956466d02
  languageName: node
  linkType: hard

"@next/env@npm:^13.4.3":
  version: 13.5.3
  resolution: "@next/env@npm:13.5.3"
  checksum: ebea3bfca114ca66616557a534fbb37d580f1ab91143eb46ba3bdb5803864dc0e72c08814110809f207d625846f0053871adb75b51b68686ec3a9ed76d9d26bf
  languageName: node
  linkType: hard

"@next/eslint-plugin-next@npm:13.5.3":
  version: 13.5.3
  resolution: "@next/eslint-plugin-next@npm:13.5.3"
  dependencies:
    glob: 7.1.7
  checksum: a496a194154b84c7178832ed5ecd51f7727cc9967bab91c8898310e422c27ec1774a21cc60399f72a1e7a23241b4eb5f0fe4b6357071d91c9c820790188504ba
  languageName: node
  linkType: hard

"@next/swc-darwin-arm64@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-darwin-arm64@npm:13.4.12"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-darwin-x64@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-darwin-x64@npm:13.4.12"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-gnu@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-linux-arm64-gnu@npm:13.4.12"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-linux-arm64-musl@npm:13.4.12"
  checksum: 4b26ea4418b83f271ed800e1917e1c20623830f58fd9dd3dc251f873957b48a9cd6d0453a011f3d1cff3cdbbebadeca803467cf29139d9c3629227ebaaa1a2d8
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-x64-gnu@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-linux-x64-gnu@npm:13.4.12"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-linux-x64-musl@npm:13.4.12"
  checksum: ee83be7737ac60e794ab51bbd8ca06721d5a0ff4dd762ef644e06b7ae2664157a79f568c0a458ecc14d84c07b3657dbcb119e5242116e3c11e4476b4d803c5fa
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-win32-arm64-msvc@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-win32-arm64-msvc@npm:13.4.12"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-win32-ia32-msvc@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-win32-ia32-msvc@npm:13.4.12"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@next/swc-win32-x64-msvc@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-win32-x64-msvc@npm:13.4.12"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@nicolo-ribaudo/chokidar-2@npm:2.1.8-no-fsevents.3":
  version: 2.1.8-no-fsevents.3
  resolution: "@nicolo-ribaudo/chokidar-2@npm:2.1.8-no-fsevents.3"
  checksum: ee55cc9241aeea7eb94b8a8551bfa4246c56c53bc71ecda0a2104018fcc328ba5723b33686bdf9cc65d4df4ae65e8016b89e0bbdeb94e0309fe91bb9ced42344
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.0
  resolution: "@npmcli/fs@npm:3.1.0"
  dependencies:
    semver: ^7.3.5
  checksum: a50a6818de5fc557d0b0e6f50ec780a7a02ab8ad07e5ac8b16bf519e0ad60a144ac64f97d05c443c3367235d337182e1d012bbac0eb8dbae8dc7b40b193efd0e
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@popperjs/core@npm:^2.11.8":
  version: 2.11.8
  resolution: "@popperjs/core@npm:2.11.8"
  checksum: e5c69fdebf52a4012f6a1f14817ca8e9599cb1be73dd1387e1785e2ed5e5f0862ff817f420a87c7fc532add1f88a12e25aeb010ffcbdc98eace3d55ce2139cf0
  languageName: node
  linkType: hard

"@reduxjs/toolkit@npm:^1.9.2":
  version: 1.9.6
  resolution: "@reduxjs/toolkit@npm:1.9.6"
  dependencies:
    immer: ^9.0.21
    redux: ^4.2.1
    redux-thunk: ^2.4.2
    reselect: ^4.1.8
  peerDependencies:
    react: ^16.9.0 || ^17.0.0 || ^18
    react-redux: ^7.2.1 || ^8.0.2
  peerDependenciesMeta:
    react:
      optional: true
    react-redux:
      optional: true
  checksum: 61d445f7e084c79f9601f61fcfc4eb65152b850b2a4330239d982297605bd870e63dc1e0211deb3822392cd3bc0c88ca0cdb236a9711a4311dfb199c607b6ac5
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 17d04adf404e04c1e61391ed97bca5117d4c2767a76ae3e879390d6dec7b317fcae68afbf9e98badee075d0b64fa60f287729c4942021b4d19cd01db77385c01
  languageName: node
  linkType: hard

"@rushstack/eslint-patch@npm:^1.3.3":
  version: 1.5.0
  resolution: "@rushstack/eslint-patch@npm:1.5.0"
  checksum: f21595f92ade631b5c5b2426962db727f79a7224a2c60e462779310bf1f2b592a6f59d280fa936001f564a4c7d40997ba80b5a32081fee061309ef2c56b7f4b5
  languageName: node
  linkType: hard

"@stoneleigh/api-lib@npm:6.1.1":
  version: 6.1.1
  resolution: "@stoneleigh/api-lib@npm:6.1.1"
  dependencies:
    react: 18.2.0
    react-query: ^3.39.3
    swr: ^1.3.0
  dependenciesMeta:
    react:
      optional: true
    react-query:
      optional: true
    swr:
      optional: true
  checksum: 2c0a2b901291058dcdcb77c173bc68d33de04eca191bd5c352e265babda1387a0dccc7648413f99e170fe480352f754f862f6e82387ffa34a955ec141f7ebc14
  languageName: node
  linkType: hard

"@stoneleigh/appconfig-lib@npm:^1.2.0":
  version: 1.2.0
  resolution: "@stoneleigh/appconfig-lib@npm:1.2.0"
  peerDependencies:
    react: ">=16"
  checksum: 03986b8929f8bb1629a805fa44757f1eb7b380fbc999ac8187372c290cac8a63904df6d9aed87fe5ff6e845b3f6755e7dae7e3a11433f65ddee9bcf8ec3885cd
  languageName: node
  linkType: hard

"@stoneleigh/eslint-plugin@npm:^1.0.16":
  version: 1.0.16
  resolution: "@stoneleigh/eslint-plugin@npm:1.0.16"
  peerDependencies:
    eslint: ">=7"
  checksum: 30d9df380ea8dfc82f20f27f94b2f3cd13bcb3d610dff7f7525e1e59f4298708e55bc259898469b8889cd1bcc8a73afa98b3789fd725ba7da08260303a81a42f
  languageName: node
  linkType: hard

"@stoneleigh/navigation-menu@npm:^1.4.4":
  version: 1.4.4
  resolution: "@stoneleigh/navigation-menu@npm:1.4.4"
  dependencies:
    "@emotion/css": ^11.9.0
    classnames: ^2.3.1
    react-animate-height: ^2.1.2
    react-icons: ^4.3.1
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: fed08a26284c27fe958905e5bcc478a58a6c60dac185164e76b3cbdc5a35ef4847dd7f3365ecbfcec53e84754652afa6a5200930e9199e6bf11df58fbaddd55b
  languageName: node
  linkType: hard

"@stoneleigh/scroll-header@npm:^0.0.24":
  version: 0.0.24
  resolution: "@stoneleigh/scroll-header@npm:0.0.24"
  peerDependencies:
    react: ">=16"
  checksum: 8f972680c5570cf9e650695b29f2703d0c111e6ab5f6a32ae17171dd8622effd540238e3d23c14b96ed4f3a92f66618fe390a4669948b6b4f433a3520a198b1b
  languageName: node
  linkType: hard

"@swc/helpers@npm:0.5.1":
  version: 0.5.1
  resolution: "@swc/helpers@npm:0.5.1"
  dependencies:
    tslib: ^2.4.0
  checksum: 71e0e27234590435e4c62b97ef5e796f88e786841a38c7116a5e27a3eafa7b9ead7cdec5249b32165902076de78446945311c973e59bddf77c1e24f33a8f272a
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: ad87447820dd3f24825d2d947ebc03072b20a42bfc96cbafec16bff8bbda6c1a81fcb0be56d5b21968560c5359a0af4038a68ba150c3e1694fe4c109a063bed8
  languageName: node
  linkType: hard

"@types/babel-plugin-macros@npm:^3":
  version: 3.1.1
  resolution: "@types/babel-plugin-macros@npm:3.1.1"
  dependencies:
    "@types/babel__core": "*"
  checksum: 2d0edb37dd3c95b34b0ae0062f78a0a841ec514a900795fc05b1a2010ce13d66fa6f106a7332e68a35556b7ad82386e204fde65a59b2f7c19e808dca8feac1ef
  languageName: node
  linkType: hard

"@types/babel__core@npm:*":
  version: 7.20.2
  resolution: "@types/babel__core@npm:7.20.2"
  dependencies:
    "@babel/parser": ^7.20.7
    "@babel/types": ^7.20.7
    "@types/babel__generator": "*"
    "@types/babel__template": "*"
    "@types/babel__traverse": "*"
  checksum: 564fbaa8ff1305d50807ada0ec227c3e7528bebb2f8fe6b2ed88db0735a31511a74ad18729679c43eeed8025ed29d408f53059289719e95ab1352ed559a100bd
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.6.5
  resolution: "@types/babel__generator@npm:7.6.5"
  dependencies:
    "@babel/types": ^7.0.0
  checksum: c7459f5025c4c800eaf58f4db3b24e9d736331fe7df40961d9bc49f31b46e2a3be83dc9276e8688f10a5ed752ae153ad5f1bdd45e2245bac95273730b9115ec2
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.2
  resolution: "@types/babel__template@npm:7.4.2"
  dependencies:
    "@babel/parser": ^7.1.0
    "@babel/types": ^7.0.0
  checksum: 0fe977b45a3269336c77f3ae4641a6c48abf0fa35ab1a23fb571690786af02d6cec08255a43499b0b25c5633800f7ae882ace450cce905e3060fa9e6995047ae
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*":
  version: 7.20.2
  resolution: "@types/babel__traverse@npm:7.20.2"
  dependencies:
    "@babel/types": ^7.20.7
  checksum: 981340286479524436348d32373eaa3bf993c635cbf70307b4b69463eee83406a959ac4844f683911e0db8ab8d9f0025ab630dc7a8c170fee9ee74144c2a528f
  languageName: node
  linkType: hard

"@types/cookie@npm:^0":
  version: 0.5.2
  resolution: "@types/cookie@npm:0.5.2"
  checksum: 4a1e46379d877f5a3cc687a9799dd72e5f7e68c4764d45cb469789dec92b4b4a7a5bb8b10a08c90be15939fa7b9b402f87ed339dfac0cabf43699c5189880e88
  languageName: node
  linkType: hard

"@types/cookie@npm:^0.3.3":
  version: 0.3.3
  resolution: "@types/cookie@npm:0.3.3"
  checksum: 450c930d792a4fd5a93645b4123f02596368f904dbb1fe6fbb5043bce8f6ecf877a08511c6ba11c8e28168f62bc278e68d214f002fab927c9056c0bc69f21370
  languageName: node
  linkType: hard

"@types/cookie@npm:^0.4.1":
  version: 0.4.1
  resolution: "@types/cookie@npm:0.4.1"
  checksum: 3275534ed69a76c68eb1a77d547d75f99fedc80befb75a3d1d03662fb08d697e6f8b1274e12af1a74c6896071b11510631ba891f64d30c78528d0ec45a9c1a18
  languageName: node
  linkType: hard

"@types/eslint@npm:^8":
  version: 8.44.3
  resolution: "@types/eslint@npm:8.44.3"
  dependencies:
    "@types/estree": "*"
    "@types/json-schema": "*"
  checksum: 3a0d152785400cb83a887a646d9c8877468e686b6fb439635c64856b70dbe91019e588d2b32bc923cd60642bf5dca7f70b2cf61eb431cf25fbdf2932f6e13dd3
  languageName: node
  linkType: hard

"@types/estree@npm:*":
  version: 1.0.2
  resolution: "@types/estree@npm:1.0.2"
  checksum: aeedb1b2fe20cbe06f44b99b562bf9703e360bfcdf5bb3d61d248182ee1dd63500f2474e12f098ffe1f5ac3202b43b3e18ec99902d9328d5374f5512fa077e45
  languageName: node
  linkType: hard

"@types/fg-loadcss@npm:^3.1.1":
  version: 3.1.1
  resolution: "@types/fg-loadcss@npm:3.1.1"
  checksum: da2110742929787e4d280f213515d25a4fc8bface878d186e1c375796503e88471024fb35b39e3b247a670679df0a82169dbf5d6bf7d1ad6529e4ed46be4976b
  languageName: node
  linkType: hard

"@types/history@npm:^4.7.11":
  version: 4.7.11
  resolution: "@types/history@npm:4.7.11"
  checksum: c92e2ba407dcab0581a9afdf98f533aa41b61a71133420a6d92b1ca9839f741ab1f9395b17454ba5b88cb86020b70b22d74a1950ccfbdfd9beeaa5459fdc3464
  languageName: node
  linkType: hard

"@types/hoist-non-react-statics@npm:^3.3.0, @types/hoist-non-react-statics@npm:^3.3.1":
  version: 3.3.2
  resolution: "@types/hoist-non-react-statics@npm:3.3.2"
  dependencies:
    "@types/react": "*"
    hoist-non-react-statics: ^3.3.0
  checksum: fe5d4b751e13f56010811fd6c4e49e53e2ccbcbbdc54bb8d86a413fbd08c5a83311bca9ef75a1a88d3ba62806711b5dea3f323c0e0f932b3a283dcebc3240238
  languageName: node
  linkType: hard

"@types/js-cookie@npm:^2.2.6":
  version: 2.2.7
  resolution: "@types/js-cookie@npm:2.2.7"
  checksum: 851f47e94ca1fc43661d8f51614d67a613e7810c91b876d0a3b311ce72f7df800107fd02a08cb6948184e12c120b4f058edca2f50424d8798bdcffd6627281e3
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.12":
  version: 7.0.13
  resolution: "@types/json-schema@npm:7.0.13"
  checksum: 345df21a678fa72fb389f35f33de77833d09d4a142bb2bcb27c18690efa4cf70fc2876e43843cefb3fbdb9fcb12cd3e970a90936df30f53bbee899865ff605ab
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: e60b153664572116dfea673c5bda7778dbff150498f44f998e34b5886d8afc47f16799280e4b6e241c0472aef1bc36add771c569c68fc5125fc2ae519a3eb9ac
  languageName: node
  linkType: hard

"@types/lodash.throttle@npm:^4":
  version: 4.1.7
  resolution: "@types/lodash.throttle@npm:4.1.7"
  dependencies:
    "@types/lodash": "*"
  checksum: 6e1b3836488fecbdc537b6ad9b3fe4855c7336b0fa388773cd57d486619f565a48cabc04b28677fd3819be3f2d13d2bb8f9d4428aa5632885c86cb99729bfd69
  languageName: node
  linkType: hard

"@types/lodash@npm:*":
  version: 4.14.199
  resolution: "@types/lodash@npm:4.14.199"
  checksum: e68d1fcbbfce953ed87b296a628573f62939227bcda0c934954e862b421e8a34c5e71cad6fea27b9980567909e6a4698f09025692958e36d64ea9ed99ec6fb2e
  languageName: node
  linkType: hard

"@types/lodash@npm:^4":
  version: 4.17.5
  resolution: "@types/lodash@npm:4.17.5"
  checksum: 3c9bb15772509f0ecb40428531863dbc3f064f2bf34bbccc2ce2b2923c69fb0868aec7e357b1d97fd0d7f7e435a014ea5c1adef8a64715529887179c97a5a823
  languageName: node
  linkType: hard

"@types/node@npm:^16.10.2":
  version: 16.18.54
  resolution: "@types/node@npm:16.18.54"
  checksum: 208e8fc64f605e9cd55ab5e620a0fd019d8fe5629e3e3c5de869a149b731ab0fac5720c516dccc0ecc834ac27df754723dfe6554551663f016ba5096ea8851df
  languageName: node
  linkType: hard

"@types/node@npm:^18.6.2":
  version: 18.18.0
  resolution: "@types/node@npm:18.18.0"
  checksum: 61bcffa28eb713e7a4c66fd369df603369c3f834a783faeced95fe3e78903faa25f1a704d49e054f41d71b7915eeb066d10a37cc699421fcf5dd267f96ad5808
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "@types/parse-json@npm:4.0.0"
  checksum: fd6bce2b674b6efc3db4c7c3d336bd70c90838e8439de639b909ce22f3720d21344f52427f1d9e57b265fcb7f6c018699b99e5e0c208a1a4823014269a6bf35b
  languageName: node
  linkType: hard

"@types/prop-types@npm:*, @types/prop-types@npm:^15.7.5":
  version: 15.7.7
  resolution: "@types/prop-types@npm:15.7.7"
  checksum: 023b95f7dd82e1c594f51dcb93ec4c382600cef6eeee29a2ac7b782b92c0882eab8da16d4cbd6e18b39e85ac8d94ebf4ca02c6e248ce5b5fb4b16dbab5d82861
  languageName: node
  linkType: hard

"@types/prop-types@npm:^15.7.13":
  version: 15.7.13
  resolution: "@types/prop-types@npm:15.7.13"
  checksum: 8935cad87c683c665d09a055919d617fe951cb3b2d5c00544e3a913f861a2bd8d2145b51c9aa6d2457d19f3107ab40784c40205e757232f6a80cc8b1c815513c
  languageName: node
  linkType: hard

"@types/prop-types@npm:^15.7.7":
  version: 15.7.8
  resolution: "@types/prop-types@npm:15.7.8"
  checksum: 61dfad79da8b1081c450bab83b77935df487ae1cdd4660ec7df6be8e74725c15fa45cf486ce057addc956ca4ae78300b97091e2a25061133d1b9a1440bc896ae
  languageName: node
  linkType: hard

"@types/prop-types@npm:^15.7.8":
  version: 15.7.9
  resolution: "@types/prop-types@npm:15.7.9"
  checksum: c7591d3ff7593e243908a07e1d3e2bb6e8879008af5800d8378115a90d0fdf669a1cae72a6d7f69e59c4fa7bb4c8ed61f6ebc1c520fe110c6f2b03ac02414072
  languageName: node
  linkType: hard

"@types/react-dom@npm:18.x":
  version: 18.3.1
  resolution: "@types/react-dom@npm:18.3.1"
  dependencies:
    "@types/react": "*"
  checksum: ad28ecce3915d30dc76adc2a1373fda1745ba429cea290e16c6628df9a05fd80b6403c8e87d78b45e6c60e51df7a67add389ab62b90070fbfdc9bda8307d9953
  languageName: node
  linkType: hard

"@types/react-image-gallery@npm:^1":
  version: 1.2.1
  resolution: "@types/react-image-gallery@npm:1.2.1"
  dependencies:
    "@types/react": "*"
  checksum: d33b22616f034e4e83112fe879a5ae0e744aa0942777df75257e233e94130eadd1798e66df14f1dbcfa98ea4721c48d7c24b19123644491cd6c2767483a9b292
  languageName: node
  linkType: hard

"@types/react-linkify@npm:^1.0.1":
  version: 1.0.2
  resolution: "@types/react-linkify@npm:1.0.2"
  dependencies:
    "@types/react": "*"
  checksum: 28bc4d7ba27e4ac31a90e54a2ac5f3b28fc445e1d199fc653f3636ef1b3d8e43b414b97a20280ef2cadc21f7d8d1cd580222fedb6657fad0b3cf04d780c254c9
  languageName: node
  linkType: hard

"@types/react-redux@npm:^7.1.20":
  version: 7.1.27
  resolution: "@types/react-redux@npm:7.1.27"
  dependencies:
    "@types/hoist-non-react-statics": ^3.3.0
    "@types/react": "*"
    hoist-non-react-statics: ^3.3.0
    redux: ^4.0.0
  checksum: 38fcc56f013e81e9a3125fd75acdacb4cdb5f9fe49402330b4783923f236d2d12ccdd2240ffa42e5bbb75900acd55393c00e0ca5dd6cab91a7b7e39e74ac62b4
  languageName: node
  linkType: hard

"@types/react-router-dom@npm:^5.3.2":
  version: 5.3.3
  resolution: "@types/react-router-dom@npm:5.3.3"
  dependencies:
    "@types/history": ^4.7.11
    "@types/react": "*"
    "@types/react-router": "*"
  checksum: 28c4ea48909803c414bf5a08502acbb8ba414669b4b43bb51297c05fe5addc4df0b8fd00e0a9d1e3535ec4073ef38aaafac2c4a2b95b787167d113bc059beff3
  languageName: node
  linkType: hard

"@types/react-router@npm:*":
  version: 5.1.20
  resolution: "@types/react-router@npm:5.1.20"
  dependencies:
    "@types/history": ^4.7.11
    "@types/react": "*"
  checksum: 128764143473a5e9457ddc715436b5d49814b1c214dde48939b9bef23f0e77f52ffcdfa97eb8d3cc27e2c229869c0cdd90f637d887b62f2c9f065a87d6425419
  languageName: node
  linkType: hard

"@types/react-transition-group@npm:^4.4.6":
  version: 4.4.7
  resolution: "@types/react-transition-group@npm:4.4.7"
  dependencies:
    "@types/react": "*"
  checksum: 3b91486e7aa777a3787e773efce79a0fa9be4ec9e02d51ccda8c7532c5c5d84fbcefe248dacb4007293d85bf0794ac51603bb9cec360db81cf3657d2b7123fb9
  languageName: node
  linkType: hard

"@types/react@npm:18.x":
  version: 18.3.12
  resolution: "@types/react@npm:18.3.12"
  dependencies:
    "@types/prop-types": "*"
    csstype: ^3.0.2
  checksum: 4ab1577a8c2105a5e316536f724117c90eee5f4bd5c137fc82a2253d8c1fd299dedaa07e8dfc95d6e2f04a4be3cb8b0e1b06098c6233ebd55c508d88099395b7
  languageName: node
  linkType: hard

"@types/redux-logger@npm:^3.0.9":
  version: 3.0.10
  resolution: "@types/redux-logger@npm:3.0.10"
  dependencies:
    redux: ^4.0.0
  checksum: e84b7881bc4279413b2cb6808dfbba8b1a39d51fade4def6469da36ae134b2366f7d4bd30bc5d4c0b7156ae4aeb2fd5913ec84f5c3f8d0fe1003852ff798c24b
  languageName: node
  linkType: hard

"@types/semver@npm:^7.5.0":
  version: 7.5.3
  resolution: "@types/semver@npm:7.5.3"
  checksum: 349fdd1ab6c213bac5c991bac766bd07b8b12e63762462bb058740dcd2eb09c8193d068bb226f134661275f2022976214c0e727a4e5eb83ec1b131127c980d3e
  languageName: node
  linkType: hard

"@types/stylis@npm:^4.0.2":
  version: 4.2.1
  resolution: "@types/stylis@npm:4.2.1"
  checksum: 556e7e4c3140f70afba4b1eacd2af48f6877c86eef43ce5d10d9d607e68f46ab25fe2cf9ae8a2f93765ca59aede98b6a1fd3cb66b4f8d73769932c50b614c8c3
  languageName: node
  linkType: hard

"@types/use-sync-external-store@npm:^0.0.3":
  version: 0.0.3
  resolution: "@types/use-sync-external-store@npm:0.0.3"
  checksum: 161ddb8eec5dbe7279ac971531217e9af6b99f7783213566d2b502e2e2378ea19cf5e5ea4595039d730aa79d3d35c6567d48599f69773a02ffcff1776ec2a44e
  languageName: node
  linkType: hard

"@types/uuid@npm:^9":
  version: 9.0.4
  resolution: "@types/uuid@npm:9.0.4"
  checksum: 356e2504456eaebbc43a5af5ca6c07d71f5ae5520b5767cb1c62cd0ec55475fc4ee3d16a2874f4a5fce78e40e8583025afd3a7d9ba41f82939de310665f53f0e
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^6.7.3":
  version: 6.7.3
  resolution: "@typescript-eslint/eslint-plugin@npm:6.7.3"
  dependencies:
    "@eslint-community/regexpp": ^4.5.1
    "@typescript-eslint/scope-manager": 6.7.3
    "@typescript-eslint/type-utils": 6.7.3
    "@typescript-eslint/utils": 6.7.3
    "@typescript-eslint/visitor-keys": 6.7.3
    debug: ^4.3.4
    graphemer: ^1.4.0
    ignore: ^5.2.4
    natural-compare: ^1.4.0
    semver: ^7.5.4
    ts-api-utils: ^1.0.1
  peerDependencies:
    "@typescript-eslint/parser": ^6.0.0 || ^6.0.0-alpha
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: ac2790882199047abc59c0407a862f3339645623d03ea0aae5a73fd4bac6abfb753afcf9f23fd51cd1d5aa73f132ef94e2850774c4b2a3d99ebb83030b09429c
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^5.4.2 || ^6.0.0, @typescript-eslint/parser@npm:^6.7.3":
  version: 6.7.3
  resolution: "@typescript-eslint/parser@npm:6.7.3"
  dependencies:
    "@typescript-eslint/scope-manager": 6.7.3
    "@typescript-eslint/types": 6.7.3
    "@typescript-eslint/typescript-estree": 6.7.3
    "@typescript-eslint/visitor-keys": 6.7.3
    debug: ^4.3.4
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 658f3294b281db06ebb46884b92172d45eb402ec25c7d4a09cc2461eee359266029af7a49eb9006ee7c3e0003ba53a06f4bee84aa2e99d2d9a3507b9c84ff775
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:6.7.3":
  version: 6.7.3
  resolution: "@typescript-eslint/scope-manager@npm:6.7.3"
  dependencies:
    "@typescript-eslint/types": 6.7.3
    "@typescript-eslint/visitor-keys": 6.7.3
  checksum: 08215444b7c70af5c45e185ba3c31c550a0a671ab464a67058cbee680c94aa9d1a062958976d8b09f7bcabf2f63114cdc7be2e4e32e2dfdcb2d7cc79961b7b32
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:6.7.3":
  version: 6.7.3
  resolution: "@typescript-eslint/type-utils@npm:6.7.3"
  dependencies:
    "@typescript-eslint/typescript-estree": 6.7.3
    "@typescript-eslint/utils": 6.7.3
    debug: ^4.3.4
    ts-api-utils: ^1.0.1
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: f30a5ab4f88f76457810d72e3ada79fefd94dbbb456069ac004bd7601c9b7f15689b906b66cd849c230f30ae65f6f7039fb169609177ab545b34bacab64f015e
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:6.7.3":
  version: 6.7.3
  resolution: "@typescript-eslint/types@npm:6.7.3"
  checksum: 4adb6177ec710e7438610fee553839a7abecc498dbb36d0170786bab66c5e5415cd720ac06419fd905458ad88c39b661603af5f013adc299137ccb4c51c4c879
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:6.7.3":
  version: 6.7.3
  resolution: "@typescript-eslint/typescript-estree@npm:6.7.3"
  dependencies:
    "@typescript-eslint/types": 6.7.3
    "@typescript-eslint/visitor-keys": 6.7.3
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    semver: ^7.5.4
    ts-api-utils: ^1.0.1
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: eaba1feb0e6882b0bad292172c118aac43ba683d1f04b940b542a20035468d030b062b036ea49eca36aa21782e9b1019e87717003b3c3db7d12dc707466b7eb7
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:6.7.3":
  version: 6.7.3
  resolution: "@typescript-eslint/utils@npm:6.7.3"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@types/json-schema": ^7.0.12
    "@types/semver": ^7.5.0
    "@typescript-eslint/scope-manager": 6.7.3
    "@typescript-eslint/types": 6.7.3
    "@typescript-eslint/typescript-estree": 6.7.3
    semver: ^7.5.4
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  checksum: 685b7c9fa95ad085f30e26431dc41b3059a42a16925defe2a94b32fb46974bfc168000de7d4d9ad4a1d0568a983f9d3c01ea6bc6cfa9a798e482719af9e9165b
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:6.7.3":
  version: 6.7.3
  resolution: "@typescript-eslint/visitor-keys@npm:6.7.3"
  dependencies:
    "@typescript-eslint/types": 6.7.3
    eslint-visitor-keys: ^3.4.1
  checksum: cef64173a919107f420703e204d97d0afef0d9bd7a67570df5bdb39ac9464211c5a7b3af735d8f41e8004b443ab83e88b1d6fb951886aed4d3fe9d4778667199
  languageName: node
  linkType: hard

"@videojs/http-streaming@npm:2.16.2":
  version: 2.16.2
  resolution: "@videojs/http-streaming@npm:2.16.2"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/vhs-utils": 3.0.5
    aes-decrypter: 3.1.3
    global: ^4.4.0
    m3u8-parser: 4.8.0
    mpd-parser: ^0.22.1
    mux.js: 6.0.1
    video.js: ^6 || ^7
  peerDependencies:
    video.js: ^6 || ^7
  checksum: 575caab333b34b7c2df291b28b802d5bb0e0d865dd2b677bfade91ab536f6a303115dcdc9783588b6636ec380e11e9bf9ec8b22361bd7cd67010b23cc83238ea
  languageName: node
  linkType: hard

"@videojs/http-streaming@npm:3.6.0":
  version: 3.6.0
  resolution: "@videojs/http-streaming@npm:3.6.0"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/vhs-utils": 4.0.0
    aes-decrypter: 4.0.1
    global: ^4.4.0
    m3u8-parser: ^7.1.0
    mpd-parser: ^1.2.2
    mux.js: 7.0.0
    video.js: ^7 || ^8
  peerDependencies:
    video.js: ^7 || ^8
  checksum: fc6f75153602d9b1c53a4f6c6f6dcc6ad4d144569417576e30d6d2ba43f466d38e1eeef8bd9902ed209c0b82da978194031f3d17739accaa857315cd391f4ca8
  languageName: node
  linkType: hard

"@videojs/vhs-utils@npm:3.0.5, @videojs/vhs-utils@npm:^3.0.4, @videojs/vhs-utils@npm:^3.0.5":
  version: 3.0.5
  resolution: "@videojs/vhs-utils@npm:3.0.5"
  dependencies:
    "@babel/runtime": ^7.12.5
    global: ^4.4.0
    url-toolkit: ^2.2.1
  checksum: 637dc9a8848027eab4213d6e85439d216577d0cb0346767c15bbb07a5a730aa30545f0b5d7bfd636f40212e490f34bf103e331f0bc180b4d3c245e77f30b437f
  languageName: node
  linkType: hard

"@videojs/vhs-utils@npm:4.0.0, @videojs/vhs-utils@npm:^4.0.0":
  version: 4.0.0
  resolution: "@videojs/vhs-utils@npm:4.0.0"
  dependencies:
    "@babel/runtime": ^7.12.5
    global: ^4.4.0
    url-toolkit: ^2.2.1
  checksum: a824900d0173478c80ee9683f67cc041e204d79ac280ae369adb03357d51d8a51b296bd6aff96b2cce66633a63f05a10d11d53da6ec063ee7c4d6e38dabd826e
  languageName: node
  linkType: hard

"@videojs/xhr@npm:2.6.0":
  version: 2.6.0
  resolution: "@videojs/xhr@npm:2.6.0"
  dependencies:
    "@babel/runtime": ^7.5.5
    global: ~4.4.0
    is-function: ^1.0.1
  checksum: 217d3d49f8088461959840f93f0b45d713e6d03254d89352e2eb4bfca9501eee03bac25d0ad878e976c9cbec902039210a86906ef57a5f8d86bfdfa1a3999def
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.3":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 4c136aec31fb3b49aaa53b6fcbfe524d02a1dc0d8e17ee35bd3bf35e9ce1344560481cd1efd086ad1a4821541482528672306d5e37cdbd187f33d7fadd3e2cf0
  languageName: node
  linkType: hard

"@xobotyi/scrollbar-width@npm:^1.9.5":
  version: 1.9.5
  resolution: "@xobotyi/scrollbar-width@npm:1.9.5"
  checksum: e880c8696bd6c7eedaad4e89cc7bcfcd502c22dc6c061288ffa7f5a4fe5dab4aa2358bdd68e7357bf0334dc8b56724ed9bee05e010b60d83a3bb0d855f3d886f
  languageName: node
  linkType: hard

"abbrev@npm:^1.0.0":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn@npm:^8.9.0":
  version: 8.10.0
  resolution: "acorn@npm:8.10.0"
  bin:
    acorn: bin/acorn
  checksum: 538ba38af0cc9e5ef983aee196c4b8b4d87c0c94532334fa7e065b2c8a1f85863467bb774231aae91613fcda5e68740c15d97b1967ae3394d20faddddd8af61d
  languageName: node
  linkType: hard

"aes-decrypter@npm:3.1.3":
  version: 3.1.3
  resolution: "aes-decrypter@npm:3.1.3"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/vhs-utils": ^3.0.5
    global: ^4.4.0
    pkcs7: ^1.0.4
  checksum: e634af25c5086894690062a2875d87e80118cd8f7945a162b4f8aaadb4e5c0f0656d2c31c619024c64f935c04938260f30e1531607617ce775430c74a840ca14
  languageName: node
  linkType: hard

"aes-decrypter@npm:4.0.1, aes-decrypter@npm:^4.0.1":
  version: 4.0.1
  resolution: "aes-decrypter@npm:4.0.1"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/vhs-utils": ^3.0.5
    global: ^4.4.0
    pkcs7: ^1.0.4
  checksum: c09f47e928d88af2387690e47e60001be74095c10007fe44ef57663db17ee8e391310c6f0b023bc63257afe442900cc916f55fba7dd27ec1c394d616b062d664
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.2.1":
  version: 4.5.0
  resolution: "agentkeepalive@npm:4.5.0"
  dependencies:
    humanize-ms: ^1.2.1
  checksum: 13278cd5b125e51eddd5079f04d6fe0914ac1b8b91c1f3db2c1822f99ac1a7457869068997784342fe455d59daaff22e14fb7b8c3da4e741896e7e31faf92481
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 1ff8b7667cded1de4fa2c9ae283e979fc87036864317da86a2e546725f96406746411d0d85e87a2d12fa5abd715d90006de7fa4fa0477c92321ad3b4c7d4e169
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 0ee8a9bdbe882c90464d75d1f55cf027f5458650c4bd1f0467e65aec38ccccda07ca5844969ee77ed46d04e7dded3eaceb027e8d32f385688523fe305fa7e1de
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^3.0.0":
  version: 3.0.1
  resolution: "are-we-there-yet@npm:3.0.1"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 52590c24860fa7173bedeb69a4c05fb573473e860197f618b9a28432ee4379049336727ae3a1f9c4cb083114601c1140cee578376164d0e651217a9843f9fe83
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"aria-query@npm:^5.1.3":
  version: 5.3.0
  resolution: "aria-query@npm:5.3.0"
  dependencies:
    dequal: ^2.0.3
  checksum: 305bd73c76756117b59aba121d08f413c7ff5e80fa1b98e217a3443fcddb9a232ee790e24e432b59ae7625aebcf4c47cb01c2cac872994f0b426f5bdfcd96ba9
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.0":
  version: 1.0.0
  resolution: "array-buffer-byte-length@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    is-array-buffer: ^3.0.1
  checksum: 044e101ce150f4804ad19c51d6c4d4cfa505c5b2577bd179256e4aa3f3f6a0a5e9874c78cd428ee566ac574c8a04d7ce21af9fe52e844abfdccb82b33035a7c3
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1":
  version: 1.0.1
  resolution: "array-buffer-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.5
    is-array-buffer: ^3.0.4
  checksum: 53524e08f40867f6a9f35318fafe467c32e45e9c682ba67b11943e167344d2febc0f6977a17e699b05699e805c3e8f073d876f8bbf1b559ed494ad2cd0fae09e
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6":
  version: 3.1.7
  resolution: "array-includes@npm:3.1.7"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    get-intrinsic: ^1.2.1
    is-string: ^1.0.7
  checksum: 06f9e4598fac12a919f7c59a3f04f010ea07f0b7f0585465ed12ef528a60e45f374e79d1bddbb34cdd4338357d00023ddbd0ac18b0be36964f5e726e8965d7fc
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.8":
  version: 3.1.8
  resolution: "array-includes@npm:3.1.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.4
    is-string: ^1.0.7
  checksum: eb39ba5530f64e4d8acab39297c11c1c5be2a4ea188ab2b34aba5fb7224d918f77717a9d57a3e2900caaa8440e59431bdaf5c974d5212ef65d97f132e38e2d91
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.2":
  version: 1.2.3
  resolution: "array.prototype.findlastindex@npm:1.2.3"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    es-shim-unscopables: ^1.0.0
    get-intrinsic: ^1.2.1
  checksum: 31f35d7b370c84db56484618132041a9af401b338f51899c2e78ef7690fbba5909ee7ca3c59a7192085b328cc0c68c6fd1f6d1553db01a689a589ae510f3966e
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlastindex@npm:1.2.5"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-shim-unscopables: ^1.0.2
  checksum: 2c81cff2a75deb95bf1ed89b6f5f2bfbfb882211e3b7cc59c3d6b87df774cd9d6b36949a8ae39ac476e092c1d4a4905f5ee11a86a456abb10f35f8211ae4e710
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1, array.prototype.flat@npm:^1.3.2":
  version: 1.3.2
  resolution: "array.prototype.flat@npm:1.3.2"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    es-shim-unscopables: ^1.0.0
  checksum: 5d6b4bf102065fb3f43764bfff6feb3295d372ce89591e6005df3d0ce388527a9f03c909af6f2a973969a4d178ab232ffc9236654149173e0e187ec3a1a6b87b
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.1, array.prototype.flatmap@npm:^1.3.2":
  version: 1.3.2
  resolution: "array.prototype.flatmap@npm:1.3.2"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    es-shim-unscopables: ^1.0.0
  checksum: ce09fe21dc0bcd4f30271f8144083aa8c13d4639074d6c8dc82054b847c7fc9a0c97f857491f4da19d4003e507172a78f4bcd12903098adac8b9cd374f734be3
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.1":
  version: 1.1.2
  resolution: "array.prototype.tosorted@npm:1.1.2"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    es-shim-unscopables: ^1.0.0
    get-intrinsic: ^1.2.1
  checksum: 3607a7d6b117f0ffa6f4012457b7af0d47d38cf05e01d50e09682fd2fb782a66093a5e5fbbdbad77c8c824794a9d892a51844041641f719ad41e3a974f0764de
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.2":
  version: 1.0.2
  resolution: "arraybuffer.prototype.slice@npm:1.0.2"
  dependencies:
    array-buffer-byte-length: ^1.0.0
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    get-intrinsic: ^1.2.1
    is-array-buffer: ^3.0.2
    is-shared-array-buffer: ^1.0.2
  checksum: c200faf437786f5b2c80d4564ff5481c886a16dee642ef02abdc7306c7edd523d1f01d1dd12b769c7eb42ac9bc53874510db19a92a2c035c0f6696172aafa5d3
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.3":
  version: 1.0.3
  resolution: "arraybuffer.prototype.slice@npm:1.0.3"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    call-bind: ^1.0.5
    define-properties: ^1.2.1
    es-abstract: ^1.22.3
    es-errors: ^1.2.1
    get-intrinsic: ^1.2.3
    is-array-buffer: ^3.0.4
    is-shared-array-buffer: ^1.0.2
  checksum: 352259cba534dcdd969c92ab002efd2ba5025b2e3b9bead3973150edbdf0696c629d7f4b3f061c5931511e8207bdc2306da614703c820b45dabce39e3daf7e3e
  languageName: node
  linkType: hard

"ast-types-flow@npm:^0.0.7":
  version: 0.0.7
  resolution: "ast-types-flow@npm:0.0.7"
  checksum: a26dcc2182ffee111cad7c471759b0bda22d3b7ebacf27c348b22c55f16896b18ab0a4d03b85b4020dce7f3e634b8f00b593888f622915096ea1927fa51866c4
  languageName: node
  linkType: hard

"asynciterator.prototype@npm:^1.0.0":
  version: 1.0.0
  resolution: "asynciterator.prototype@npm:1.0.0"
  dependencies:
    has-symbols: ^1.0.3
  checksum: e8ebfd9493ac651cf9b4165e9d64030b3da1d17181bb1963627b59e240cdaf021d9b59d44b827dc1dde4e22387ec04c2d0f8720cf58a1c282e34e40cc12721b3
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.5":
  version: 1.0.5
  resolution: "available-typed-arrays@npm:1.0.5"
  checksum: 20eb47b3cefd7db027b9bbb993c658abd36d4edd3fe1060e83699a03ee275b0c9b216cc076ff3f2db29073225fb70e7613987af14269ac1fe2a19803ccc97f1a
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"axe-core@npm:^4.6.2":
  version: 4.8.2
  resolution: "axe-core@npm:4.8.2"
  checksum: 8c19f507dabfcb8514e4280c7fc66e85143be303ddb57ec9f119338021228dc9b80560993938003837bda415fde7c07bba3a96560008ffa5f4145a248ed8f5fe
  languageName: node
  linkType: hard

"axios@npm:^0.24.0":
  version: 0.24.0
  resolution: "axios@npm:0.24.0"
  dependencies:
    follow-redirects: ^1.14.4
  checksum: 468cf496c08a6aadfb7e699bebdac02851e3043d4e7d282350804ea8900e30d368daa6e3cd4ab83b8ddb5a3b1e17a5a21ada13fc9cebd27b74828f47a4236316
  languageName: node
  linkType: hard

"axobject-query@npm:^3.1.1":
  version: 3.2.1
  resolution: "axobject-query@npm:3.2.1"
  dependencies:
    dequal: ^2.0.3
  checksum: a94047e702b57c91680e6a952ec4a1aaa2cfd0d80ead76bc8c954202980d8c51968a6ea18b4d8010e8e2cf95676533d8022a8ebba9abc1dfe25686721df26fd2
  languageName: node
  linkType: hard

"babel-plugin-macros@npm:^3.1.0":
  version: 3.1.0
  resolution: "babel-plugin-macros@npm:3.1.0"
  dependencies:
    "@babel/runtime": ^7.12.5
    cosmiconfig: ^7.0.0
    resolve: ^1.19.0
  checksum: 765de4abebd3e4688ebdfbff8571ddc8cd8061f839bb6c3e550b0344a4027b04c60491f843296ce3f3379fb356cc873d57a9ee6694262547eb822c14a25be9a6
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.5":
  version: 0.4.5
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.5"
  dependencies:
    "@babel/compat-data": ^7.22.6
    "@babel/helper-define-polyfill-provider": ^0.4.2
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 33a8e06aa54e2858d211c743d179f0487b03222f9ca1bfd7c4865bca243fca942a3358cb75f6bb894ed476cbddede834811fbd6903ff589f055821146f053e1a
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.8.3":
  version: 0.8.4
  resolution: "babel-plugin-polyfill-corejs3@npm:0.8.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.4.2
    core-js-compat: ^3.32.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 7243241a5b978b1335d51bcbd1248d6c4df88f6b3726706e71e0392f111c59bbf01118c85bb0ed42dce65e90e8fc768d19eda0a81a321cbe54abd3df9a285dc8
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.5.2":
  version: 0.5.2
  resolution: "babel-plugin-polyfill-regenerator@npm:0.5.2"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.4.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: d962200f604016a9a09bc9b4aaf60a3db7af876bb65bcefaeac04d44ac9d9ec4037cf24ce117760cc141d7046b6394c7eb0320ba9665cb4a2ee64df2be187c93
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base-64@npm:^1.0.0":
  version: 1.0.0
  resolution: "base-64@npm:1.0.0"
  checksum: d10b64a1fc9b2c5a5f39f1ce1e6c9d1c5b249222bbfa3a0604c592d90623caf74419983feadd8a170f27dc0c3389704f72faafa3e645aeb56bfc030c93ff074a
  languageName: node
  linkType: hard

"big-integer@npm:^1.6.16":
  version: 1.6.51
  resolution: "big-integer@npm:1.6.51"
  checksum: 3d444173d1b2e20747e2c175568bedeebd8315b0637ea95d75fd27830d3b8e8ba36c6af40374f36bdaea7b5de376dcada1b07587cb2a79a928fccdb6e6e3c518
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.2.0
  resolution: "binary-extensions@npm:2.2.0"
  checksum: ccd267956c58d2315f5d3ea6757cf09863c5fc703e50fbeb13a7dc849b812ef76e3cf9ca8f35a0c48498776a7478d7b4a0418e1e2b8cb9cb9731f2922aaad7f8
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:~3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: ^7.0.1
  checksum: e2a8e769a863f3d4ee887b5fe21f63193a891c68b612ddb4b68d82d1b5f3ff9073af066c343e9867a393fe4c2555dcb33e89b937195feb9c1613d259edfcd459
  languageName: node
  linkType: hard

"broadcast-channel@npm:^3.4.1":
  version: 3.7.0
  resolution: "broadcast-channel@npm:3.7.0"
  dependencies:
    "@babel/runtime": ^7.7.2
    detect-node: ^2.1.0
    js-sha3: 0.8.0
    microseconds: 0.2.0
    nano-time: 1.0.0
    oblivious-set: 1.0.0
    rimraf: 3.0.2
    unload: 2.2.0
  checksum: 803794c48dcce7f03aca69797430bd8b1c4cfd70b7de22079cd89567eeffaa126a1db98c7c2d86af8131d9bb41ed367c0fef96dfb446151c927b831572c621fc
  languageName: node
  linkType: hard

"browserslist@npm:^4.21.10, browserslist@npm:^4.21.9":
  version: 4.22.0
  resolution: "browserslist@npm:4.22.0"
  dependencies:
    caniuse-lite: ^1.0.30001539
    electron-to-chromium: ^1.4.530
    node-releases: ^2.0.13
    update-browserslist-db: ^1.0.13
  bin:
    browserslist: cli.js
  checksum: 14fc119bbfb85b65e2ee4a82205fabf9327520d010c4c586f1176ceaf9136cfdb391397045a4eafaa9defe52b6dbdf875916714695826c69091a936d5838f9ec
  languageName: node
  linkType: hard

"buffer-from@npm:~0.1.1":
  version: 0.1.2
  resolution: "buffer-from@npm:0.1.2"
  checksum: 50a1fa5da97d2081b7d945483c8967d3b89a096fa585eb55000bb2100e827c647c9370280ec9bd057da8f9fa5abc1d3b764228851a31fa8a67f659f70c0052d8
  languageName: node
  linkType: hard

"busboy@npm:1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: ^1.1.0
  checksum: 32801e2c0164e12106bf236291a00795c3c4e4b709ae02132883fe8478ba2ae23743b11c5735a0aae8afe65ac4b6ca4568b91f0d9fed1fdbc32ede824a73746e
  languageName: node
  linkType: hard

"cacache@npm:^17.0.0":
  version: 17.1.4
  resolution: "cacache@npm:17.1.4"
  dependencies:
    "@npmcli/fs": ^3.1.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^7.7.1
    minipass: ^7.0.3
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^4.0.0
    ssri: ^10.0.0
    tar: ^6.1.11
    unique-filename: ^3.0.0
  checksum: b7751df756656954a51201335addced8f63fc53266fa56392c9f5ae83c8d27debffb4458ac2d168a744a4517ec3f2163af05c20097f93d17bdc2dc8a385e14a6
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.0, call-bind@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind@npm:1.0.2"
  dependencies:
    function-bind: ^1.1.1
    get-intrinsic: ^1.0.2
  checksum: f8e31de9d19988a4b80f3e704788c4a2d6b6f3d17cfec4f57dc29ced450c53a49270dc66bf0fbd693329ee948dd33e6c90a329519aef17474a4d961e8d6426b0
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.5, call-bind@npm:^1.0.6, call-bind@npm:^1.0.7":
  version: 1.0.7
  resolution: "call-bind@npm:1.0.7"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.1
  checksum: 295c0c62b90dd6522e6db3b0ab1ce26bdf9e7404215bda13cfee25b626b5ff1a7761324d58d38b1ef1607fc65aca2d06e44d2e18d0dfc6c14b465b00d8660029
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelize@npm:^1.0.0":
  version: 1.0.1
  resolution: "camelize@npm:1.0.1"
  checksum: 91d8611d09af725e422a23993890d22b2b72b4cabf7239651856950c76b4bf53fe0d0da7c5e4db05180e898e4e647220e78c9fbc976113bd96d603d1fcbfcb99
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001406, caniuse-lite@npm:^1.0.30001539":
  version: 1.0.30001541
  resolution: "caniuse-lite@npm:1.0.30001541"
  checksum: 972f6c223cf4ea2c6821b817b419249285006bbf67ebe415fe58097cf07551e3bae898586736d92f7c40b9f0ac28638dbf760631c23742b780affd0254f44d17
  languageName: node
  linkType: hard

"chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chokidar@npm:>=3.0.0 <4.0.0, chokidar@npm:^3.4.0":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: b49fcde40176ba007ff361b198a2d35df60d9bb2a5aab228279eb810feae9294a6b4649ab15981304447afe1e6ffbf4788ad5db77235dc770ab777c6e771980c
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"classnames@npm:^2.2.5, classnames@npm:^2.2.6, classnames@npm:^2.3.1, classnames@npm:^2.3.2":
  version: 2.3.2
  resolution: "classnames@npm:2.3.2"
  checksum: 2c62199789618d95545c872787137262e741f9db13328e216b093eea91c85ef2bfb152c1f9e63027204e2559a006a92eb74147d46c800a9f96297ae1d9f96f4e
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"client-only@npm:0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 0c16bf660dadb90610553c1d8946a7fdfb81d624adea073b8440b7d795d5b5b08beb3c950c6a2cf16279365a3265158a236876d92bce16423c485c322d7dfaf8
  languageName: node
  linkType: hard

"clsx@npm:^1.1.0":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 30befca8019b2eb7dbad38cff6266cf543091dae2825c856a62a8ccf2c3ab9c2907c4d12b288b73101196767f66812365400a227581484a05f968b0307cfaf12
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0":
  version: 2.0.0
  resolution: "clsx@npm:2.0.0"
  checksum: a2cfb2351b254611acf92faa0daf15220f4cd648bdf96ce369d729813b85336993871a4bf6978ddea2b81b5a130478339c20d9d0b5c6fc287e5147f0c059276e
  languageName: node
  linkType: hard

"clsx@npm:^2.1.0, clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: acd3e1ab9d8a433ecb3cc2f6a05ab95fe50b4a3cfc5ba47abb6cbf3754585fcb87b84e90c822a1f256c4198e3b41c7f6c391577ffc8678ad587fc0976b24fd57
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-support@npm:^1.1.3":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 9b7356817670b9a13a26ca5af1c21615463b500783b739b7634a0c2047c16cef4b2865d7576875c31c3cddf9dd621fa19285e628f20198b233a5cfdda6d0793b
  languageName: node
  linkType: hard

"commander@npm:^4.0.1":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: d7b9913ff92cae20cb577a4ac6fcc121bd6223319e54a40f51a14740a681ad5c574fd29a57da478a5f234a6fa6c52cbf0b7c641353e03c648b1ae85ba670b977
  languageName: node
  linkType: hard

"compare-versions@npm:^3.6.0":
  version: 3.6.0
  resolution: "compare-versions@npm:3.6.0"
  checksum: 7492a50cdaa2c27f5254eee7c4b38856e1c164991bab3d98d7fd067fe4b570d47123ecb92523b78338be86aa221668fd3868bfe8caa5587dc3ebbe1a03d52b5d
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 8755d76787f94e6cf79ce4666f0c5519906d7f5b02d4b884cf41e11dcd759ed69c57da0670afd9236d229a46e0f9cf519db0cd829c6dca820bb5a5c3def584ed
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.5.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: dc55a1f28ddd0e9485ef13565f8f756b342f9a46c4ae18b843fe3c30c675d058d6a4823eff86d472f187b176f0adf51ea7b69ea38be34be4a63cbbf91b0593c8
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 63ae9933be5a2b8d4509daca5124e20c14d023c820258e484e32dc324d34c2754e71297c94a05784064ad27615037ef677e3f0c00469fb55f409d2bb21261035
  languageName: node
  linkType: hard

"cookie@npm:^0.4.0":
  version: 0.4.2
  resolution: "cookie@npm:0.4.2"
  checksum: a00833c998bedf8e787b4c342defe5fa419abd96b32f4464f718b91022586b8f1bafbddd499288e75c037642493c83083da426c6a9080d309e3bd90fd11baa9b
  languageName: node
  linkType: hard

"cookie@npm:^0.5.0":
  version: 0.5.0
  resolution: "cookie@npm:0.5.0"
  checksum: 1f4bd2ca5765f8c9689a7e8954183f5332139eb72b6ff783d8947032ec1fdf43109852c178e21a953a30c0dd42257828185be01b49d1eb1a67fd054ca588a180
  languageName: node
  linkType: hard

"cookies-next@npm:^2.1.2":
  version: 2.1.2
  resolution: "cookies-next@npm:2.1.2"
  dependencies:
    "@types/cookie": ^0.4.1
    "@types/node": ^16.10.2
    cookie: ^0.4.0
  checksum: 5c7bf4edf946f8955014417fa609a0426476a6cce7d5f4ae0e7d8309d997e55fd3e41b63dcbbb15d44f6db099f4cdada7144d3c40968deb19efb7f918f8ed7e4
  languageName: node
  linkType: hard

"copy-to-clipboard@npm:^3.3.1":
  version: 3.3.3
  resolution: "copy-to-clipboard@npm:3.3.3"
  dependencies:
    toggle-selection: ^1.0.6
  checksum: e0a325e39b7615108e6c1c8ac110ae7b829cdc4ee3278b1df6a0e4228c490442cc86444cd643e2da344fbc424b3aab8909e2fec82f8bc75e7e5b190b7c24eecf
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.31.0, core-js-compat@npm:^3.32.2":
  version: 3.32.2
  resolution: "core-js-compat@npm:3.32.2"
  dependencies:
    browserslist: ^4.21.10
  checksum: efca146ad71a542e6f196db5ba5aed617e48c615bdf1fbb065471b3267f833ac545bd5fc5ad0642c3d3974b955f0684ff0863d7471d7050ee0284e0a1313942e
  languageName: node
  linkType: hard

"core-js@npm:^3":
  version: 3.32.2
  resolution: "core-js@npm:3.32.2"
  checksum: d6fac7e8eb054eefc211c76cd0a0ff07447a917122757d085f469f046ec888d122409c7db1a9601c3eb5fa767608ed380bcd219eace02bdf973da155680edeec
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7.0.0":
  version: 7.1.0
  resolution: "cosmiconfig@npm:7.1.0"
  dependencies:
    "@types/parse-json": ^4.0.0
    import-fresh: ^3.2.1
    parse-json: ^5.0.0
    path-type: ^4.0.0
    yaml: ^1.10.0
  checksum: c53bf7befc1591b2651a22414a5e786cd5f2eeaa87f3678a3d49d6069835a9d8d1aef223728e98aa8fec9a95bf831120d245096db12abe019fecb51f5696c96f
  languageName: node
  linkType: hard

"countries-list@npm:^3.1.1":
  version: 3.1.1
  resolution: "countries-list@npm:3.1.1"
  checksum: c11467b55a7d59ee7000d1149d5b66b00d60c3a407431808bb7bd8813a9b5c3639f5b1b3efbec9b388b12448340eacfee9a4c4595813708449d6231c158d5e65
  languageName: node
  linkType: hard

"cross-env@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-env@npm:7.0.3"
  dependencies:
    cross-spawn: ^7.0.1
  bin:
    cross-env: src/bin/cross-env.js
    cross-env-shell: src/bin/cross-env-shell.js
  checksum: 26f2f3ea2ab32617f57effb70d329c2070d2f5630adc800985d8b30b56e8bf7f5f439dd3a0358b79cee6f930afc23cf8e23515f17ccfb30092c6b62c6b630a79
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.2":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 671cc7c7288c3a8406f3c69a3ae2fc85555c04169e9d611def9a675635472614f1c0ed0ef80955d5b6d4e724f6ced67f0ad1bb006c2ea643488fcfef994d7f52
  languageName: node
  linkType: hard

"css-color-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-color-keywords@npm:1.0.0"
  checksum: 8f125e3ad477bd03c77b533044bd9e8a6f7c0da52d49bbc0bbe38327b3829d6ba04d368ca49dd9ff3b667d2fc8f1698d891c198bbf8feade1a5501bf5a296408
  languageName: node
  linkType: hard

"css-in-js-utils@npm:^3.1.0":
  version: 3.1.0
  resolution: "css-in-js-utils@npm:3.1.0"
  dependencies:
    hyphenate-style-name: ^1.0.3
  checksum: 066318e918c04a5e5bce46b38fe81052ea6ac051bcc6d3c369a1d59ceb1546cb2b6086901ab5d22be084122ee3732169996a3dfb04d3406eaee205af77aec61b
  languageName: node
  linkType: hard

"css-to-react-native@npm:^3.2.0":
  version: 3.2.0
  resolution: "css-to-react-native@npm:3.2.0"
  dependencies:
    camelize: ^1.0.0
    css-color-keywords: ^1.0.0
    postcss-value-parser: ^4.0.2
  checksum: 263be65e805aef02c3f20c064665c998a8c35293e1505dbe6e3054fb186b01a9897ac6cf121f9840e5a9dfe3fb3994f6fcd0af84a865f1df78ba5bf89e77adce
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.2":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: 2.0.14
    source-map: ^0.6.1
  checksum: 79f9b81803991b6977b7fcb1588799270438274d89066ce08f117f5cdb5e20019b446d766c61506dd772c839df84caa16042d6076f20c97187f5abe3b50e7d1f
  languageName: node
  linkType: hard

"css-vendor@npm:^2.0.8":
  version: 2.0.8
  resolution: "css-vendor@npm:2.0.8"
  dependencies:
    "@babel/runtime": ^7.8.3
    is-in-browser: ^1.0.2
  checksum: 647cd4ea5e401c65c59376255aa2b708e92bf84fba9ce2b3ff5ecb94bf51d74ac374052b1cf9956ef7419b8ebf07fcea9a7683d2d2459127b2ca747ab5b98745
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2, csstype@npm:^3.0.6, csstype@npm:^3.1.2":
  version: 3.1.2
  resolution: "csstype@npm:3.1.2"
  checksum: e1a52e6c25c1314d6beef5168da704ab29c5186b877c07d822bd0806717d9a265e8493a2e35ca7e68d0f5d472d43fac1cdce70fd79fd0853dff81f3028d857b5
  languageName: node
  linkType: hard

"csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"damerau-levenshtein@npm:^1.0.8":
  version: 1.0.8
  resolution: "damerau-levenshtein@npm:1.0.8"
  checksum: d240b7757544460ae0586a341a53110ab0a61126570ef2d8c731e3eab3f0cb6e488e2609e6a69b46727635de49be20b071688698744417ff1b6c1d7ccd03e0de
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-buffer@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.6
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: ce24348f3c6231223b216da92e7e6a57a12b4af81a23f27eff8feabdf06acfb16c00639c8b705ca4d167f761cfc756e27e5f065d0a1f840c10b907fdaf8b988c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.7
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: dbb3200edcb7c1ef0d68979834f81d64fd8cab2f7691b3a4c6b97e67f22182f3ec2c8602efd7b76997b55af6ff8bce485829c1feda4fa2165a6b71fb7baa4269
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.0":
  version: 1.0.0
  resolution: "data-view-byte-offset@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.6
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: 7f0bf8720b7414ca719eedf1846aeec392f2054d7af707c5dc9a753cc77eb8625f067fa901e0b5127e831f9da9056138d894b9c2be79c27a21f6db5824f009c2
  languageName: node
  linkType: hard

"date-fns@npm:^2.30.0":
  version: 2.30.0
  resolution: "date-fns@npm:2.30.0"
  dependencies:
    "@babel/runtime": ^7.21.0
  checksum: f7be01523282e9bb06c0cd2693d34f245247a29098527d4420628966a2d9aad154bd0e90a6b1cf66d37adcb769cd108cf8a7bd49d76db0fb119af5cdd13644f4
  languageName: node
  linkType: hard

"date-fns@npm:^4.1.0":
  version: 4.1.0
  resolution: "date-fns@npm:4.1.0"
  checksum: fb681b242cccabed45494468f64282a7d375ea970e0adbcc5dcc92dcb7aba49b2081c2c9739d41bf71ce89ed68dd73bebfe06ca35129490704775d091895710b
  languageName: node
  linkType: hard

"dayjs-plugin-utc@npm:^0.1.2":
  version: 0.1.2
  resolution: "dayjs-plugin-utc@npm:0.1.2"
  checksum: 80a3732407d7bcb0e2ca34a2964852a60c88540144ecca3fb0f4db03c1611f8198fea49fd8373972af7f8427789e591f42e32d0a5fa54d0f413c1b0921685dec
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.9":
  version: 1.11.10
  resolution: "dayjs@npm:1.11.10"
  checksum: a6b5a3813b8884f5cd557e2e6b7fa569f4c5d0c97aca9558e38534af4f2d60daafd3ff8c2000fed3435cfcec9e805bcebd99f90130c6d1c5ef524084ced588c4
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 3dbad3f94ea64f34431a9cbf0bafb61853eda57bff2880036153438f50fb5a84f27683ba0d8e5426bf41a8c6ff03879488120cf5b3a761e77953169c0600a708
  languageName: node
  linkType: hard

"debug@npm:^2.1.3":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"deep-diff@npm:^0.3.5":
  version: 0.3.8
  resolution: "deep-diff@npm:0.3.8"
  checksum: 8a0fb6cbe468e50211836f8daa1c14798b2d7436bfbcb7d8eb0902e0d61bf1dfd48d5b9edd46a10596182b90ad25f87461b8e55111ff9257b6067ad0676f79c9
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:^4.0.0":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 2024c6a980a1b7128084170c4cf56b0fd58a63f2da1660dcfe977415f27b17dbe5888668b59d0b063753f3220719d5e400b7f113609489c90160bb9a5518d052
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1":
  version: 1.1.0
  resolution: "define-data-property@npm:1.1.0"
  dependencies:
    get-intrinsic: ^1.2.1
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.0
  checksum: 7ad4ee84cca8ad427a4831f5693526804b62ce9dfd4efac77214e95a4382aed930072251d4075dc8dc9fc949a353ed51f19f5285a84a788ba9216cc51472a093
  languageName: node
  linkType: hard

"define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.1.4, define-properties@npm:^1.2.0, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"dequal@npm:^2.0.3":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 8679b850e1a3d0ebbc46ee780d5df7b478c23f335887464023a631d1b9af051ad4a6595a44220f9ff8ff95a8ddccf019b5ad778a976fd7bbf77383d36f412f90
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4, detect-node@npm:^2.1.0":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 832184ec458353e41533ac9c622f16c19f7c02d8b10c303dfd3a756f56be93e903616c0bb2d4226183c9351c15fc0b3dba41a17a2308262afabcfa3776e6ae6e
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: ^2.0.2
  checksum: a45e277f7feaed309fe658ace1ff286c6e2002ac515af0aaf37145b8baa96e49899638c7cd47dccf84c3d32abfc113246625b3ac8f552d1046072adee13b0dc8
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: fd7673ca77fe26cd5cba38d816bc72d641f500f1f9b25b83e8ce28827fe2da7ad583a8da26ab6af85f834138cf8dae9f69b0cd6ab925f52ddab1754db44d99ce
  languageName: node
  linkType: hard

"dom-helpers@npm:^5.0.1":
  version: 5.2.1
  resolution: "dom-helpers@npm:5.2.1"
  dependencies:
    "@babel/runtime": ^7.8.7
    csstype: ^3.0.2
  checksum: 863ba9e086f7093df3376b43e74ce4422571d404fc9828bf2c56140963d5edf0e56160f9b2f3bb61b282c07f8fc8134f023c98fd684bddcb12daf7b0f14d951c
  languageName: node
  linkType: hard

"dom-walk@npm:^0.1.0":
  version: 0.1.2
  resolution: "dom-walk@npm:0.1.2"
  checksum: 19eb0ce9c6de39d5e231530685248545d9cd2bd97b2cb3486e0bfc0f2a393a9addddfd5557463a932b52fdfcf68ad2a619020cd2c74a5fe46fbecaa8e80872f3
  languageName: node
  linkType: hard

"dotenv@npm:^10.0.0":
  version: 10.0.0
  resolution: "dotenv@npm:10.0.0"
  checksum: f412c5fe8c24fbe313d302d2500e247ba8a1946492db405a4de4d30dd0eb186a88a43f13c958c5a7de303938949c4231c56994f97d05c4bc1f22478d631b4005
  languageName: node
  linkType: hard

"duplexer2@npm:^0.1.2":
  version: 0.1.4
  resolution: "duplexer2@npm:0.1.4"
  dependencies:
    readable-stream: ^2.0.2
  checksum: 744961f03c7f54313f90555ac20284a3fb7bf22fdff6538f041a86c22499560eb6eac9d30ab5768054137cb40e6b18b40f621094e0261d7d8c35a37b7a5ad241
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.530":
  version: 1.4.532
  resolution: "electron-to-chromium@npm:1.4.532"
  checksum: e9f77b5d6df84aa1f7598359ec2c988c3758e58106e63f2a0a6dc4756a6733b126316e61a79a2a6643aa2a0f9a1cf9ebe66c817dcb970a3fc9d8190342ef070a
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.12.0":
  version: 5.15.0
  resolution: "enhanced-resolve@npm:5.15.0"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: fbd8cdc9263be71cc737aa8a7d6c57b43d6aa38f6cc75dde6fcd3598a130cc465f979d2f4d01bb3bf475acb43817749c79f8eef9be048683602ca91ab52e4f11
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: ^1.3.4
  checksum: 3b916d2d14c6682f287c8bfa28e14672f47eafe832701080e420e7cdbaebb2c50293868256a95706ac2330fe078cf5664713158b49bc30d7a5f2ac229ded0e18
  languageName: node
  linkType: hard

"es-abstract@npm:^1.22.1":
  version: 1.22.2
  resolution: "es-abstract@npm:1.22.2"
  dependencies:
    array-buffer-byte-length: ^1.0.0
    arraybuffer.prototype.slice: ^1.0.2
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    es-set-tostringtag: ^2.0.1
    es-to-primitive: ^1.2.1
    function.prototype.name: ^1.1.6
    get-intrinsic: ^1.2.1
    get-symbol-description: ^1.0.0
    globalthis: ^1.0.3
    gopd: ^1.0.1
    has: ^1.0.3
    has-property-descriptors: ^1.0.0
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
    internal-slot: ^1.0.5
    is-array-buffer: ^3.0.2
    is-callable: ^1.2.7
    is-negative-zero: ^2.0.2
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.2
    is-string: ^1.0.7
    is-typed-array: ^1.1.12
    is-weakref: ^1.0.2
    object-inspect: ^1.12.3
    object-keys: ^1.1.1
    object.assign: ^4.1.4
    regexp.prototype.flags: ^1.5.1
    safe-array-concat: ^1.0.1
    safe-regex-test: ^1.0.0
    string.prototype.trim: ^1.2.8
    string.prototype.trimend: ^1.0.7
    string.prototype.trimstart: ^1.0.7
    typed-array-buffer: ^1.0.0
    typed-array-byte-length: ^1.0.0
    typed-array-byte-offset: ^1.0.0
    typed-array-length: ^1.0.4
    unbox-primitive: ^1.0.2
    which-typed-array: ^1.1.11
  checksum: cc70e592d360d7d729859013dee7a610c6b27ed8630df0547c16b0d16d9fe6505a70ee14d1af08d970fdd132b3f88c9ca7815ce72c9011608abf8ab0e55fc515
  languageName: node
  linkType: hard

"es-abstract@npm:^1.22.3, es-abstract@npm:^1.23.0, es-abstract@npm:^1.23.2":
  version: 1.23.5
  resolution: "es-abstract@npm:1.23.5"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    arraybuffer.prototype.slice: ^1.0.3
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    data-view-buffer: ^1.0.1
    data-view-byte-length: ^1.0.1
    data-view-byte-offset: ^1.0.0
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-set-tostringtag: ^2.0.3
    es-to-primitive: ^1.2.1
    function.prototype.name: ^1.1.6
    get-intrinsic: ^1.2.4
    get-symbol-description: ^1.0.2
    globalthis: ^1.0.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
    has-proto: ^1.0.3
    has-symbols: ^1.0.3
    hasown: ^2.0.2
    internal-slot: ^1.0.7
    is-array-buffer: ^3.0.4
    is-callable: ^1.2.7
    is-data-view: ^1.0.1
    is-negative-zero: ^2.0.3
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.3
    is-string: ^1.0.7
    is-typed-array: ^1.1.13
    is-weakref: ^1.0.2
    object-inspect: ^1.13.3
    object-keys: ^1.1.1
    object.assign: ^4.1.5
    regexp.prototype.flags: ^1.5.3
    safe-array-concat: ^1.1.2
    safe-regex-test: ^1.0.3
    string.prototype.trim: ^1.2.9
    string.prototype.trimend: ^1.0.8
    string.prototype.trimstart: ^1.0.8
    typed-array-buffer: ^1.0.2
    typed-array-byte-length: ^1.0.1
    typed-array-byte-offset: ^1.0.2
    typed-array-length: ^1.0.6
    unbox-primitive: ^1.0.2
    which-typed-array: ^1.1.15
  checksum: 17c81f8a42f0322fd11e0025d3c2229ecfd7923560c710906b8e68660e19c42322750dcedf8ba5cf28bae50d5befd8174d3903ac50dbabb336d3efc3aabed2ee
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-define-property@npm:1.0.0"
  dependencies:
    get-intrinsic: ^1.2.4
  checksum: f66ece0a887b6dca71848fa71f70461357c0e4e7249696f81bad0a1f347eed7b31262af4a29f5d726dc026426f085483b6b90301855e647aa8e21936f07293c6
  languageName: node
  linkType: hard

"es-errors@npm:^1.2.1, es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.0.12":
  version: 1.0.15
  resolution: "es-iterator-helpers@npm:1.0.15"
  dependencies:
    asynciterator.prototype: ^1.0.0
    call-bind: ^1.0.2
    define-properties: ^1.2.1
    es-abstract: ^1.22.1
    es-set-tostringtag: ^2.0.1
    function-bind: ^1.1.1
    get-intrinsic: ^1.2.1
    globalthis: ^1.0.3
    has-property-descriptors: ^1.0.0
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
    internal-slot: ^1.0.5
    iterator.prototype: ^1.1.2
    safe-array-concat: ^1.0.1
  checksum: 50081ae5c549efe62e5c1d244df0194b40b075f7897fc2116b7e1aa437eb3c41f946d2afda18c33f9b31266ec544765932542765af839f76fa6d7b7855d1e0e1
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-object-atoms@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
  checksum: 26f0ff78ab93b63394e8403c353842b2272836968de4eafe97656adfb8a7c84b9099bf0fe96ed58f4a4cddc860f6e34c77f91649a58a5daa4a9c40b902744e3c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.1":
  version: 2.0.1
  resolution: "es-set-tostringtag@npm:2.0.1"
  dependencies:
    get-intrinsic: ^1.1.3
    has: ^1.0.3
    has-tostringtag: ^1.0.0
  checksum: ec416a12948cefb4b2a5932e62093a7cf36ddc3efd58d6c58ca7ae7064475ace556434b869b0bbeb0c365f1032a8ccd577211101234b69837ad83ad204fff884
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3":
  version: 2.0.3
  resolution: "es-set-tostringtag@npm:2.0.3"
  dependencies:
    get-intrinsic: ^1.2.4
    has-tostringtag: ^1.0.2
    hasown: ^2.0.1
  checksum: 7227fa48a41c0ce83e0377b11130d324ac797390688135b8da5c28994c0165be8b252e15cd1de41e1325e5a5412511586960213e88f9ab4a5e7d028895db5129
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-shim-unscopables@npm:1.0.0"
  dependencies:
    has: ^1.0.3
  checksum: 83e95cadbb6ee44d3644dfad60dcad7929edbc42c85e66c3e99aefd68a3a5c5665f2686885cddb47dfeabfd77bd5ea5a7060f2092a955a729bbd8834f0d86fa1
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2":
  version: 1.0.2
  resolution: "es-shim-unscopables@npm:1.0.2"
  dependencies:
    hasown: ^2.0.0
  checksum: 432bd527c62065da09ed1d37a3f8e623c423683285e6188108286f4a1e8e164a5bcbfbc0051557c7d14633cd2a41ce24c7048e6bbb66a985413fd32f1be72626
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-to-primitive@npm:1.2.1"
  dependencies:
    is-callable: ^1.1.4
    is-date-object: ^1.0.1
    is-symbol: ^1.0.2
  checksum: 4ead6671a2c1402619bdd77f3503991232ca15e17e46222b0a41a5d81aebc8740a77822f5b3c965008e631153e9ef0580540007744521e72de8e33599fca2eed
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: a3e2a99f07acb74b3ad4989c48ca0c3140f69f923e56d0cba0526240ee470b91010f9d39001f2a4a313841d237ede70a729e92125191ba5d21e74b106800b133
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-config-next@npm:^13.2.4":
  version: 13.5.3
  resolution: "eslint-config-next@npm:13.5.3"
  dependencies:
    "@next/eslint-plugin-next": 13.5.3
    "@rushstack/eslint-patch": ^1.3.3
    "@typescript-eslint/parser": ^5.4.2 || ^6.0.0
    eslint-import-resolver-node: ^0.3.6
    eslint-import-resolver-typescript: ^3.5.2
    eslint-plugin-import: ^2.28.1
    eslint-plugin-jsx-a11y: ^6.7.1
    eslint-plugin-react: ^7.33.2
    eslint-plugin-react-hooks: ^4.5.0 || 5.0.0-canary-7118f5dd7-20230705
  peerDependencies:
    eslint: ^7.23.0 || ^8.0.0
    typescript: ">=3.3.1"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 0990e262bc93060fba7dd77296ae4c775ac10cede2c78b50183810b08da917010d7e6d5283afc3fb0d026336121818ccf96629b94eb919981161496bf39d57c6
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.6, eslint-import-resolver-node@npm:^0.3.7, eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: ^3.2.7
    is-core-module: ^2.13.0
    resolve: ^1.22.4
  checksum: 439b91271236b452d478d0522a44482e8c8540bf9df9bd744062ebb89ab45727a3acd03366a6ba2bdbcde8f9f718bab7fe8db64688aca75acf37e04eafd25e22
  languageName: node
  linkType: hard

"eslint-import-resolver-typescript@npm:^3.5.2":
  version: 3.6.1
  resolution: "eslint-import-resolver-typescript@npm:3.6.1"
  dependencies:
    debug: ^4.3.4
    enhanced-resolve: ^5.12.0
    eslint-module-utils: ^2.7.4
    fast-glob: ^3.3.1
    get-tsconfig: ^4.5.0
    is-core-module: ^2.11.0
    is-glob: ^4.0.3
  peerDependencies:
    eslint: "*"
    eslint-plugin-import: "*"
  checksum: 454fa0646533050fb57f13d27daf8c71f51b0bb9156d6a461290ccb8576d892209fcc6702a89553f3f5ea8e5b407395ca2e5de169a952c953685f1f7c46b4496
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.0":
  version: 2.12.0
  resolution: "eslint-module-utils@npm:2.12.0"
  dependencies:
    debug: ^3.2.7
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: be3ac52e0971c6f46daeb1a7e760e45c7c45f820c8cc211799f85f10f04ccbf7afc17039165d56cb2da7f7ca9cec2b3a777013cddf0b976784b37eb9efa24180
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.7.4, eslint-module-utils@npm:^2.8.0":
  version: 2.8.0
  resolution: "eslint-module-utils@npm:2.8.0"
  dependencies:
    debug: ^3.2.7
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 74c6dfea7641ebcfe174be61168541a11a14aa8d72e515f5f09af55cd0d0862686104b0524aa4b8e0ce66418a44aa38a94d2588743db5fd07a6b49ffd16921d2
  languageName: node
  linkType: hard

"eslint-plugin-es@npm:^3.0.0":
  version: 3.0.1
  resolution: "eslint-plugin-es@npm:3.0.1"
  dependencies:
    eslint-utils: ^2.0.0
    regexpp: ^3.0.0
  peerDependencies:
    eslint: ">=4.19.1"
  checksum: e57592c52301ee8ddc296ae44216df007f3a870bcb3be8d1fbdb909a1d3a3efe3fa3785de02066f9eba1d6466b722d3eb3cc3f8b75b3cf6a1cbded31ac6298e4
  languageName: node
  linkType: hard

"eslint-plugin-eslint-plugin@npm:^5.1.1":
  version: 5.1.1
  resolution: "eslint-plugin-eslint-plugin@npm:5.1.1"
  dependencies:
    eslint-utils: ^3.0.0
    estraverse: ^5.3.0
  peerDependencies:
    eslint: ">=7.0.0"
  checksum: a76e66708d8b79f4703a0722d7ed5e53af978c588fb60fe47936a32b377c08e3b2cf92f51180414d70be02d66f7fef0a3dad65d2e7b27873f24292e6923b7aee
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.28.1":
  version: 2.28.1
  resolution: "eslint-plugin-import@npm:2.28.1"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.findlastindex: ^1.2.2
    array.prototype.flat: ^1.3.1
    array.prototype.flatmap: ^1.3.1
    debug: ^3.2.7
    doctrine: ^2.1.0
    eslint-import-resolver-node: ^0.3.7
    eslint-module-utils: ^2.8.0
    has: ^1.0.3
    is-core-module: ^2.13.0
    is-glob: ^4.0.3
    minimatch: ^3.1.2
    object.fromentries: ^2.0.6
    object.groupby: ^1.0.0
    object.values: ^1.1.6
    semver: ^6.3.1
    tsconfig-paths: ^3.14.2
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
  checksum: e8ae6dd8f06d8adf685f9c1cfd46ac9e053e344a05c4090767e83b63a85c8421ada389807a39e73c643b9bff156715c122e89778169110ed68d6428e12607edf
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.29.1":
  version: 2.31.0
  resolution: "eslint-plugin-import@npm:2.31.0"
  dependencies:
    "@rtsao/scc": ^1.1.0
    array-includes: ^3.1.8
    array.prototype.findlastindex: ^1.2.5
    array.prototype.flat: ^1.3.2
    array.prototype.flatmap: ^1.3.2
    debug: ^3.2.7
    doctrine: ^2.1.0
    eslint-import-resolver-node: ^0.3.9
    eslint-module-utils: ^2.12.0
    hasown: ^2.0.2
    is-core-module: ^2.15.1
    is-glob: ^4.0.3
    minimatch: ^3.1.2
    object.fromentries: ^2.0.8
    object.groupby: ^1.0.3
    object.values: ^1.2.0
    semver: ^6.3.1
    string.prototype.trimend: ^1.0.8
    tsconfig-paths: ^3.15.0
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: b1d2ac268b3582ff1af2a72a2c476eae4d250c100f2e335b6e102036e4a35efa530b80ec578dfc36761fabb34a635b9bf5ab071abe9d4404a4bb054fdf22d415
  languageName: node
  linkType: hard

"eslint-plugin-jsx-a11y@npm:^6.7.1":
  version: 6.7.1
  resolution: "eslint-plugin-jsx-a11y@npm:6.7.1"
  dependencies:
    "@babel/runtime": ^7.20.7
    aria-query: ^5.1.3
    array-includes: ^3.1.6
    array.prototype.flatmap: ^1.3.1
    ast-types-flow: ^0.0.7
    axe-core: ^4.6.2
    axobject-query: ^3.1.1
    damerau-levenshtein: ^1.0.8
    emoji-regex: ^9.2.2
    has: ^1.0.3
    jsx-ast-utils: ^3.3.3
    language-tags: =1.0.5
    minimatch: ^3.1.2
    object.entries: ^1.1.6
    object.fromentries: ^2.0.6
    semver: ^6.3.0
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8
  checksum: f166dd5fe7257c7b891c6692e6a3ede6f237a14043ae3d97581daf318fc5833ddc6b4871aa34ab7656187430170500f6d806895747ea17ecdf8231a666c3c2fd
  languageName: node
  linkType: hard

"eslint-plugin-node@npm:^11.1.0":
  version: 11.1.0
  resolution: "eslint-plugin-node@npm:11.1.0"
  dependencies:
    eslint-plugin-es: ^3.0.0
    eslint-utils: ^2.0.0
    ignore: ^5.1.1
    minimatch: ^3.0.4
    resolve: ^1.10.1
    semver: ^6.1.0
  peerDependencies:
    eslint: ">=5.16.0"
  checksum: 5804c4f8a6e721f183ef31d46fbe3b4e1265832f352810060e0502aeac7de034df83352fc88643b19641bb2163f2587f1bd4119aff0fd21e8d98c57c450e013b
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^4.5.0 || 5.0.0-canary-7118f5dd7-20230705":
  version: 5.0.0-canary-7118f5dd7-20230705
  resolution: "eslint-plugin-react-hooks@npm:5.0.0-canary-7118f5dd7-20230705"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
  checksum: 20e334e60bf5e56cf9f760598411847525c3ff826e6ae7757c8efdc60b33d47a97ddbe1b94ce95956ea9f7bbef37995b19c716be50bd44e6a1e789cba08b6224
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.33.2":
  version: 7.33.2
  resolution: "eslint-plugin-react@npm:7.33.2"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flatmap: ^1.3.1
    array.prototype.tosorted: ^1.1.1
    doctrine: ^2.1.0
    es-iterator-helpers: ^1.0.12
    estraverse: ^5.3.0
    jsx-ast-utils: ^2.4.1 || ^3.0.0
    minimatch: ^3.1.2
    object.entries: ^1.1.6
    object.fromentries: ^2.0.6
    object.hasown: ^1.1.2
    object.values: ^1.1.6
    prop-types: ^15.8.1
    resolve: ^2.0.0-next.4
    semver: ^6.3.1
    string.prototype.matchall: ^4.0.8
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8
  checksum: b4c3d76390b0ae6b6f9fed78170604cc2c04b48e6778a637db339e8e3911ec9ef22510b0ae77c429698151d0f1b245f282177f384105b6830e7b29b9c9b26610
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: ec97dbf5fb04b94e8f4c5a91a7f0a6dd3c55e46bfc7bbcd0e3138c3a76977570e02ed89a1810c778dcd72072ff0e9621ba1379b4babe53921d71e2e4486fda3e
  languageName: node
  linkType: hard

"eslint-utils@npm:^2.0.0":
  version: 2.1.0
  resolution: "eslint-utils@npm:2.1.0"
  dependencies:
    eslint-visitor-keys: ^1.1.0
  checksum: 27500938f348da42100d9e6ad03ae29b3de19ba757ae1a7f4a087bdcf83ac60949bbb54286492ca61fac1f5f3ac8692dd21537ce6214240bf95ad0122f24d71d
  languageName: node
  linkType: hard

"eslint-utils@npm:^3.0.0":
  version: 3.0.0
  resolution: "eslint-utils@npm:3.0.0"
  dependencies:
    eslint-visitor-keys: ^2.0.0
  peerDependencies:
    eslint: ">=5"
  checksum: 0668fe02f5adab2e5a367eee5089f4c39033af20499df88fe4e6aba2015c20720404d8c3d6349b6f716b08fdf91b9da4e5d5481f265049278099c4c836ccb619
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^1.1.0":
  version: 1.3.0
  resolution: "eslint-visitor-keys@npm:1.3.0"
  checksum: 37a19b712f42f4c9027e8ba98c2b06031c17e0c0a4c696cd429bd9ee04eb43889c446f2cd545e1ff51bef9593fcec94ecd2c2ef89129fcbbf3adadbef520376a
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^2.0.0":
  version: 2.1.0
  resolution: "eslint-visitor-keys@npm:2.1.0"
  checksum: e3081d7dd2611a35f0388bbdc2f5da60b3a3c5b8b6e928daffff7391146b434d691577aa95064c8b7faad0b8a680266bcda0a42439c18c717b80e6718d7e267d
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint@npm:^8.35.0":
  version: 8.50.0
  resolution: "eslint@npm:8.50.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.6.1
    "@eslint/eslintrc": ^2.1.2
    "@eslint/js": 8.50.0
    "@humanwhocodes/config-array": ^0.11.11
    "@humanwhocodes/module-importer": ^1.0.1
    "@nodelib/fs.walk": ^1.2.8
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.2
    debug: ^4.3.2
    doctrine: ^3.0.0
    escape-string-regexp: ^4.0.0
    eslint-scope: ^7.2.2
    eslint-visitor-keys: ^3.4.3
    espree: ^9.6.1
    esquery: ^1.4.2
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^6.0.1
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    globals: ^13.19.0
    graphemer: ^1.4.0
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    is-path-inside: ^3.0.3
    js-yaml: ^4.1.0
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.4.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
    strip-ansi: ^6.0.1
    text-table: ^0.2.0
  bin:
    eslint: bin/eslint.js
  checksum: 9ebfe5615dc84700000d218e32ddfdcfc227ca600f65f18e5541ec34f8902a00356a9a8804d9468fd6c8637a5ef6a3897291dad91ba6579d5b32ffeae5e31768
  languageName: node
  linkType: hard

"espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: ^8.9.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^3.4.1
  checksum: eb8c149c7a2a77b3f33a5af80c10875c3abd65450f60b8af6db1bfcfa8f101e21c1e56a561c6dc13b848e18148d43469e7cd208506238554fb5395a9ea5a1ab9
  languageName: node
  linkType: hard

"esquery@npm:^1.4.2":
  version: 1.5.0
  resolution: "esquery@npm:1.5.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: aefb0d2596c230118656cd4ec7532d447333a410a48834d80ea648b1e7b5c9bc9ed8b5e33a89cb04e487b60d622f44cf5713bf4abed7c97343edefdc84a35900
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.12, fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.1":
  version: 3.3.1
  resolution: "fast-glob@npm:3.3.1"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: b6f3add6403e02cf3a798bfbb1183d0f6da2afd368f27456010c0bc1f9640aea308243d4cb2c0ab142f618276e65ecb8be1661d7c62a7b4e5ba774b9ce5432e5
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-loops@npm:^1.1.3":
  version: 1.1.3
  resolution: "fast-loops@npm:1.1.3"
  checksum: b674378ba2ed8364ca1a00768636e88b22201c8d010fa62a8588a4cace04f90bac46714c13cf638be82b03438d2fe813600da32291fb47297a1bd7fa6cef0cee
  languageName: node
  linkType: hard

"fast-shallow-equal@npm:^1.0.0":
  version: 1.0.0
  resolution: "fast-shallow-equal@npm:1.0.0"
  checksum: ae89318ce43c0c46410d9511ac31520d59cfe675bad3d0b1cb5f900b2d635943d788b8370437178e91ae0d0412decc394229c03e69925ade929a8c02da241610
  languageName: node
  linkType: hard

"fastest-stable-stringify@npm:^2.0.2":
  version: 2.0.2
  resolution: "fastest-stable-stringify@npm:2.0.2"
  checksum: 5e2cb166c7bb6f16ac25a1e4be17f6b8d2923234c80739e12c9d21dea376b3128b2c63f90aa2aae7746cfec4dcf188d1d4eb6a964bb484ca133f17c8e9acfacc
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.15.0
  resolution: "fastq@npm:1.15.0"
  dependencies:
    reusify: ^1.0.4
  checksum: 0170e6bfcd5d57a70412440b8ef600da6de3b2a6c5966aeaf0a852d542daff506a0ee92d6de7679d1de82e644bce69d7a574a6c93f0b03964b5337eed75ada1a
  languageName: node
  linkType: hard

"fg-loadcss@npm:^3.1.0":
  version: 3.1.0
  resolution: "fg-loadcss@npm:3.1.0"
  checksum: d8471f4fb4044f1c94a7c44924acda800af0bc0518a6a5f18e88b9b3a2314fdce6900c86f8afb372913c37a695a6ced5b62afc45185edd687cb3b6b6ac7b7e6b
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: ^3.0.4
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: cc283f4e65b504259e64fd969bcf4def4eb08d85565e906b7d36516e87819db52029a76b6363d0f02d0d532f0033c9603b9e2d943d56ee3b0d4f7ad3328ff917
  languageName: node
  linkType: hard

"find-root@npm:^1.1.0":
  version: 1.1.0
  resolution: "find-root@npm:1.1.0"
  checksum: b2a59fe4b6c932eef36c45a048ae8f93c85640212ebe8363164814990ee20f154197505965f3f4f102efc33bfb1cbc26fd17c4a2fc739ebc51b886b137cbefaf
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.1.0
  resolution: "flat-cache@npm:3.1.0"
  dependencies:
    flatted: ^3.2.7
    keyv: ^4.5.3
    rimraf: ^3.0.2
  checksum: 99312601d5b90f44aef403f17f056dc09be7e437703740b166cdc9386d99e681f74e6b6e8bd7d010bda66904ea643c9527276b1b80308a2119741d94108a4d8f
  languageName: node
  linkType: hard

"flatted@npm:^3.2.7":
  version: 3.2.9
  resolution: "flatted@npm:3.2.9"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.14.4":
  version: 1.15.3
  resolution: "follow-redirects@npm:1.15.3"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 584da22ec5420c837bd096559ebfb8fe69d82512d5585004e36a3b4a6ef6d5905780e0c74508c7b72f907d1fa2b7bd339e613859e9c304d0dc96af2027fd0231
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: ^1.1.3
  checksum: 6c48ff2bc63362319c65e2edca4a8e1e3483a2fabc72fbe7feaf8c73db94fc7861bd53bc02c8a66a0c1dd709da6b04eec42e0abdd6b40ce47305ae92a25e5d28
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.1.1
  resolution: "foreground-child@npm:3.1.1"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 139d270bc82dc9e6f8bc045fe2aae4001dc2472157044fdfad376d0a3457f77857fa883c1c8b21b491c6caade9a926a4bed3d3d2e8d3c9202b151a4cbbd0bcd5
  languageName: node
  linkType: hard

"framer-motion@npm:^10.16.4":
  version: 10.16.4
  resolution: "framer-motion@npm:10.16.4"
  dependencies:
    "@emotion/is-prop-valid": ^0.8.2
    tslib: ^2.4.0
  peerDependencies:
    react: ^18.0.0
    react-dom: ^18.0.0
  dependenciesMeta:
    "@emotion/is-prop-valid":
      optional: true
  peerDependenciesMeta:
    react:
      optional: true
    react-dom:
      optional: true
  checksum: 57eb252f25a2c4ee14b024295c6a1162a53a05e0321bdb9c8a22ec266fbe777832823eaa0309e42854170fcde16c42915c6c5d0208b628fd000d6fab013c501f
  languageName: node
  linkType: hard

"fs-extra@npm:^0.26.5":
  version: 0.26.7
  resolution: "fs-extra@npm:0.26.7"
  dependencies:
    graceful-fs: ^4.1.2
    jsonfile: ^2.1.0
    klaw: ^1.0.0
    path-is-absolute: ^1.0.0
    rimraf: ^2.2.8
  checksum: bb4d02348e9b036794e8c9da7be6f89a851dd37014d691893b54ebdaadffe2eab1c7578c3bc2f0deaa1e204030cda282d006ec020f176e0689fcd0e74499367e
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs-promise@npm:^0.5.0":
  version: 0.5.0
  resolution: "fs-promise@npm:0.5.0"
  dependencies:
    any-promise: ^1.0.0
    fs-extra: ^0.26.5
    mz: ^2.3.1
    thenify-all: ^1.6.0
  checksum: eb93a5642894c8ac8b8f88008b628f78c30d44de550fd430785102928c28df1aaed86198d33551926b593e252aef6832a0772a458c6a0ff4c8a323686de967ef
  languageName: node
  linkType: hard

"fs-readdir-recursive@npm:^1.1.0":
  version: 1.1.0
  resolution: "fs-readdir-recursive@npm:1.1.0"
  checksum: 29d50f3d2128391c7fc9fd051c8b7ea45bcc8aa84daf31ef52b17218e20bfd2bd34d02382742801954cc8d1905832b68227f6b680a666ce525d8b6b75068ad1e
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1":
  version: 1.1.1
  resolution: "function-bind@npm:1.1.1"
  checksum: b32fbaebb3f8ec4969f033073b43f5c8befbb58f1a79e12f1d7490358150359ebd92f49e72ff0144f65f2c48ea2a605bff2d07965f548f6474fd8efd95bf361a
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.5, function.prototype.name@npm:^1.1.6":
  version: 1.1.6
  resolution: "function.prototype.name@npm:1.1.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    functions-have-names: ^1.2.3
  checksum: 7a3f9bd98adab09a07f6e1f03da03d3f7c26abbdeaeee15223f6c04a9fb5674792bdf5e689dac19b97ac71de6aad2027ba3048a9b883aa1b3173eed6ab07f479
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"gauge@npm:^4.0.3":
  version: 4.0.4
  resolution: "gauge@npm:4.0.4"
  dependencies:
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.3
    console-control-strings: ^1.1.0
    has-unicode: ^2.0.1
    signal-exit: ^3.0.7
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.5
  checksum: 788b6bfe52f1dd8e263cda800c26ac0ca2ff6de0b6eee2fe0d9e3abf15e149b651bd27bf5226be10e6e3edb5c4e5d5985a5a1a98137e7a892f75eff76467ad2d
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2, get-intrinsic@npm:^1.1.1, get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.0, get-intrinsic@npm:^1.2.1":
  version: 1.2.1
  resolution: "get-intrinsic@npm:1.2.1"
  dependencies:
    function-bind: ^1.1.1
    has: ^1.0.3
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
  checksum: 5b61d88552c24b0cf6fa2d1b3bc5459d7306f699de060d76442cce49a4721f52b8c560a33ab392cf5575b7810277d54ded9d4d39a1ea61855619ebc005aa7e5f
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.3, get-intrinsic@npm:^1.2.4":
  version: 1.2.4
  resolution: "get-intrinsic@npm:1.2.4"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
    hasown: ^2.0.0
  checksum: 414e3cdf2c203d1b9d7d33111df746a4512a1aa622770b361dadddf8ed0b5aeb26c560f49ca077e24bfafb0acb55ca908d1f709216ccba33ffc548ec8a79a951
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.0.0":
  version: 1.0.0
  resolution: "get-symbol-description@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.1
  checksum: 9ceff8fe968f9270a37a1f73bf3f1f7bda69ca80f4f80850670e0e7b9444ff99323f7ac52f96567f8b5f5fbe7ac717a0d81d3407c7313e82810c6199446a5247
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.0.2":
  version: 1.0.2
  resolution: "get-symbol-description@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.5
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.4
  checksum: e1cb53bc211f9dbe9691a4f97a46837a553c4e7caadd0488dc24ac694db8a390b93edd412b48dcdd0b4bbb4c595de1709effc75fc87c0839deedc6968f5bd973
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.5.0":
  version: 4.7.2
  resolution: "get-tsconfig@npm:4.7.2"
  dependencies:
    resolve-pkg-maps: ^1.0.0
  checksum: 172358903250eff0103943f816e8a4e51d29b8e5449058bdf7266714a908a48239f6884308bd3a6ff28b09f692b9533dbebfd183ab63e4e14f073cda91f1bca9
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: e795f4e8f06d2a15e86f76e4d92751cf8bbfcf0157cea5c2f0f35678a8195a750b34096b1256e436f0cebc1883b5ff0888c47348443e69546a5a87f9e1eb1167
  languageName: node
  linkType: hard

"glob@npm:7.1.7":
  version: 7.1.7
  resolution: "glob@npm:7.1.7"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: b61f48973bbdcf5159997b0874a2165db572b368b931135832599875919c237fc05c12984e38fe828e69aa8a921eb0e8a4997266211c517c9cfaae8a93988bb8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.3.10
  resolution: "glob@npm:10.3.10"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^2.3.5
    minimatch: ^9.0.1
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
    path-scurry: ^1.10.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 4f2fe2511e157b5a3f525a54092169a5f92405f24d2aed3142f4411df328baca13059f4182f1db1bf933e2c69c0bd89e57ae87edd8950cba8c7ccbe84f721cf3
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.4, glob@npm:^7.2.0":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"global@npm:4.4.0, global@npm:^4.3.1, global@npm:^4.3.2, global@npm:^4.4.0, global@npm:~4.4.0":
  version: 4.4.0
  resolution: "global@npm:4.4.0"
  dependencies:
    min-document: ^2.19.0
    process: ^0.11.10
  checksum: 9c057557c8f5a5bcfbeb9378ba4fe2255d04679452be504608dd5f13b54edf79f7be1db1031ea06a4ec6edd3b9f5f17d2d172fb47e6c69dae57fd84b7e72b77f
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globals@npm:^13.19.0":
  version: 13.22.0
  resolution: "globals@npm:13.22.0"
  dependencies:
    type-fest: ^0.20.2
  checksum: 64af5a09565341432770444085f7aa98b54331c3b69732e0de411003921fa2dd060222ae7b50bec0b98f29c4d00b4f49bf434049ba9f7c36ca4ee1773f60458c
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.3":
  version: 1.0.3
  resolution: "globalthis@npm:1.0.3"
  dependencies:
    define-properties: ^1.1.3
  checksum: fbd7d760dc464c886d0196166d92e5ffb4c84d0730846d6621a39fbbc068aeeb9c8d1421ad330e94b7bca4bb4ea092f5f21f3d36077812af5d098b4dc006c998
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: ^1.2.1
    gopd: ^1.0.1
  checksum: 39ad667ad9f01476474633a1834a70842041f70a55571e8dcef5fb957980a92da5022db5430fca8aecc5d47704ae30618c0bc877a579c70710c904e9ef06108a
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b4be8885e0cfa018fc783792942d53926c35c50b3aefd3fdcfb9d22c627639dc26bd2327a40a0b74b074100ce95bb7187bfeae2f236856aa3de183af7a02aea6
  languageName: node
  linkType: hard

"goober@npm:^2.0.33":
  version: 2.1.13
  resolution: "goober@npm:2.1.13"
  peerDependencies:
    csstype: ^3.0.10
  checksum: 0c00b90d26d1a2fad432e311fd4f47bc9fef1eee2a733158d9e2c72a89cf76d414090d063a8d20fe378f2b2b8087df0a83b0f00a3244d1466b97a0d3b14344a7
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.1.3
  checksum: a5ccfb8806e0917a94e0b3de2af2ea4979c1da920bc381667c260e00e7cafdbe844e2cb9c5bcfef4e5412e8bf73bab837285bc35c7ba73aaaf0134d4583393a6
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.1.9, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.1, has-bigints@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-bigints@npm:1.0.2"
  checksum: 390e31e7be7e5c6fe68b81babb73dfc35d413604d7ee5f56da101417027a4b4ce6a27e46eff97ad040c835b5d228676eae99a9b5c3bc0e23c8e81a49241ff45b
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-property-descriptors@npm:1.0.0"
  dependencies:
    get-intrinsic: ^1.1.1
  checksum: a6d3f0a266d0294d972e354782e872e2fe1b6495b321e6ef678c9b7a06a40408a6891817350c62e752adced73a94ac903c54734fee05bf65b1905ee1368194bb
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "has-proto@npm:1.0.1"
  checksum: febc5b5b531de8022806ad7407935e2135f1cc9e64636c3916c6842bd7995994ca3b29871ecd7954bd35f9e2986c17b3b227880484d22259e2f8e6ce63fd383e
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-proto@npm:1.0.3"
  checksum: fe7c3d50b33f50f3933a04413ed1f69441d21d2d2944f81036276d30635cad9279f6b43bc8f32036c31ebdfcf6e731150f46c1907ad90c669ffe9b066c3ba5c4
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.2, has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: a054c40c631c0d5741a8285010a0777ea0c068f99ed43e5d6eb12972da223f8af553a455132fdb0801bdcfa0e0f443c0c03a68d8555aa529b3144b446c3f2410
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-tostringtag@npm:1.0.0"
  dependencies:
    has-symbols: ^1.0.2
  checksum: cc12eb28cb6ae22369ebaad3a8ab0799ed61270991be88f208d508076a1e99abe4198c965935ce85ea90b60c94ddda73693b0920b58e7ead048b4a391b502c1c
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 1eab07a7436512db0be40a710b29b5dc21fa04880b7f63c9980b706683127e3c1b57cb80ea96d47991bdae2dfe479604f6a1ba410106ee1046a41d1bd0814400
  languageName: node
  linkType: hard

"has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: ^1.1.1
  checksum: b9ad53d53be4af90ce5d1c38331e712522417d017d5ef1ebd0507e07c2fbad8686fffb8e12ddecd4c39ca9b9b47431afbb975b8abf7f3c3b82c98e9aad052792
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0, hasown@npm:^2.0.1, hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.3.0, hoist-non-react-statics@npm:^3.3.1, hoist-non-react-statics@npm:^3.3.2":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: ^16.7.0
  checksum: b1538270429b13901ee586aa44f4cc3ecd8831c061d06cb8322e50ea17b3f5ce4d0e2e66394761e6c8e152cd8c34fb3b4b690116c6ce2bd45b18c746516cb9e8
  languageName: node
  linkType: hard

"html-parse-stringify@npm:^3.0.1":
  version: 3.0.1
  resolution: "html-parse-stringify@npm:3.0.1"
  dependencies:
    void-elements: 3.1.0
  checksum: 334fdebd4b5c355dba8e95284cead6f62bf865a2359da2759b039db58c805646350016d2017875718bc3c4b9bf81a0d11be5ee0cf4774a3a5a7b97cde21cfd67
  languageName: node
  linkType: hard

"html-tokenize@npm:^2.0.0":
  version: 2.0.1
  resolution: "html-tokenize@npm:2.0.1"
  dependencies:
    buffer-from: ~0.1.1
    inherits: ~2.0.1
    minimist: ~1.2.5
    readable-stream: ~1.0.27-1
    through2: ~0.4.1
  bin:
    html-tokenize: bin/cmd.js
  checksum: 4e04078fd22cf274fc1fa430490af3feda1c3bc4dd2fc88880caf6c2e816992d508bc44a7b16721b713a6b98d880f43e10ea4b169529056134cd488403adc8fc
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": 2
    agent-base: 6
    debug: 4
  checksum: e2ee1ff1656a131953839b2a19cd1f3a52d97c25ba87bd2559af6ae87114abf60971e498021f9b73f9fd78aea8876d1fb0d4656aac8a03c6caa9fc175f22b786
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 571fccdf38184f05943e12d37d6ce38197becdd69e58d03f43637f7fa1269cf303a7d228aa27e5b27bbd3af8f09fd938e1c91dcfefff2df7ba77c20ed8dfc765
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"hyphenate-style-name@npm:^1.0.3":
  version: 1.0.4
  resolution: "hyphenate-style-name@npm:1.0.4"
  checksum: 4f5bf4b055089754924babebaa23c17845937bcca6aee95d5d015f8fa1e6814279002bd6a9e541e3fac2cd02519fc76305396727066c57c8e21a7e73e7a12137
  languageName: node
  linkType: hard

"i18next-fs-backend@npm:^2.1.1":
  version: 2.2.0
  resolution: "i18next-fs-backend@npm:2.2.0"
  checksum: 33e00ccc8ec66a9fc20363513c3189a201a59e8601f167f0483c0a0d53ecee1dd4bb43b83d0f5661784e7a7ca3e43cd9c771d426cec73d8f819b9b823b77d724
  languageName: node
  linkType: hard

"i18next@npm:23.2.11":
  version: 23.2.11
  resolution: "i18next@npm:23.2.11"
  dependencies:
    "@babel/runtime": ^7.22.5
  checksum: ce4d16db9404a2a1fd929029ff0106dd92c7d719b6157e4551516ef2ab2917688a5ad712326f5869829614b01601839aa2900f40296b6c4a1efa9b802491477f
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ignore@npm:^5.1.1, ignore@npm:^5.2.0, ignore@npm:^5.2.4":
  version: 5.2.4
  resolution: "ignore@npm:5.2.4"
  checksum: 3d4c309c6006e2621659311783eaea7ebcd41fe4ca1d78c91c473157ad6666a57a2df790fe0d07a12300d9aac2888204d7be8d59f9aaf665b1c7fcdb432517ef
  languageName: node
  linkType: hard

"immer@npm:^9.0.21":
  version: 9.0.21
  resolution: "immer@npm:9.0.21"
  checksum: 70e3c274165995352f6936695f0ef4723c52c92c92dd0e9afdfe008175af39fa28e76aafb3a2ca9d57d1fb8f796efc4dd1e1cc36f18d33fa5b74f3dfb0375432
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0":
  version: 4.3.4
  resolution: "immutable@npm:4.3.4"
  checksum: de3edd964c394bab83432429d3fb0b4816b42f56050f2ca913ba520bd3068ec3e504230d0800332d3abc478616e8f55d3787424a90d0952e6aba864524f1afc3
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"individual@npm:^2.0.0":
  version: 2.0.0
  resolution: "individual@npm:2.0.0"
  checksum: 34f071ade77365e2cdb9e034e7dc92930450ce427415b9ef975a2c2a455b40aa9e071ae7888972f3d2ec7d977a32cf1af46e0b8d602d70d05da5b9bbc9e23392
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.3, inherits@npm:~2.0.1, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"inline-style-prefixer@npm:^6.0.0":
  version: 6.0.4
  resolution: "inline-style-prefixer@npm:6.0.4"
  dependencies:
    css-in-js-utils: ^3.1.0
    fast-loops: ^1.1.3
  checksum: caf7a75d18acbedc7e3b8bfac17563082becd2df6b65accad964a6afdf490329b42315c37fe65ba0177cc10fd32809eb40d62aba23a0118c74d87d4fc58defa2
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.5":
  version: 1.0.5
  resolution: "internal-slot@npm:1.0.5"
  dependencies:
    get-intrinsic: ^1.2.0
    has: ^1.0.3
    side-channel: ^1.0.4
  checksum: 97e84046bf9e7574d0956bd98d7162313ce7057883b6db6c5c7b5e5f05688864b0978ba07610c726d15d66544ffe4b1050107d93f8a39ebc59b15d8b429b497a
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.7":
  version: 1.0.7
  resolution: "internal-slot@npm:1.0.7"
  dependencies:
    es-errors: ^1.3.0
    hasown: ^2.0.0
    side-channel: ^1.0.4
  checksum: cadc5eea5d7d9bc2342e93aae9f31f04c196afebb11bde97448327049f492cd7081e18623ae71388aac9cd237b692ca3a105be9c68ac39c1dec679d7409e33eb
  languageName: node
  linkType: hard

"ip@npm:^2.0.0":
  version: 2.0.0
  resolution: "ip@npm:2.0.0"
  checksum: cfcfac6b873b701996d71ec82a7dd27ba92450afdb421e356f44044ed688df04567344c36cbacea7d01b1c39a4c732dc012570ebe9bebfb06f27314bca625349
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.1, is-array-buffer@npm:^3.0.2":
  version: 3.0.2
  resolution: "is-array-buffer@npm:3.0.2"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.0
    is-typed-array: ^1.1.10
  checksum: dcac9dda66ff17df9cabdc58214172bf41082f956eab30bb0d86bc0fab1e44b690fc8e1f855cf2481245caf4e8a5a006a982a71ddccec84032ed41f9d8da8c14
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4":
  version: 3.0.4
  resolution: "is-array-buffer@npm:3.0.4"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.1
  checksum: e4e3e6ef0ff2239e75371d221f74bc3c26a03564a22efb39f6bb02609b598917ddeecef4e8c877df2a25888f247a98198959842a5e73236bc7f22cabdf6351a7
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-async-function@npm:2.0.0"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: e3471d95e6c014bf37cad8a93f2f4b6aac962178e0a5041e8903147166964fdc1c5c1d2ef87e86d77322c370ca18f2ea004fa7420581fa747bcaf7c223069dbd
  languageName: node
  linkType: hard

"is-bigint@npm:^1.0.1":
  version: 1.0.4
  resolution: "is-bigint@npm:1.0.4"
  dependencies:
    has-bigints: ^1.0.1
  checksum: c56edfe09b1154f8668e53ebe8252b6f185ee852a50f9b41e8d921cb2bed425652049fbe438723f6cb48a63ca1aa051e948e7e401e093477c99c84eba244f666
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.1.0":
  version: 1.1.2
  resolution: "is-boolean-object@npm:1.1.2"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: c03b23dbaacadc18940defb12c1c0e3aaece7553ef58b162a0f6bba0c2a7e1551b59f365b91e00d2dbac0522392d576ef322628cb1d036a0fe51eb466db67222
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3, is-callable@npm:^1.1.4, is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-core-module@npm:^2.11.0, is-core-module@npm:^2.13.0, is-core-module@npm:^2.9.0":
  version: 2.13.0
  resolution: "is-core-module@npm:2.13.0"
  dependencies:
    has: ^1.0.3
  checksum: 053ab101fb390bfeb2333360fd131387bed54e476b26860dc7f5a700bbf34a0ec4454f7c8c4d43e8a0030957e4b3db6e16d35e1890ea6fb654c833095e040355
  languageName: node
  linkType: hard

"is-core-module@npm:^2.15.1":
  version: 2.15.1
  resolution: "is-core-module@npm:2.15.1"
  dependencies:
    hasown: ^2.0.2
  checksum: df134c168115690724b62018c37b2f5bba0d5745fa16960b329c5a00883a8bea6a5632fdb1e3efcce237c201826ba09f93197b7cd95577ea56b0df335be23633
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-data-view@npm:1.0.1"
  dependencies:
    is-typed-array: ^1.1.13
  checksum: 4ba4562ac2b2ec005fefe48269d6bd0152785458cd253c746154ffb8a8ab506a29d0cfb3b74af87513843776a88e4981ae25c89457bf640a33748eab1a7216b5
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.1, is-date-object@npm:^1.0.5":
  version: 1.0.5
  resolution: "is-date-object@npm:1.0.5"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: baa9077cdf15eb7b58c79398604ca57379b2fc4cf9aa7a9b9e295278648f628c9b201400c01c5e0f7afae56507d741185730307cbe7cad3b9f90a77e5ee342fc
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-finalizationregistry@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 4f243a8e06228cd45bdab8608d2cb7abfc20f6f0189c8ac21ea8d603f1f196eabd531ce0bb8e08cbab047e9845ef2c191a3761c9a17ad5cabf8b35499c4ad35d
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-function@npm:^1.0.1":
  version: 1.0.2
  resolution: "is-function@npm:1.0.2"
  checksum: 7d564562e07b4b51359547d3ccc10fb93bb392fd1b8177ae2601ee4982a0ece86d952323fc172a9000743a3971f09689495ab78a1d49a9b14fc97a7e28521dc0
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.0.10
  resolution: "is-generator-function@npm:1.0.10"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: d54644e7dbaccef15ceb1e5d91d680eb5068c9ee9f9eb0a9e04173eb5542c9b51b5ab52c5537f5703e48d5fddfd376817c1ca07a84a407b7115b769d4bdde72b
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-in-browser@npm:^1.0.2, is-in-browser@npm:^1.1.3":
  version: 1.1.3
  resolution: "is-in-browser@npm:1.1.3"
  checksum: 178491f97f6663c0574565701b76f41633dbe065e4bd8d518ce017a8fa25e5109ecb6a3bd8bd55c0aba11b208f86b9f0f9c91f3664e148ebf618b74a74fcaf09
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-map@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-map@npm:2.0.2"
  checksum: ace3d0ecd667bbdefdb1852de601268f67f2db725624b1958f279316e13fecb8fa7df91fd60f690d7417b4ec180712f5a7ee967008e27c65cfd475cc84337728
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-negative-zero@npm:2.0.2"
  checksum: f3232194c47a549da60c3d509c9a09be442507616b69454716692e37ae9f37c4dea264fb208ad0c9f3efd15a796a46b79df07c7e53c6227c32170608b809149a
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: c1e6b23d2070c0539d7b36022d5a94407132411d01aba39ec549af824231f3804b1aea90b5e4e58e807a65d23ceb538ed6e355ce76b267bdd86edb757ffcbdcd
  languageName: node
  linkType: hard

"is-number-object@npm:^1.0.4":
  version: 1.0.7
  resolution: "is-number-object@npm:1.0.7"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: d1e8d01bb0a7134c74649c4e62da0c6118a0bfc6771ea3c560914d52a627873e6920dd0fd0ebc0e12ad2ff4687eac4c308f7e80320b973b2c8a2c8f97a7524f7
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4":
  version: 1.1.4
  resolution: "is-regex@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 362399b33535bc8f386d96c45c9feb04cf7f8b41c182f54174c1a45c9abbbe5e31290bbad09a458583ff6bf3b2048672cdb1881b13289569a7c548370856a652
  languageName: node
  linkType: hard

"is-set@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-set@npm:2.0.2"
  checksum: b64343faf45e9387b97a6fd32be632ee7b269bd8183701f3b3f5b71a7cf00d04450ed8669d0bd08753e08b968beda96fca73a10fd0ff56a32603f64deba55a57
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-shared-array-buffer@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 9508929cf14fdc1afc9d61d723c6e8d34f5e117f0bffda4d97e7a5d88c3a8681f633a74f8e3ad1fe92d5113f9b921dc5ca44356492079612f9a247efbce7032a
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "is-shared-array-buffer@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.7
  checksum: a4fff602c309e64ccaa83b859255a43bb011145a42d3f56f67d9268b55bc7e6d98a5981a1d834186ad3105d6739d21547083fe7259c76c0468483fc538e716d8
  languageName: node
  linkType: hard

"is-string@npm:^1.0.5, is-string@npm:^1.0.7":
  version: 1.0.7
  resolution: "is-string@npm:1.0.7"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: 323b3d04622f78d45077cf89aab783b2f49d24dc641aa89b5ad1a72114cfeff2585efc8c12ef42466dff32bde93d839ad321b26884cf75e5a7892a938b089989
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.2, is-symbol@npm:^1.0.3":
  version: 1.0.4
  resolution: "is-symbol@npm:1.0.4"
  dependencies:
    has-symbols: ^1.0.2
  checksum: 92805812ef590738d9de49d677cd17dfd486794773fb6fa0032d16452af46e9b91bb43ffe82c983570f015b37136f4b53b28b8523bfb10b0ece7a66c31a54510
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.10, is-typed-array@npm:^1.1.12, is-typed-array@npm:^1.1.9":
  version: 1.1.12
  resolution: "is-typed-array@npm:1.1.12"
  dependencies:
    which-typed-array: ^1.1.11
  checksum: 4c89c4a3be07186caddadf92197b17fda663a9d259ea0d44a85f171558270d36059d1c386d34a12cba22dfade5aba497ce22778e866adc9406098c8fc4771796
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13":
  version: 1.1.13
  resolution: "is-typed-array@npm:1.1.13"
  dependencies:
    which-typed-array: ^1.1.14
  checksum: 150f9ada183a61554c91e1c4290086d2c100b0dff45f60b028519be72a8db964da403c48760723bf5253979b8dffe7b544246e0e5351dcd05c5fdb1dcc1dc0f0
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.1":
  version: 2.0.1
  resolution: "is-weakmap@npm:2.0.1"
  checksum: 1222bb7e90c32bdb949226e66d26cb7bce12e1e28e3e1b40bfa6b390ba3e08192a8664a703dff2a00a84825f4e022f9cd58c4599ff9981ab72b1d69479f4f7f6
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-weakref@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 95bd9a57cdcb58c63b1c401c60a474b0f45b94719c30f548c891860f051bc2231575c290a6b420c6bc6e7ed99459d424c652bd5bf9a1d5259505dc35b4bf83de
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-weakset@npm:2.0.2"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.1
  checksum: 5d8698d1fa599a0635d7ca85be9c26d547b317ed8fd83fc75f03efbe75d50001b5eececb1e9971de85fcde84f69ae6f8346bc92d20d55d46201d328e4c74a367
  languageName: node
  linkType: hard

"isarray@npm:0.0.1":
  version: 0.0.1
  resolution: "isarray@npm:0.0.1"
  checksum: 49191f1425681df4a18c2f0f93db3adb85573bcdd6a4482539d98eac9e705d8961317b01175627e860516a2fc45f8f9302db26e5a380a97a520e272e2a40a8d4
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.2":
  version: 1.1.2
  resolution: "iterator.prototype@npm:1.1.2"
  dependencies:
    define-properties: ^1.2.1
    get-intrinsic: ^1.2.1
    has-symbols: ^1.0.3
    reflect.getprototypeof: ^1.0.4
    set-function-name: ^2.0.1
  checksum: d8a507e2ccdc2ce762e8a1d3f4438c5669160ac72b88b648e59a688eec6bc4e64b22338e74000518418d9e693faf2a092d2af21b9ec7dbf7763b037a54701168
  languageName: node
  linkType: hard

"jackspeak@npm:^2.3.5":
  version: 2.3.6
  resolution: "jackspeak@npm:2.3.6"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 57d43ad11eadc98cdfe7496612f6bbb5255ea69fe51ea431162db302c2a11011642f50cfad57288bd0aea78384a0612b16e131944ad8ecd09d619041c8531b54
  languageName: node
  linkType: hard

"js-cookie@npm:^2.2.1":
  version: 2.2.1
  resolution: "js-cookie@npm:2.2.1"
  checksum: 9b1fb980a1c5e624fd4b28ea4867bb30c71e04c4484bb3a42766344c533faa684de9498e443425479ec68609e96e27b60614bfe354877c449c631529b6d932f2
  languageName: node
  linkType: hard

"js-sha256@npm:^0.9.0":
  version: 0.9.0
  resolution: "js-sha256@npm:0.9.0"
  checksum: ffad54b3373f81581e245866abfda50a62c483803a28176dd5c28fd2d313e0bdf830e77dac7ff8afd193c53031618920f3d98daf21cbbe80082753ab639c0365
  languageName: node
  linkType: hard

"js-sha3@npm:0.8.0":
  version: 0.8.0
  resolution: "js-sha3@npm:0.8.0"
  checksum: 75df77c1fc266973f06cce8309ce010e9e9f07ec35ab12022ed29b7f0d9c8757f5a73e1b35aa24840dced0dea7059085aa143d817aea9e188e2a80d569d9adce
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 4dc190771129e12023f729ce20e1e0bfceac84d73a85bc3119f7f938843fe25a4aeccb54b6494dce26fcf263d815f5f31acdefac7cc9329efb8422a4f4d9fa9d
  languageName: node
  linkType: hard

"jsesc@npm:~0.5.0":
  version: 0.5.0
  resolution: "jsesc@npm:0.5.0"
  bin:
    jsesc: bin/jsesc
  checksum: b8b44cbfc92f198ad972fba706ee6a1dfa7485321ee8c0b25f5cedd538dcb20cde3197de16a7265430fce8277a12db066219369e3d51055038946039f6e20e17
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: 866458a8c58a95a49bef3adba929c625e82532bcff1fe93f01d29cb02cac7c3fe1f4b79951b7792c2da9de0b32871a8401a6e3c5b36778ad852bf5b8a61165d7
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonfile@npm:^2.1.0":
  version: 2.4.0
  resolution: "jsonfile@npm:2.4.0"
  dependencies:
    graceful-fs: ^4.1.6
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: f5064aabbc9e35530dc471d8b203ae1f40dbe949ddde4391c6f6a6d310619a15f0efdae5587df594d1d70c555193aaeee9d2ed4aec9ffd5767bd5e4e62d49c3d
  languageName: node
  linkType: hard

"jsonp@npm:^0.2.1":
  version: 0.2.1
  resolution: "jsonp@npm:0.2.1"
  dependencies:
    debug: ^2.1.3
  checksum: 90aabd9deb3a9ba83aedf8d40e1aaff1fc29f3f3fe42985a661782498dde526a7cd9b7bec4b8721f7d0beafab9e5ccfdafd46640f7dd6c58d529a6bb5238e2b8
  languageName: node
  linkType: hard

"jss-plugin-camel-case@npm:^10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-camel-case@npm:10.10.0"
  dependencies:
    "@babel/runtime": ^7.3.1
    hyphenate-style-name: ^1.0.3
    jss: 10.10.0
  checksum: 693485b86f7a0e0bd0c16b8ddd057ca02a993fc088558c96501f9131e7e6261cc9f4b08047879a68441c688c40dceeb5219b1f15ade9043935aade4f37f5ca85
  languageName: node
  linkType: hard

"jss-plugin-default-unit@npm:^10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-default-unit@npm:10.10.0"
  dependencies:
    "@babel/runtime": ^7.3.1
    jss: 10.10.0
  checksum: 6e56213830753ad80bca3824973a667106defaef698d5996d45d03a0e2a3e035b33cd257aa8015040c41bd6669e7598dce72c36099d7ae69db758a7b2ca453fa
  languageName: node
  linkType: hard

"jss-plugin-global@npm:^10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-global@npm:10.10.0"
  dependencies:
    "@babel/runtime": ^7.3.1
    jss: 10.10.0
  checksum: f3af4f40358e96cf89e0c7c84b6e441dc9b4d543cd6109fdf9314a9818fd780d252035f46cc526c3d3fb4393bc29effc6993cc22e04f4e67ec3c889ab760d580
  languageName: node
  linkType: hard

"jss-plugin-nested@npm:^10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-nested@npm:10.10.0"
  dependencies:
    "@babel/runtime": ^7.3.1
    jss: 10.10.0
    tiny-warning: ^1.0.2
  checksum: 190094375972b68eb8f683387c74e97dc8347e7cc4f2fbfd40b3baf077dfde83d70e57be56744690d22537c0390e0a398714d86736df820c64e498df95f937de
  languageName: node
  linkType: hard

"jss-plugin-props-sort@npm:^10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-props-sort@npm:10.10.0"
  dependencies:
    "@babel/runtime": ^7.3.1
    jss: 10.10.0
  checksum: 274483444b6733bd58d229ebdcdb32b3c24172bc83cb2f6f8364926de19acd872758bcf06c7b3af11cf75504a67a7d67abba62b25081d144585a56b4df9512ba
  languageName: node
  linkType: hard

"jss-plugin-rule-value-function@npm:^10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-rule-value-function@npm:10.10.0"
  dependencies:
    "@babel/runtime": ^7.3.1
    jss: 10.10.0
    tiny-warning: ^1.0.2
  checksum: 009c9593b9be8b9f1030b797e58e3c233d90e034e5c68b0cabd25bffc7da965c69dc1ccb1bb6a542d72bb824df89036b2264fe564e8538320ef99febaf2882ee
  languageName: node
  linkType: hard

"jss-plugin-vendor-prefixer@npm:^10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-vendor-prefixer@npm:10.10.0"
  dependencies:
    "@babel/runtime": ^7.3.1
    css-vendor: ^2.0.8
    jss: 10.10.0
  checksum: 879b7233f9b0b571074dc2b88d97a05dbb949012ba2405f1481bbedd521167dc835133632adb3f2d8ffceddd337c8c13e3e8b1931590516c0664039598752dff
  languageName: node
  linkType: hard

"jss@npm:10.10.0, jss@npm:^10.10.0":
  version: 10.10.0
  resolution: "jss@npm:10.10.0"
  dependencies:
    "@babel/runtime": ^7.3.1
    csstype: ^3.0.2
    is-in-browser: ^1.1.3
    tiny-warning: ^1.0.2
  checksum: ecf71971df42729668c283e432e841349b7fdbe52e520f7704991cf4a738fd2451ec0feeb25c12cdc5addf7facecf838e74e62936fd461fb4c99f23d54a4792d
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0, jsx-ast-utils@npm:^3.3.3":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flat: ^1.3.1
    object.assign: ^4.1.4
    object.values: ^1.1.6
  checksum: f4b05fa4d7b5234230c905cfa88d36dc8a58a6666975a3891429b1a8cdc8a140bca76c297225cb7a499fad25a2c052ac93934449a2c31a44fc9edd06c773780a
  languageName: node
  linkType: hard

"karma-safaritechpreview-launcher@npm:0.0.6":
  version: 0.0.6
  resolution: "karma-safaritechpreview-launcher@npm:0.0.6"
  dependencies:
    fs-promise: ^0.5.0
    marcosc-async: ^3.0.4
  checksum: a8beba0a38d7e7e653a31747288703a609d5c624052276303f06fd82594f76149e7f754224a8dd74ee304862a553e9f5c3b717bc8c580deb93b7308ace443932
  languageName: node
  linkType: hard

"keycode@npm:2.2.0":
  version: 2.2.0
  resolution: "keycode@npm:2.2.0"
  checksum: cb91c2940a892f1444a41fc08339b8831445a6b095af9103e3061ea7d4bdbfc420135dcb5d9257020e35c374468bb7d4495ea9fcea54e5760196daff3c874fa4
  languageName: node
  linkType: hard

"keycode@npm:^2.2.0":
  version: 2.2.1
  resolution: "keycode@npm:2.2.1"
  checksum: 7a5c775b2410a3b6d9c07a415ecb70639187a965be239d2c11c71079cb1ca2f9dede94b86426f3ece475dc8ae3debf2367531bbcafc0af8c82f157d9a4f7e6cb
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.3
  resolution: "keyv@npm:4.5.3"
  dependencies:
    json-buffer: 3.0.1
  checksum: 3ffb4d5b72b6b4b4af443bbb75ca2526b23c750fccb5ac4c267c6116888b4b65681015c2833cb20d26cf3e6e32dac6b988c77f7f022e1a571b7d90f1442257da
  languageName: node
  linkType: hard

"klaw@npm:^1.0.0":
  version: 1.3.1
  resolution: "klaw@npm:1.3.1"
  dependencies:
    graceful-fs: ^4.1.9
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 8f69e4797c26e7c3f2426bfa85f38a3da3c2cb1b4c6bd850d2377aed440d41ce9d806f2885c2e2e224372c56af4b1d43b8a499adecf9a05e7373dc6b8b7c52e4
  languageName: node
  linkType: hard

"language-subtag-registry@npm:~0.3.2":
  version: 0.3.22
  resolution: "language-subtag-registry@npm:0.3.22"
  checksum: 8ab70a7e0e055fe977ac16ea4c261faec7205ac43db5e806f72e5b59606939a3b972c4bd1e10e323b35d6ffa97c3e1c4c99f6553069dad2dfdd22020fa3eb56a
  languageName: node
  linkType: hard

"language-tags@npm:=1.0.5":
  version: 1.0.5
  resolution: "language-tags@npm:1.0.5"
  dependencies:
    language-subtag-registry: ~0.3.2
  checksum: c81b5d8b9f5f9cfd06ee71ada6ddfe1cf83044dd5eeefcd1e420ad491944da8957688db4a0a9bc562df4afdc2783425cbbdfd152c01d93179cf86888903123cf
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"load-script@npm:^1.0.0":
  version: 1.0.0
  resolution: "load-script@npm:1.0.0"
  checksum: 8458e3f07b4a86f8d9d66e47a987811491a5d013af23ba7b371c6d3c9dc899885b072ccf65abf7874c10cb197d4975eacd8a7a125bfb38dbbcb267539f5dc1e9
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.memoize@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: 9ff3942feeccffa4f1fafa88d32f0d24fdc62fd15ded5a74a5f950ff5f0c6f61916157246744c620173dddf38d37095a92327d5fd3861e2063e736a5c207d089
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash.reduce@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.reduce@npm:4.6.0"
  checksum: 81f2a1045440554f8427f895ef479f1de5c141edd7852dde85a894879312801efae0295116e5cf830c531c1a51cdab8f3628c3ad39fa21a9874bb9158d9ea075
  languageName: node
  linkType: hard

"lodash.startswith@npm:^4.2.1":
  version: 4.2.1
  resolution: "lodash.startswith@npm:4.2.1"
  checksum: 1847371cbf6f32c4125a555847aff8a1ff1a977f40882b0ad4e2656a32e364793cfa7602915c9165cc0bba61fbd802e561888126d242faadcf3fc77215bab19b
  languageName: node
  linkType: hard

"lodash.throttle@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.throttle@npm:4.1.1"
  checksum: 129c0a28cee48b348aef146f638ef8a8b197944d4e9ec26c1890c19d9bf5a5690fe11b655c77a4551268819b32d27f4206343e30c78961f60b561b8608c8c805
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"lru-cache@npm:^7.7.1":
  version: 7.18.3
  resolution: "lru-cache@npm:7.18.3"
  checksum: e550d772384709deea3f141af34b6d4fa392e2e418c1498c078de0ee63670f1f46f5eee746e8ef7e69e1c895af0d4224e62ee33e66a543a14763b0f2e74c1356
  languageName: node
  linkType: hard

"lru-cache@npm:^9.1.1 || ^10.0.0":
  version: 10.0.1
  resolution: "lru-cache@npm:10.0.1"
  checksum: 06f8d0e1ceabd76bb6f644a26dbb0b4c471b79c7b514c13c6856113879b3bf369eb7b497dad4ff2b7e2636db202412394865b33c332100876d838ad1372f0181
  languageName: node
  linkType: hard

"m3u8-parser@npm:4.8.0":
  version: 4.8.0
  resolution: "m3u8-parser@npm:4.8.0"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/vhs-utils": ^3.0.5
    global: ^4.4.0
  checksum: 850a8c5cacdbdc154d4fa18c79af77fdf45ef4afcb3d8a0507d04e8d9ddb5c0a9d18cb48c8e23be707d007c063038cdeab0d3de23100e5c02050a7aff6e2b2b8
  languageName: node
  linkType: hard

"m3u8-parser@npm:^6.0.0":
  version: 6.2.0
  resolution: "m3u8-parser@npm:6.2.0"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/vhs-utils": ^3.0.5
    global: ^4.4.0
  checksum: 8aa9c726436cbdc3952eaabc34e30317e9f7fd02efc3bc3332b6c93b24ba556ceb0f29b2bdeb1aefe4ef3a5c55070f0f8e14e5f15507ff03750cb10337e926e8
  languageName: node
  linkType: hard

"m3u8-parser@npm:^7.1.0":
  version: 7.1.0
  resolution: "m3u8-parser@npm:7.1.0"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/vhs-utils": ^3.0.5
    global: ^4.4.0
  checksum: c7aa6faaf5ebbbbf4b6afe538da7c583bad8d17c1f80d17d9d17bb03df470d80d2c053620c439df38750b013d155eacfa83014f423f7df3a78acaf2efd2bdb76
  languageName: node
  linkType: hard

"make-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "make-dir@npm:2.1.0"
  dependencies:
    pify: ^4.0.1
    semver: ^5.6.0
  checksum: 043548886bfaf1820323c6a2997e6d2fa51ccc2586ac14e6f14634f7458b4db2daf15f8c310e2a0abd3e0cddc64df1890d8fc7263033602c47bb12cbfcf86aab
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^11.0.3":
  version: 11.1.1
  resolution: "make-fetch-happen@npm:11.1.1"
  dependencies:
    agentkeepalive: ^4.2.1
    cacache: ^17.0.0
    http-cache-semantics: ^4.1.1
    http-proxy-agent: ^5.0.0
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^7.7.1
    minipass: ^5.0.0
    minipass-fetch: ^3.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.3
    promise-retry: ^2.0.1
    socks-proxy-agent: ^7.0.0
    ssri: ^10.0.0
  checksum: 7268bf274a0f6dcf0343829489a4506603ff34bd0649c12058753900b0eb29191dce5dba12680719a5d0a983d3e57810f594a12f3c18494e93a1fbc6348a4540
  languageName: node
  linkType: hard

"marcosc-async@npm:^3.0.4":
  version: 3.0.5
  resolution: "marcosc-async@npm:3.0.5"
  checksum: da39281ca035ad75b2e83685bb87a7bd175331acc6eb42dccc7924ce94f940a9af74fb8a8b5a7e63e0af3bed727226a6153363118bf7d79ffdc1e38e63ea3d2c
  languageName: node
  linkType: hard

"markdown-to-jsx@npm:^7.3.2":
  version: 7.3.2
  resolution: "markdown-to-jsx@npm:7.3.2"
  peerDependencies:
    react: ">= 0.14.0"
  checksum: 8885c6343b71570b0a7ec16cd85a49b853a830234790ee7430e2517ea5d8d361ff138bd52147f650790f3e7b3a28a15c755fc16f8856dd01ddf09a6161782e06
  languageName: node
  linkType: hard

"match-sorter@npm:^6.0.2":
  version: 6.3.1
  resolution: "match-sorter@npm:6.3.1"
  dependencies:
    "@babel/runtime": ^7.12.5
    remove-accents: 0.4.2
  checksum: a4b02b676ac4ce64a89a091539ee4a70a802684713bcf06f2b70787927f510fe8a2adc849f9288857a90906083ad303467e530e8723b4a9756df9994fc164550
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 9d0128ed425a89f4cba8f787dca27ad9408b5cb1b220af2d938e2a0629d17d879a34d2cb19318bdb26c3f14c77dd5dfbae67211f5caaf07b61b1f2c5c8c7dc16
  languageName: node
  linkType: hard

"memoize-one@npm:^5.1.1":
  version: 5.2.1
  resolution: "memoize-one@npm:5.2.1"
  checksum: a3cba7b824ebcf24cdfcd234aa7f86f3ad6394b8d9be4c96ff756dafb8b51c7f71320785fbc2304f1af48a0467cbbd2a409efc9333025700ed523f254cb52e3d
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: ^3.0.2
    picomatch: ^2.3.1
  checksum: 02a17b671c06e8fefeeb6ef996119c1e597c942e632a21ef589154f23898c9c6a9858526246abb14f8bca6e77734aa9dcf65476fca47cedfb80d9577d52843fc
  languageName: node
  linkType: hard

"microseconds@npm:0.2.0":
  version: 0.2.0
  resolution: "microseconds@npm:0.2.0"
  checksum: 22bfa8553f92c7d95afff6de0aeb2aecf750680d41b8c72b02098ccc5bbbb0a384380ff539292dbd3788f5dfc298682f9d38a2b4c101f5ee2c9471d53934c5fa
  languageName: node
  linkType: hard

"min-document@npm:^2.19.0":
  version: 2.19.0
  resolution: "min-document@npm:2.19.0"
  dependencies:
    dom-walk: ^0.1.0
  checksum: da6437562ea2228041542a2384528e74e22d1daa1a4ec439c165abf0b9d8a63e17e3b8a6dc6e0c731845e85301198730426932a0e813d23f932ca668340c9623
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.1":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 253487976bf485b612f16bf57463520a14f512662e592e95c571afdab1442a6a6864b6c88f248ce6fc4ff0b6de04ac7aa6c8bb51e868e99d1d65eb0658a708b5
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6, minimist@npm:^1.2.8, minimist@npm:~1.2.5":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: ^3.0.0
  checksum: 14df761028f3e47293aee72888f2657695ec66bd7d09cae7ad558da30415fdc4752bbfee66287dcc6fd5e6a2fa3466d6c484dc1cbd986525d9393b9523d97f10
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.4
  resolution: "minipass-fetch@npm:3.0.4"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^2.1.2
  dependenciesMeta:
    encoding:
      optional: true
  checksum: af7aad15d5c128ab1ebe52e043bdf7d62c3c6f0cecb9285b40d7b395e1375b45dcdfd40e63e93d26a0e8249c9efd5c325c65575aceee192883970ff8cb11364a
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 425dab288738853fded43da3314a0b5c035844d6f3097a8e3b5b29b328da8f3c1af6fc70618b32c29ff906284cf6406b6841376f21caaadd0793c1d5a6a620ea
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.3":
  version: 7.0.3
  resolution: "minipass@npm:7.0.3"
  checksum: 6f1614f5b5b55568a46bca5fec0e7c46dac027691db27d0e1923a8192866903144cd962ac772c0e9f89b608ea818b702709c042bce98e190d258847d85461531
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"moment-timezone@npm:^0.5.34":
  version: 0.5.43
  resolution: "moment-timezone@npm:0.5.43"
  dependencies:
    moment: ^2.29.4
  checksum: 8075c897ed8a044f992ef26fe8cdbcad80caf974251db424cae157473cca03be2830de8c74d99341b76edae59f148c9d9d19c1c1d9363259085688ec1cf508d0
  languageName: node
  linkType: hard

"moment@npm:^2.29.1, moment@npm:^2.29.4":
  version: 2.29.4
  resolution: "moment@npm:2.29.4"
  checksum: 0ec3f9c2bcba38dc2451b1daed5daded747f17610b92427bebe1d08d48d8b7bdd8d9197500b072d14e326dd0ccf3e326b9e3d07c5895d3d49e39b6803b76e80e
  languageName: node
  linkType: hard

"mpd-parser@npm:0.22.1, mpd-parser@npm:^0.22.1":
  version: 0.22.1
  resolution: "mpd-parser@npm:0.22.1"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/vhs-utils": ^3.0.5
    "@xmldom/xmldom": ^0.8.3
    global: ^4.4.0
  bin:
    mpd-to-m3u8-json: bin/parse.js
  checksum: 688e4d6c2353e3e55a9a7841fff263825d92ec44e0d0800e4c9d332fa27f9ffffac201723d9f5793a49813060fd150e9f203f20e840e64f7da2626b49764ba01
  languageName: node
  linkType: hard

"mpd-parser@npm:^1.0.1, mpd-parser@npm:^1.2.2":
  version: 1.2.2
  resolution: "mpd-parser@npm:1.2.2"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/vhs-utils": ^3.0.5
    "@xmldom/xmldom": ^0.8.3
    global: ^4.4.0
  bin:
    mpd-to-m3u8-json: bin/parse.js
  checksum: 29af36af2a6c9552315091e9170625f2dc1a8e5d49bce410bf788439e69c9a3ba0e9561865ff551eccf12959553913d05b181f18cabd2e8828777d0f6a526b31
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:^2.0.0, ms@npm:^2.1.1":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"mui-file-input@npm:^6.0.0":
  version: 6.0.0
  resolution: "mui-file-input@npm:6.0.0"
  dependencies:
    pretty-bytes: ^6.1.1
  peerDependencies:
    "@emotion/react": ^11.13.3
    "@emotion/styled": ^11.13.0
    "@mui/material": ^5.0.0 || ^6.0.0
    "@types/react": ^18.0.0
    react: ^18.0.0
    react-dom: ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: eb20b0c888f0d7e2f4a60db054d248b0bf095b8a94025b3fce90ff087f164cad507a29f7640e8527269d8442eff2cc6999bdf3183bc851e52e90d34656d31af2
  languageName: node
  linkType: hard

"multipipe@npm:^1.0.2":
  version: 1.0.2
  resolution: "multipipe@npm:1.0.2"
  dependencies:
    duplexer2: ^0.1.2
    object-assign: ^4.1.0
  checksum: 99cf8934714da7f9ce03e1f0a99621a41443217d80849c62e22b31b6ac356b9acd22f41e555fd7a759f1c7c9d3273e7abff2fb82dff8285f00a6a1022727e4ab
  languageName: node
  linkType: hard

"mux.js@npm:6.0.1":
  version: 6.0.1
  resolution: "mux.js@npm:6.0.1"
  dependencies:
    "@babel/runtime": ^7.11.2
    global: ^4.4.0
  bin:
    muxjs-transmux: bin/transmux.js
  checksum: dcf6fb7a3ea5bce741268480fe5ed43ae75c82403ebcaddb2f6d1bd83364e3c70359feea9348112cb7ddd88b02e9c2f9fa0c0a5de9eaf32e8f246a53c6ed8f8a
  languageName: node
  linkType: hard

"mux.js@npm:7.0.0":
  version: 7.0.0
  resolution: "mux.js@npm:7.0.0"
  dependencies:
    "@babel/runtime": ^7.11.2
    global: ^4.4.0
  bin:
    muxjs-transmux: bin/transmux.js
  checksum: 37a8bb469222d26937b45540601afd43c6556d13df98621e34c378c27e99528af019f664f7b61fbf1a5861ed6f96c207db7f6414d0df126c6f512a1e7ed0bfbb
  languageName: node
  linkType: hard

"mux.js@npm:^6.2.0":
  version: 6.3.0
  resolution: "mux.js@npm:6.3.0"
  dependencies:
    "@babel/runtime": ^7.11.2
    global: ^4.4.0
  bin:
    muxjs-transmux: bin/transmux.js
  checksum: dc914bc12ee7d1e8143dc124697db87e41b57c6735a5a0024d5592e9ea424e25f5c19fc6fd7f66333621bb2c5e86120de862049eb62f2c8ac5a17dd5dc54b194
  languageName: node
  linkType: hard

"mz@npm:^2.3.1":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: ^1.0.0
    object-assign: ^4.0.1
    thenify-all: ^1.0.0
  checksum: 8427de0ece99a07e9faed3c0c6778820d7543e3776f9a84d22cf0ec0a8eb65f6e9aee9c9d353ff9a105ff62d33a9463c6ca638974cc652ee8140cd1e35951c87
  languageName: node
  linkType: hard

"nano-css@npm:^5.3.1":
  version: 5.3.5
  resolution: "nano-css@npm:5.3.5"
  dependencies:
    css-tree: ^1.1.2
    csstype: ^3.0.6
    fastest-stable-stringify: ^2.0.2
    inline-style-prefixer: ^6.0.0
    rtl-css-js: ^1.14.0
    sourcemap-codec: ^1.4.8
    stacktrace-js: ^2.0.2
    stylis: ^4.0.6
  peerDependencies:
    react: "*"
    react-dom: "*"
  checksum: 8d4e59a2a29477221af47320d850a7dcee1ac51774fb5a0dce6ee59b22174c7149f75108235de85559581fbb2b93aa222a2b32ea53c93ba3f5d322c4d098c355
  languageName: node
  linkType: hard

"nano-time@npm:1.0.0":
  version: 1.0.0
  resolution: "nano-time@npm:1.0.0"
  dependencies:
    big-integer: ^1.6.16
  checksum: eef8548546cc1020625f8e44751a7263e9eddf0412a6a1a6c80a8d2be2ea7973622804a977cdfe796807b85b20ff6c8ba340e8dd20effcc7078193ed5edbb5d4
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.4, nanoid@npm:^3.3.6":
  version: 3.3.6
  resolution: "nanoid@npm:3.3.6"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 7d0eda657002738aa5206107bd0580aead6c95c460ef1bdd0b1a87a9c7ae6277ac2e9b945306aaa5b32c6dcb7feaf462d0f552e7f8b5718abfc6ead5c94a71b3
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"next-i18next@npm:13.3.0":
  version: 13.3.0
  resolution: "next-i18next@npm:13.3.0"
  dependencies:
    "@babel/runtime": ^7.20.13
    "@types/hoist-non-react-statics": ^3.3.1
    core-js: ^3
    hoist-non-react-statics: ^3.3.2
    i18next-fs-backend: ^2.1.1
  peerDependencies:
    i18next: ^22.0.6
    next: ">= 12.0.0"
    react: ">= 17.0.2"
    react-i18next: ^12.2.0
  checksum: 4c3aa5bbbc12964ed6f61f37d33f4319abd04932d6871baba9f209183952b286470fff80f90a94ccddba736c8fb13a9238effa1e8247dfb08e2239cbdc4fb769
  languageName: node
  linkType: hard

"next-sitemap@npm:^4.1.8":
  version: 4.2.3
  resolution: "next-sitemap@npm:4.2.3"
  dependencies:
    "@corex/deepmerge": ^4.0.43
    "@next/env": ^13.4.3
    fast-glob: ^3.2.12
    minimist: ^1.2.8
  peerDependencies:
    next: "*"
  bin:
    next-sitemap: bin/next-sitemap.mjs
    next-sitemap-cjs: bin/next-sitemap.cjs
  checksum: 322cbbf11b6549b7bd5acda98f60a4ef6bb6627e6cd5b4a9367aa8950edf6bcc90f47f4da74a18ecdd823f52c0f625b559b4165dbd27f292c731b222e38fa105
  languageName: node
  linkType: hard

"next@npm:13.4.12":
  version: 13.4.12
  resolution: "next@npm:13.4.12"
  dependencies:
    "@next/env": 13.4.12
    "@next/swc-darwin-arm64": 13.4.12
    "@next/swc-darwin-x64": 13.4.12
    "@next/swc-linux-arm64-gnu": 13.4.12
    "@next/swc-linux-arm64-musl": 13.4.12
    "@next/swc-linux-x64-gnu": 13.4.12
    "@next/swc-linux-x64-musl": 13.4.12
    "@next/swc-win32-arm64-msvc": 13.4.12
    "@next/swc-win32-ia32-msvc": 13.4.12
    "@next/swc-win32-x64-msvc": 13.4.12
    "@swc/helpers": 0.5.1
    busboy: 1.6.0
    caniuse-lite: ^1.0.30001406
    postcss: 8.4.14
    styled-jsx: 5.1.1
    watchpack: 2.4.0
    zod: 3.21.4
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
    fibers: ">= 3.1.0"
    react: ^18.2.0
    react-dom: ^18.2.0
    sass: ^1.3.0
  dependenciesMeta:
    "@next/swc-darwin-arm64":
      optional: true
    "@next/swc-darwin-x64":
      optional: true
    "@next/swc-linux-arm64-gnu":
      optional: true
    "@next/swc-linux-arm64-musl":
      optional: true
    "@next/swc-linux-x64-gnu":
      optional: true
    "@next/swc-linux-x64-musl":
      optional: true
    "@next/swc-win32-arm64-msvc":
      optional: true
    "@next/swc-win32-ia32-msvc":
      optional: true
    "@next/swc-win32-x64-msvc":
      optional: true
  peerDependenciesMeta:
    "@opentelemetry/api":
      optional: true
    fibers:
      optional: true
    sass:
      optional: true
  bin:
    next: dist/bin/next
  checksum: 50bd443ffe09424c1a94d6606d41886ccd1d65185e125aa199957ea92c5e4d1c226f1675f3e5ea92e88f43f8355824ba50db52a8aeae225f7a86b6c901d25527
  languageName: node
  linkType: hard

"nextjs-enduserweb@workspace:.":
  version: 0.0.0-use.local
  resolution: "nextjs-enduserweb@workspace:."
  dependencies:
    "@easylive-show/react-phone-input": ^2.14.10
    "@easylive-show/video-player": ^1.3.11
    "@easylive-show/videojs-hls-quality-selector": 1.1.5
    "@easylive-show/videojs-plugin-contextmenu": ^1.0.3
    "@emotion/cache": ^11.4.0
    "@emotion/react": ^11.11.1
    "@emotion/server": ^11.4.0
    "@emotion/styled": ^11.11.0
    "@fingerprintjs/fingerprintjs": ^3.3.0
    "@mui/base": ^5.0.0-beta.18
    "@mui/icons-material": 5.x
    "@mui/lab": ^6.0.0-beta.15
    "@mui/material": 5.x
    "@mui/material-nextjs": ^6.1.6
    "@mui/styles": 5.x
    "@mui/system": 5.x
    "@mui/x-date-pickers": ^6.9.2
    "@next/swc-linux-arm64-musl": 13.4.12
    "@next/swc-linux-x64-musl": 13.4.12
    "@reduxjs/toolkit": ^1.9.2
    "@stoneleigh/api-lib": 6.1.1
    "@stoneleigh/appconfig-lib": ^1.2.0
    "@stoneleigh/eslint-plugin": ^1.0.16
    "@stoneleigh/navigation-menu": ^1.4.4
    "@stoneleigh/scroll-header": ^0.0.24
    "@types/babel-plugin-macros": ^3
    "@types/cookie": ^0
    "@types/eslint": ^8
    "@types/fg-loadcss": ^3.1.1
    "@types/lodash": ^4
    "@types/lodash.throttle": ^4
    "@types/node": ^18.6.2
    "@types/react": 18.x
    "@types/react-dom": 18.x
    "@types/react-image-gallery": ^1
    "@types/react-linkify": ^1.0.1
    "@types/react-redux": ^7.1.20
    "@types/react-router-dom": ^5.3.2
    "@types/redux-logger": ^3.0.9
    "@types/uuid": ^9
    "@typescript-eslint/eslint-plugin": ^6.7.3
    "@typescript-eslint/parser": ^6.7.3
    axios: ^0.24.0
    babel-plugin-macros: ^3.1.0
    base-64: ^1.0.0
    classnames: ^2.3.1
    cookie: ^0.5.0
    cookies-next: ^2.1.2
    countries-list: ^3.1.1
    cross-env: ^7.0.3
    date-fns: ^2.30.0
    dayjs: ^1.11.9
    dayjs-plugin-utc: ^0.1.2
    dotenv: ^10.0.0
    eslint: ^8.35.0
    eslint-config-next: ^13.2.4
    eslint-plugin-eslint-plugin: ^5.1.1
    eslint-plugin-import: ^2.29.1
    eslint-plugin-node: ^11.1.0
    fg-loadcss: ^3.1.0
    framer-motion: ^10.16.4
    i18next: 23.2.11
    js-sha256: ^0.9.0
    lodash: ^4.17.21
    lodash.throttle: ^4.1.1
    markdown-to-jsx: ^7.3.2
    moment: ^2.29.1
    moment-timezone: ^0.5.34
    mui-file-input: ^6.0.0
    next: 13.4.12
    next-i18next: 13.3.0
    next-sitemap: ^4.1.8
    notistack: ^3.0.1
    qrcode.react: ^1.0.1
    react: 18.x
    react-day-picker: ^9.1.3
    react-dom: 18.x
    react-i18next: 13.0.2
    react-image-gallery: ^1.3.0
    react-multi-carousel: ^2.8.4
    react-player: ^2.13.0
    react-qr-code: ^2.0.15
    react-redux: ^8.0.5
    react-share: ^4.4.0
    react-sticky-el: ^2.1.0
    react-use: ^17.4.0
    redux: ^4.2.1
    redux-cookie: ^0.5.9
    redux-logger: ^3.0.6
    redux-persist: ^6.0.0
    rfs: 10.x
    rxjs: ^7.8.1
    sass: ^1.43.2
    styled-components: ^6.0.4
    typescript: 5.x
    universal-cookie: ^4.0.4
    usehooks-ts: ^2.9.1
    uuid: ^9.0.1
  peerDependencies:
    cross-env: ^7.0.3
  languageName: unknown
  linkType: soft

"node-gyp@npm:latest":
  version: 9.4.0
  resolution: "node-gyp@npm:9.4.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^7.1.4
    graceful-fs: ^4.2.6
    make-fetch-happen: ^11.0.3
    nopt: ^6.0.0
    npmlog: ^6.0.0
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.2
    which: ^2.0.2
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 78b404e2e0639d64e145845f7f5a3cb20c0520cdaf6dda2f6e025e9b644077202ea7de1232396ba5bde3fee84cdc79604feebe6ba3ec84d464c85d407bb5da99
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.13":
  version: 2.0.13
  resolution: "node-releases@npm:2.0.13"
  checksum: 17ec8f315dba62710cae71a8dad3cd0288ba943d2ece43504b3b1aa8625bf138637798ab470b1d9035b0545996f63000a8a926e0f6d35d0996424f8b6d36dda3
  languageName: node
  linkType: hard

"nopt@npm:^6.0.0":
  version: 6.0.0
  resolution: "nopt@npm:6.0.0"
  dependencies:
    abbrev: ^1.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 82149371f8be0c4b9ec2f863cc6509a7fd0fa729929c009f3a58e4eb0c9e4cae9920e8f1f8eb46e7d032fec8fb01bede7f0f41a67eb3553b7b8e14fa53de1dac
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"notistack@npm:^3.0.1":
  version: 3.0.1
  resolution: "notistack@npm:3.0.1"
  dependencies:
    clsx: ^1.1.0
    goober: ^2.0.33
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: 421c970308690b8c8cb2e275e7f66020db7c1955e104f638e7fa562396a6b9322ff95f0e62492b07f3d36b0ef72adb4de2c2ce9803089c1c8f028d1a3b088e01
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.2
  resolution: "npmlog@npm:6.0.2"
  dependencies:
    are-we-there-yet: ^3.0.0
    console-control-strings: ^1.1.0
    gauge: ^4.0.3
    set-blocking: ^2.0.0
  checksum: ae238cd264a1c3f22091cdd9e2b106f684297d3c184f1146984ecbe18aaa86343953f26b9520dedd1b1372bc0316905b736c1932d778dbeb1fcf5a1001390e2a
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.12.3, object-inspect@npm:^1.9.0":
  version: 1.12.3
  resolution: "object-inspect@npm:1.12.3"
  checksum: dabfd824d97a5f407e6d5d24810d888859f6be394d8b733a77442b277e0808860555176719c5905e765e3743a7cada6b8b0a3b85e5331c530fd418cc8ae991db
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.3
  resolution: "object-inspect@npm:1.13.3"
  checksum: 8c962102117241e18ea403b84d2521f78291b774b03a29ee80a9863621d88265ffd11d0d7e435c4c2cea0dc2a2fbf8bbc92255737a05536590f2df2e8756f297
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object-keys@npm:~0.4.0":
  version: 0.4.0
  resolution: "object-keys@npm:0.4.0"
  checksum: 1be3ebe9b48c0d5eda8e4a30657d887a748cb42435e0e2eaf49faf557bdd602cd2b7558b8ce90a4eb2b8592d16b875a1900bce859cbb0f35b21c67e11a45313c
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4":
  version: 4.1.4
  resolution: "object.assign@npm:4.1.4"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    has-symbols: ^1.0.3
    object-keys: ^1.1.1
  checksum: 76cab513a5999acbfe0ff355f15a6a125e71805fcf53de4e9d4e082e1989bdb81d1e329291e1e4e0ae7719f0e4ef80e88fb2d367ae60500d79d25a6224ac8864
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.5":
  version: 4.1.5
  resolution: "object.assign@npm:4.1.5"
  dependencies:
    call-bind: ^1.0.5
    define-properties: ^1.2.1
    has-symbols: ^1.0.3
    object-keys: ^1.1.1
  checksum: f9aeac0541661370a1fc86e6a8065eb1668d3e771f7dbb33ee54578201336c057b21ee61207a186dd42db0c62201d91aac703d20d12a79fc79c353eed44d4e25
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.6":
  version: 1.1.7
  resolution: "object.entries@npm:1.1.7"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
  checksum: da287d434e7e32989586cd734382364ba826a2527f2bc82e6acbf9f9bfafa35d51018b66ec02543ffdfa2a5ba4af2b6f1ca6e588c65030cb4fd9c67d6ced594c
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.6":
  version: 2.0.7
  resolution: "object.fromentries@npm:2.0.7"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
  checksum: 7341ce246e248b39a431b87a9ddd331ff52a454deb79afebc95609f94b1f8238966cf21f52188f2a353f0fdf83294f32f1ebf1f7826aae915ebad21fd0678065
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
  checksum: 29b2207a2db2782d7ced83f93b3ff5d425f901945f3665ffda1821e30a7253cd1fd6b891a64279976098137ddfa883d748787a6fea53ecdb51f8df8b8cec0ae1
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.0":
  version: 1.0.1
  resolution: "object.groupby@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    get-intrinsic: ^1.2.1
  checksum: d7959d6eaaba358b1608066fc67ac97f23ce6f573dc8fc661f68c52be165266fcb02937076aedb0e42722fdda0bdc0bbf74778196ac04868178888e9fd3b78b5
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
  checksum: 0d30693ca3ace29720bffd20b3130451dca7a56c612e1926c0a1a15e4306061d84410bdb1456be2656c5aca53c81b7a3661eceaa362db1bba6669c2c9b6d1982
  languageName: node
  linkType: hard

"object.hasown@npm:^1.1.2":
  version: 1.1.3
  resolution: "object.hasown@npm:1.1.3"
  dependencies:
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
  checksum: 76bc17356f6124542fb47e5d0e78d531eafa4bba3fc2d6fc4b1a8ce8b6878912366c0d99f37ce5c84ada8fd79df7aa6ea1214fddf721f43e093ad2df51f27da1
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6":
  version: 1.1.7
  resolution: "object.values@npm:1.1.7"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
  checksum: f3e4ae4f21eb1cc7cebb6ce036d4c67b36e1c750428d7b7623c56a0db90edced63d08af8a316d81dfb7c41a3a5fa81b05b7cc9426e98d7da986b1682460f0777
  languageName: node
  linkType: hard

"object.values@npm:^1.2.0":
  version: 1.2.0
  resolution: "object.values@npm:1.2.0"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: 51fef456c2a544275cb1766897f34ded968b22adfc13ba13b5e4815fdaf4304a90d42a3aee114b1f1ede048a4890381d47a5594d84296f2767c6a0364b9da8fa
  languageName: node
  linkType: hard

"oblivious-set@npm:1.0.0":
  version: 1.0.0
  resolution: "oblivious-set@npm:1.0.0"
  checksum: f31740ea9c3a8242ad2324e4ebb9a35359fbc2e6e7131731a0fc1c8b7b1238eb07e4c8c631a38535243a7b8e3042b7e89f7dc2a95d2989afd6f80bd5793b0aab
  languageName: node
  linkType: hard

"once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.3
  resolution: "optionator@npm:0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap": ^1.2.3
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
  checksum: 09281999441f2fe9c33a5eeab76700795365a061563d66b098923eb719251a42bdbe432790d35064d0816ead9296dbeb1ad51a733edf4167c96bd5d0882e428a
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.10.1":
  version: 1.10.1
  resolution: "path-scurry@npm:1.10.1"
  dependencies:
    lru-cache: ^9.1.1 || ^10.0.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: e2557cff3a8fb8bc07afdd6ab163a92587884f9969b05bbbaf6fe7379348bfb09af9ed292af12ed32398b15fb443e81692047b786d1eeb6d898a51eb17ed7d90
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 9c4e34278cb09987685fa5ef81499c82546c033713518f6441778fbec623fc708777fe8ac633097c72d88470d5963094076c7305cafc7ad340aae27cfacd856b
  languageName: node
  linkType: hard

"pkcs7@npm:^1.0.4":
  version: 1.0.4
  resolution: "pkcs7@npm:1.0.4"
  dependencies:
    "@babel/runtime": ^7.5.5
  bin:
    pkcs7: bin/cli.js
  checksum: 926a713263c7656883d1fcc9fa709e08c26babc76d535dd76ccdf5ad1bc1c5931ecbbe2d7ae6075ad54897e1184be4ec304d295e28953c2ec0f1a04c27f4e838
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.0.0
  resolution: "possible-typed-array-names@npm:1.0.0"
  checksum: b32d403ece71e042385cc7856385cecf1cd8e144fa74d2f1de40d1e16035dba097bc189715925e79b67bdd1472796ff168d3a90d296356c9c94d272d5b95f3ae
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.2, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:8.4.14":
  version: 8.4.14
  resolution: "postcss@npm:8.4.14"
  dependencies:
    nanoid: ^3.3.4
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: fe58766ff32e4becf65a7d57678995cfd239df6deed2fe0557f038b47c94e4132e7e5f68b5aa820c13adfec32e523b693efaeb65798efb995ce49ccd83953816
  languageName: node
  linkType: hard

"postcss@npm:^8.4.23":
  version: 8.4.30
  resolution: "postcss@npm:8.4.30"
  dependencies:
    nanoid: ^3.3.6
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: 6c810c10c9bd3e03ca016e0b6b6756261e640aba1a9a7b1200b55502bc34b9165e38f590aef3493afc2f30ab55cdfcd43fd0f8408d69a77318ddbcf2a8ad164b
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"pretty-bytes@npm:^6.1.1":
  version: 6.1.1
  resolution: "pretty-bytes@npm:6.1.1"
  checksum: 43d29d909d2d88072da2c3d72f8fd0f2d2523c516bfa640aff6e31f596ea1004b6601f4cabc50d14b2cf10e82635ebe5b7d9378f3d5bae1c0067131829421b8a
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: bfcce49814f7d172a6e6a14d5fa3ac92cc3d0c3b9feb1279774708a719e19acd673995226351a082a9ae99978254e320ccda4240ddc474ba31a76c79491ca7c3
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prop-types@npm:^15.6.0, prop-types@npm:^15.6.1, prop-types@npm:^15.6.2, prop-types@npm:^15.7.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.0
  resolution: "punycode@npm:2.3.0"
  checksum: 39f760e09a2a3bbfe8f5287cf733ecdad69d6af2fe6f97ca95f24b8921858b91e9ea3c9eeec6e08cede96181b3bb33f95c6ffd8c77e63986508aa2e8159fa200
  languageName: node
  linkType: hard

"qr.js@npm:0.0.0":
  version: 0.0.0
  resolution: "qr.js@npm:0.0.0"
  checksum: 5ac6c393967bdeaa660e7fd3a501a25eb538c1f6008a4d30ab2b97bbe520e5c236530090773f1578aa0a523cdaa6923c866615e21143f9e7cd22abd41c789b69
  languageName: node
  linkType: hard

"qrcode.react@npm:^1.0.1":
  version: 1.0.1
  resolution: "qrcode.react@npm:1.0.1"
  dependencies:
    loose-envify: ^1.4.0
    prop-types: ^15.6.0
    qr.js: 0.0.0
  peerDependencies:
    react: ^15.5.3 || ^16.0.0 || ^17.0.0
  checksum: 154a9557d103fa776cd99bc0d2600b862be74872ff965052533bc38ea0606c4d0965b4dd6df93e53f1db86699517a25db2718bbbc08c5d535a90c0d335552e9a
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"react-animate-height@npm:^2.1.2":
  version: 2.1.2
  resolution: "react-animate-height@npm:2.1.2"
  dependencies:
    classnames: ^2.2.5
    prop-types: ^15.6.1
  peerDependencies:
    react: ">=15.6.2"
    react-dom: ">=15.6.2"
  checksum: 56f0db8a8a53a8e115cc9d38ce290aad4bb989e83b573a1b5a01d4b33bddf28810ae6b60cb95cd870fb11bf83eae3ef33ffd4bfd58c578e8c2a28b410e3e9885
  languageName: node
  linkType: hard

"react-day-picker@npm:^9.1.3":
  version: 9.3.0
  resolution: "react-day-picker@npm:9.3.0"
  dependencies:
    "@date-fns/tz": ^1.1.2
    date-fns: ^4.1.0
  peerDependencies:
    react: ">=16.8.0"
  checksum: e60ece1a7ec0dcefcba781d006d3eb9bb601bd1a14e2348cbacd722056c65e805e2672d4663efcee0dcb5e026d528edfa7586c8f55577f2782a99ba27c1e2d84
  languageName: node
  linkType: hard

"react-dom@npm:18.x":
  version: 18.2.0
  resolution: "react-dom@npm:18.2.0"
  dependencies:
    loose-envify: ^1.1.0
    scheduler: ^0.23.0
  peerDependencies:
    react: ^18.2.0
  checksum: 7d323310bea3a91be2965f9468d552f201b1c27891e45ddc2d6b8f717680c95a75ae0bc1e3f5cf41472446a2589a75aed4483aee8169287909fcd59ad149e8cc
  languageName: node
  linkType: hard

"react-fast-compare@npm:^3.0.1":
  version: 3.2.2
  resolution: "react-fast-compare@npm:3.2.2"
  checksum: 2071415b4f76a3e6b55c84611c4d24dcb12ffc85811a2840b5a3f1ff2d1a99be1020d9437ee7c6e024c9f4cbb84ceb35e48cf84f28fcb00265ad2dfdd3947704
  languageName: node
  linkType: hard

"react-i18next@npm:13.0.2":
  version: 13.0.2
  resolution: "react-i18next@npm:13.0.2"
  dependencies:
    "@babel/runtime": ^7.22.5
    html-parse-stringify: ^3.0.1
  peerDependencies:
    i18next: ">= 23.2.3"
    react: ">= 16.8.0"
  peerDependenciesMeta:
    react-dom:
      optional: true
    react-native:
      optional: true
  checksum: ab8226939f3681efb5a8628f938d90b3311336e69ca9aaf1ea54aad6fac3395516736971a3484234455f3831ecae8555f68c32fa1cd4d38b94ff69769da2b677
  languageName: node
  linkType: hard

"react-icons@npm:^4.3.1":
  version: 4.11.0
  resolution: "react-icons@npm:4.11.0"
  peerDependencies:
    react: "*"
  checksum: 7b8b80bbe2dabcc54b6c2129b7761a04b19caebe24389adc7683478ef41212b9ca0b53c63abcc06b3c01b94c84855ec5142b4c357e19c4aaaad9a4db23a3c485
  languageName: node
  linkType: hard

"react-image-gallery@npm:^1.3.0":
  version: 1.3.0
  resolution: "react-image-gallery@npm:1.3.0"
  peerDependencies:
    react: ^16.0.0 || ^17.0.0 || ^18.0.0
  checksum: 59043609bbcee62bd7c9c72feb59df63abfc246e227c0fae3032d72544c3916a2ff64eae777c7b3400a04221d9791a13ac4b984bd5c25c7ff11ccc7157d92d44
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0, react-is@npm:^18.2.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: e72d0ba81b5922759e4aff17e0252bd29988f9642ed817f56b25a3e217e13eea8a7f2322af99a06edb779da12d5d636e9fda473d620df9a3da0df2a74141d53e
  languageName: node
  linkType: hard

"react-is@npm:^18.3.1":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: e20fe84c86ff172fc8d898251b7cc2c43645d108bf96d0b8edf39b98f9a2cae97b40520ee7ed8ee0085ccc94736c4886294456033304151c3f94978cec03df21
  languageName: node
  linkType: hard

"react-multi-carousel@npm:^2.8.4":
  version: 2.8.4
  resolution: "react-multi-carousel@npm:2.8.4"
  checksum: ed4d95630f2a6c144f630874cc445e94678a9cfa4f1a2ebffe15b4f8317d3210f5141b64646f5b64689c18d11ebaeed008190ae864d7ee55193f12a9f1857542
  languageName: node
  linkType: hard

"react-player@npm:^2.13.0":
  version: 2.13.0
  resolution: "react-player@npm:2.13.0"
  dependencies:
    deepmerge: ^4.0.0
    load-script: ^1.0.0
    memoize-one: ^5.1.1
    prop-types: ^15.7.2
    react-fast-compare: ^3.0.1
  peerDependencies:
    react: ">=16.6.0"
  checksum: 7e0e69e0ac37227ab5bfdda73991d4f5d4741585562f3ad9cfb787ae2c427510b69ddf6ef3f23f319d699b790af852fca57f3e9b1dae94f385d545a3db200d67
  languageName: node
  linkType: hard

"react-qr-code@npm:^2.0.15":
  version: 2.0.15
  resolution: "react-qr-code@npm:2.0.15"
  dependencies:
    prop-types: ^15.8.1
    qr.js: 0.0.0
  peerDependencies:
    react: "*"
  checksum: 4124b7c2ce71bfe94279d85322175c881ba87dcc9b67f3c6d6e2806854a8792cf5f9ed8e10ea04c22e39aa060ef12fb39e679e780cba14f168d3cd6cffb7086d
  languageName: node
  linkType: hard

"react-query@npm:^3.39.3":
  version: 3.39.3
  resolution: "react-query@npm:3.39.3"
  dependencies:
    "@babel/runtime": ^7.5.5
    broadcast-channel: ^3.4.1
    match-sorter: ^6.0.2
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    react-dom:
      optional: true
    react-native:
      optional: true
  checksum: d2de6a0992dbf039ff2de564de1ae6361f8ac7310159dae42ec16f833b79c05caedced187235c42373ac331cc5f2fe9e2b31b14ae75a815e86d86e30ca9887ad
  languageName: node
  linkType: hard

"react-redux@npm:^8.0.5":
  version: 8.1.2
  resolution: "react-redux@npm:8.1.2"
  dependencies:
    "@babel/runtime": ^7.12.1
    "@types/hoist-non-react-statics": ^3.3.1
    "@types/use-sync-external-store": ^0.0.3
    hoist-non-react-statics: ^3.3.2
    react-is: ^18.0.0
    use-sync-external-store: ^1.0.0
  peerDependencies:
    "@types/react": ^16.8 || ^17.0 || ^18.0
    "@types/react-dom": ^16.8 || ^17.0 || ^18.0
    react: ^16.8 || ^17.0 || ^18.0
    react-dom: ^16.8 || ^17.0 || ^18.0
    react-native: ">=0.59"
    redux: ^4 || ^5.0.0-beta.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
    react-dom:
      optional: true
    react-native:
      optional: true
    redux:
      optional: true
  checksum: 4d5976b0f721e4148475871fcabce2fee875cc7f70f9a292f3370d63b38aa1dd474eb303c073c5555f3e69fc732f3bac05303def60304775deb28361e3f4b7cc
  languageName: node
  linkType: hard

"react-share@npm:^4.4.0":
  version: 4.4.1
  resolution: "react-share@npm:4.4.1"
  dependencies:
    classnames: ^2.3.2
    jsonp: ^0.2.1
  peerDependencies:
    react: ^16.3.0 || ^17 || ^18
  checksum: f86bf7415f9fe98243b1ea58bf9df53503de7fb2235b1e633dd25c8cef68e59070b3cd20fab6ec5286eccbd30d27ce6c458ac532c4fdf7b6f12f4048bbea24d3
  languageName: node
  linkType: hard

"react-sticky-el@npm:^2.1.0":
  version: 2.1.0
  resolution: "react-sticky-el@npm:2.1.0"
  peerDependencies:
    react: ">=16.3.0"
    react-dom: ">=16.3.0"
  checksum: 53f686a15fb74c8589a2b86f76098332b684adcebc2a51a0ef3a64303a045f856aa0ac22647905be2ded85a476c9a5c1d3da237a468a5b3022c60627487ccf50
  languageName: node
  linkType: hard

"react-transition-group@npm:^4.4.5":
  version: 4.4.5
  resolution: "react-transition-group@npm:4.4.5"
  dependencies:
    "@babel/runtime": ^7.5.5
    dom-helpers: ^5.0.1
    loose-envify: ^1.4.0
    prop-types: ^15.6.2
  peerDependencies:
    react: ">=16.6.0"
    react-dom: ">=16.6.0"
  checksum: 75602840106aa9c6545149d6d7ae1502fb7b7abadcce70a6954c4b64a438ff1cd16fc77a0a1e5197cdd72da398f39eb929ea06f9005c45b132ed34e056ebdeb1
  languageName: node
  linkType: hard

"react-universal-interface@npm:^0.6.2":
  version: 0.6.2
  resolution: "react-universal-interface@npm:0.6.2"
  peerDependencies:
    react: "*"
    tslib: "*"
  checksum: 070a7e9e3cdd8b0ec91a2ac9ac0a8df6bcb3fd183d2775bf0f439b9870fc1faf5b4fa9fe9741abd5187f0a35be645cb4004e1c9ebda9ada7e5d0a624f94910cb
  languageName: node
  linkType: hard

"react-use@npm:^17.4.0":
  version: 17.4.0
  resolution: "react-use@npm:17.4.0"
  dependencies:
    "@types/js-cookie": ^2.2.6
    "@xobotyi/scrollbar-width": ^1.9.5
    copy-to-clipboard: ^3.3.1
    fast-deep-equal: ^3.1.3
    fast-shallow-equal: ^1.0.0
    js-cookie: ^2.2.1
    nano-css: ^5.3.1
    react-universal-interface: ^0.6.2
    resize-observer-polyfill: ^1.5.1
    screenfull: ^5.1.0
    set-harmonic-interval: ^1.0.1
    throttle-debounce: ^3.0.1
    ts-easing: ^0.2.0
    tslib: ^2.1.0
  peerDependencies:
    react: ^16.8.0  || ^17.0.0 || ^18.0.0
    react-dom: ^16.8.0  || ^17.0.0 || ^18.0.0
  checksum: 0889da919b49a186de375ec15d2778b954ae981c523acd17dd496e4a4da7b6190efe7993491e1b85fdd6de3e745d08a4eaba4caa35408d570b5f1de550f35d11
  languageName: node
  linkType: hard

"react@npm:18.2.0, react@npm:18.x":
  version: 18.2.0
  resolution: "react@npm:18.2.0"
  dependencies:
    loose-envify: ^1.1.0
  checksum: 88e38092da8839b830cda6feef2e8505dec8ace60579e46aa5490fc3dc9bba0bd50336507dc166f43e3afc1c42939c09fe33b25fae889d6f402721dcd78fca1b
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.2":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 65645467038704f0c8aaf026a72fbb588a9e2ef7a75cd57a01702ee9db1c4a1e4b03aaad36861a6a0926546a74d174149c8c207527963e0c2d3eee2f37678a42
  languageName: node
  linkType: hard

"readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readable-stream@npm:~1.0.17, readable-stream@npm:~1.0.27-1":
  version: 1.0.34
  resolution: "readable-stream@npm:1.0.34"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.1
    isarray: 0.0.1
    string_decoder: ~0.10.x
  checksum: 85042c537e4f067daa1448a7e257a201070bfec3dd2706abdbd8ebc7f3418eb4d3ed4b8e5af63e2544d69f88ab09c28d5da3c0b77dc76185fddd189a59863b60
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"redux-cookie@npm:^0.5.9":
  version: 0.5.9
  resolution: "redux-cookie@npm:0.5.9"
  checksum: 23e6257f46974adfc21ab7c7259ca797722ea622988d858da7c7a1d5350eb40301a4f1edfb3bafb86e6cdb622dd319747102ded001aa045731c0bdd68f4b81fa
  languageName: node
  linkType: hard

"redux-logger@npm:^3.0.6":
  version: 3.0.6
  resolution: "redux-logger@npm:3.0.6"
  dependencies:
    deep-diff: ^0.3.5
  checksum: c40f63c44c6475cf6374ae0eaa810d913f142614cb80692a0beacaf135c5dc3eb3e2cdd4296f01446ba48cb69b82e81363b84d829f1f6659382c991022a814ac
  languageName: node
  linkType: hard

"redux-persist@npm:^6.0.0":
  version: 6.0.0
  resolution: "redux-persist@npm:6.0.0"
  peerDependencies:
    redux: ">4.0.0"
  checksum: edaf10dbf17351ce8058d0802357adae8665b3a1ff39371834e37838ddbe1a79cccbc717b8ba54acb5307651ccf51d0f7dc1cbc8dbae0726ff952d11ef61c6b8
  languageName: node
  linkType: hard

"redux-thunk@npm:^2.4.2":
  version: 2.4.2
  resolution: "redux-thunk@npm:2.4.2"
  peerDependencies:
    redux: ^4
  checksum: c7f757f6c383b8ec26152c113e20087818d18ed3edf438aaad43539e9a6b77b427ade755c9595c4a163b6ad3063adf3497e5fe6a36c68884eb1f1cfb6f049a5c
  languageName: node
  linkType: hard

"redux@npm:^4.0.0, redux@npm:^4.2.1":
  version: 4.2.1
  resolution: "redux@npm:4.2.1"
  dependencies:
    "@babel/runtime": ^7.9.2
  checksum: f63b9060c3a1d930ae775252bb6e579b42415aee7a23c4114e21a0b4ba7ec12f0ec76936c00f546893f06e139819f0e2855e0d55ebfce34ca9c026241a6950dd
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.4":
  version: 1.0.4
  resolution: "reflect.getprototypeof@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    get-intrinsic: ^1.2.1
    globalthis: ^1.0.3
    which-builtin-type: ^1.1.3
  checksum: 16e2361988dbdd23274b53fb2b1b9cefeab876c3941a2543b4cadac6f989e3db3957b07a44aac46cfceb3e06e2871785ec2aac992d824f76292f3b5ee87f66f2
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.1.0":
  version: 10.1.1
  resolution: "regenerate-unicode-properties@npm:10.1.1"
  dependencies:
    regenerate: ^1.4.2
  checksum: b80958ef40f125275824c2c47d5081dfaefebd80bff26c76761e9236767c748a4a95a69c053fe29d2df881177f2ca85df4a71fe70a82360388b31159ef19adcf
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.0
  resolution: "regenerator-runtime@npm:0.14.0"
  checksum: 1c977ad82a82a4412e4f639d65d22be376d3ebdd30da2c003eeafdaaacd03fc00c2320f18120007ee700900979284fc78a9f00da7fb593f6e6eeebc673fba9a3
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.15.2":
  version: 0.15.2
  resolution: "regenerator-transform@npm:0.15.2"
  dependencies:
    "@babel/runtime": ^7.8.4
  checksum: 20b6f9377d65954980fe044cfdd160de98df415b4bff38fbade67b3337efaf078308c4fed943067cd759827cc8cfeca9cb28ccda1f08333b85d6a2acbd022c27
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.0, regexp.prototype.flags@npm:^1.5.1":
  version: 1.5.1
  resolution: "regexp.prototype.flags@npm:1.5.1"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    set-function-name: ^2.0.0
  checksum: 869edff00288442f8d7fa4c9327f91d85f3b3acf8cbbef9ea7a220345cf23e9241b6def9263d2c1ebcf3a316b0aa52ad26a43a84aa02baca3381717b3e307f47
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3":
  version: 1.5.3
  resolution: "regexp.prototype.flags@npm:1.5.3"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-errors: ^1.3.0
    set-function-name: ^2.0.2
  checksum: 83ff0705b837f7cb6d664010a11642250f36d3f642263dd0f3bdfe8f150261aa7b26b50ee97f21c1da30ef82a580bb5afedbef5f45639d69edaafbeac9bbb0ed
  languageName: node
  linkType: hard

"regexpp@npm:^3.0.0":
  version: 3.2.0
  resolution: "regexpp@npm:3.2.0"
  checksum: a78dc5c7158ad9ddcfe01aa9144f46e192ddbfa7b263895a70a5c6c73edd9ce85faf7c0430e59ac38839e1734e275b9c3de5c57ee3ab6edc0e0b1bdebefccef8
  languageName: node
  linkType: hard

"regexpu-core@npm:^5.3.1":
  version: 5.3.2
  resolution: "regexpu-core@npm:5.3.2"
  dependencies:
    "@babel/regjsgen": ^0.8.0
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^10.1.0
    regjsparser: ^0.9.1
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.1.0
  checksum: 95bb97088419f5396e07769b7de96f995f58137ad75fac5811fb5fe53737766dfff35d66a0ee66babb1eb55386ef981feaef392f9df6d671f3c124812ba24da2
  languageName: node
  linkType: hard

"regjsparser@npm:^0.9.1":
  version: 0.9.1
  resolution: "regjsparser@npm:0.9.1"
  dependencies:
    jsesc: ~0.5.0
  bin:
    regjsparser: bin/parser
  checksum: 5e1b76afe8f1d03c3beaf9e0d935dd467589c3625f6d65fb8ffa14f224d783a0fed4bf49c2c1b8211043ef92b6117313419edf055a098ed8342e340586741afc
  languageName: node
  linkType: hard

"remove-accents@npm:0.4.2":
  version: 0.4.2
  resolution: "remove-accents@npm:0.4.2"
  checksum: 84a6988555dea24115e2d1954db99509588d43fe55a1590f0b5894802776f7b488b3151c37ceb9e4f4b646f26b80b7325dcea2fae58bc3865df146e1fa606711
  languageName: node
  linkType: hard

"reselect@npm:^4.1.8":
  version: 4.1.8
  resolution: "reselect@npm:4.1.8"
  checksum: a4ac87cedab198769a29be92bc221c32da76cfdad6911eda67b4d3e7136dca86208c3b210e31632eae31ebd2cded18596f0dd230d3ccc9e978df22f233b5583e
  languageName: node
  linkType: hard

"resize-observer-polyfill@npm:^1.5.1":
  version: 1.5.1
  resolution: "resize-observer-polyfill@npm:1.5.1"
  checksum: 57e7f79489867b00ba43c9c051524a5c8f162a61d5547e99333549afc23e15c44fd43f2f318ea0261ea98c0eb3158cca261e6f48d66e1ed1cd1f340a43977094
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 1012afc566b3fdb190a6309cc37ef3b2dcc35dff5fa6683a9d00cd25c3247edfbc4691b91078c97adc82a29b77a2660c30d791d65dab4fc78bfc473f60289977
  languageName: node
  linkType: hard

"resolve@npm:^1.10.1, resolve@npm:^1.14.2, resolve@npm:^1.19.0, resolve@npm:^1.22.4":
  version: 1.22.6
  resolution: "resolve@npm:1.22.6"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: d13bf66d4e2ee30d291491f16f2fa44edd4e0cefb85d53249dd6f93e70b2b8c20ec62f01b18662e3cd40e50a7528f18c4087a99490048992a3bb954cf3201a5b
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.4":
  version: 2.0.0-next.4
  resolution: "resolve@npm:2.0.0-next.4"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: c438ac9a650f2030fd074219d7f12ceb983b475da2d89ad3d6dd05fbf6b7a0a8cd37d4d10b43cb1f632bc19f22246ab7f36ebda54d84a29bfb2910a0680906d3
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.10.1#~builtin<compat/resolve>, resolve@patch:resolve@^1.14.2#~builtin<compat/resolve>, resolve@patch:resolve@^1.19.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.4#~builtin<compat/resolve>":
  version: 1.22.6
  resolution: "resolve@patch:resolve@npm%3A1.22.6#~builtin<compat/resolve>::version=1.22.6&hash=c3c19d"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 9d3b3c67aefd12cecbe5f10ca4d1f51ea190891096497c43f301b086883b426466918c3a64f1bbf1788fabb52b579d58809614006c5d0b49186702b3b8fb746a
  languageName: node
  linkType: hard

"resolve@patch:resolve@^2.0.0-next.4#~builtin<compat/resolve>":
  version: 2.0.0-next.4
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.4#~builtin<compat/resolve>::version=2.0.0-next.4&hash=c3c19d"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 4bf9f4f8a458607af90518ff73c67a4bc1a38b5a23fef2bb0ccbd45e8be89820a1639b637b0ba377eb2be9eedfb1739a84cde24fe4cd670c8207d8fea922b011
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rfs@npm:10.x":
  version: 10.0.0
  resolution: "rfs@npm:10.0.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  checksum: 4b5ca27c9bcda1bea70c9ac2ed9082ff1e9e162f5757102be20a69cd45c18e7c38946e3322c4d916036055d61a4d04f09430fda4f7f6f5bfe7d6c7690c61b1cc
  languageName: node
  linkType: hard

"rimraf@npm:3.0.2, rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"rimraf@npm:^2.2.8":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: ./bin.js
  checksum: cdc7f6eacb17927f2a075117a823e1c5951792c6498ebcce81ca8203454a811d4cf8900314154d3259bb8f0b42ab17f67396a8694a54cae3283326e57ad250cd
  languageName: node
  linkType: hard

"rtl-css-js@npm:^1.14.0":
  version: 1.16.1
  resolution: "rtl-css-js@npm:1.16.1"
  dependencies:
    "@babel/runtime": ^7.1.2
  checksum: 7d9ab942098eee565784ccf957f6b7dfa78ea1eec7c6bffedc6641575d274189e90752537c7bdba1f43ae6534648144f467fd6d581527455ba626a4300e62c7a
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rust-result@npm:^1.0.0":
  version: 1.0.0
  resolution: "rust-result@npm:1.0.0"
  dependencies:
    individual: ^2.0.0
  checksum: ccad2bdf79d3ff29e9f163db3121342b31e6d3008714851900c59da20489175f389dc3309cb92bfa5fa4d8f8842f0287567021912d37afbe5d379880af4bb95b
  languageName: node
  linkType: hard

"rxjs@npm:^7.8.1":
  version: 7.8.1
  resolution: "rxjs@npm:7.8.1"
  dependencies:
    tslib: ^2.1.0
  checksum: de4b53db1063e618ec2eca0f7965d9137cabe98cf6be9272efe6c86b47c17b987383df8574861bcced18ebd590764125a901d5506082be84a8b8e364bf05f119
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.0.1":
  version: 1.0.1
  resolution: "safe-array-concat@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.1
    has-symbols: ^1.0.3
    isarray: ^2.0.5
  checksum: 001ecf1d8af398251cbfabaf30ed66e3855127fbceee178179524b24160b49d15442f94ed6c0db0b2e796da76bb05b73bf3cc241490ec9c2b741b41d33058581
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.2":
  version: 1.1.2
  resolution: "safe-array-concat@npm:1.1.2"
  dependencies:
    call-bind: ^1.0.7
    get-intrinsic: ^1.2.4
    has-symbols: ^1.0.3
    isarray: ^2.0.5
  checksum: a3b259694754ddfb73ae0663829e396977b99ff21cbe8607f35a469655656da8e271753497e59da8a7575baa94d2e684bea3e10ddd74ba046c0c9b4418ffa0c4
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-json-parse@npm:4.0.0":
  version: 4.0.0
  resolution: "safe-json-parse@npm:4.0.0"
  dependencies:
    rust-result: ^1.0.0
  checksum: dab69dee6cfeeb5735447e4bb79da06054ff46954f9ef7504ade0ae8afe4984d44afb16adbd24adf89a83088fac8cdb07cf4ec0abc94422a1f54a4484cdb5c1a
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-regex-test@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.3
    is-regex: ^1.1.4
  checksum: bc566d8beb8b43c01b94e67de3f070fd2781685e835959bbbaaec91cc53381145ca91f69bd837ce6ec244817afa0a5e974fc4e40a2957f0aca68ac3add1ddd34
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.3":
  version: 1.0.3
  resolution: "safe-regex-test@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.6
    es-errors: ^1.3.0
    is-regex: ^1.1.4
  checksum: 6c7d392ff1ae7a3ae85273450ed02d1d131f1d2c76e177d6b03eb88e6df8fa062639070e7d311802c1615f351f18dc58f9454501c58e28d5ffd9b8f502ba6489
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sass@npm:^1.43.2":
  version: 1.68.0
  resolution: "sass@npm:1.68.0"
  dependencies:
    chokidar: ">=3.0.0 <4.0.0"
    immutable: ^4.0.0
    source-map-js: ">=0.6.2 <2.0.0"
  bin:
    sass: sass.js
  checksum: 65ccede83c96768beeb8dcaf67957b7c76b12ff1276bfd2849d7be151d46ba1400048a67717e6e5e4969bc75e87348e5530f5f272833f2e60a891c21a33d8ab0
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.0":
  version: 0.23.0
  resolution: "scheduler@npm:0.23.0"
  dependencies:
    loose-envify: ^1.1.0
  checksum: d79192eeaa12abef860c195ea45d37cbf2bbf5f66e3c4dcd16f54a7da53b17788a70d109ee3d3dde1a0fd50e6a8fc171f4300356c5aee4fc0171de526bf35f8a
  languageName: node
  linkType: hard

"screenfull@npm:^5.1.0":
  version: 5.2.0
  resolution: "screenfull@npm:5.2.0"
  checksum: 21eae33b780eb4679ea0ea2d14734b11168cf35049c45a2bf24ddeb39c67a788e7a8fb46d8b61ca6d8367fd67ce9dd4fc8bfe476489249c7189c2a79cf83f51a
  languageName: node
  linkType: hard

"semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^6.1.0, semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.4":
  version: 7.5.4
  resolution: "semver@npm:7.5.4"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: 12d8ad952fa353b0995bf180cdac205a4068b759a140e5d3c608317098b3575ac2f1e09182206bf2eb26120e1c0ed8fb92c48c592f6099680de56bb071423ca3
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.1":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.0, set-function-name@npm:^2.0.1":
  version: 2.0.1
  resolution: "set-function-name@npm:2.0.1"
  dependencies:
    define-data-property: ^1.0.1
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.0
  checksum: 4975d17d90c40168eee2c7c9c59d023429f0a1690a89d75656306481ece0c3c1fb1ebcc0150ea546d1913e35fbd037bace91372c69e543e51fc5d1f31a9fa126
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.2
  checksum: d6229a71527fd0404399fc6227e0ff0652800362510822a291925c9d7b48a1ca1a468b11b281471c34cd5a2da0db4f5d7ff315a61d26655e77f6e971e6d0c80f
  languageName: node
  linkType: hard

"set-harmonic-interval@npm:^1.0.1":
  version: 1.0.1
  resolution: "set-harmonic-interval@npm:1.0.1"
  checksum: c122b831c2e0b1fb812e5e9d065094b9d174bd0576f9a779ab7a7d8881c8f6dd7d5fcab9a2553da15eea670eb598f9dd4d5162b626d45cc9c529706aa1444a84
  languageName: node
  linkType: hard

"shallowequal@npm:^1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: f4c1de0837f106d2dbbfd5d0720a5d059d1c66b42b580965c8f06bb1db684be8783538b684092648c981294bf817869f743a066538771dbecb293df78f765e00
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4":
  version: 1.0.4
  resolution: "side-channel@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.0
    get-intrinsic: ^1.0.2
    object-inspect: ^1.9.0
  checksum: 351e41b947079c10bd0858364f32bb3a7379514c399edb64ab3dce683933483fc63fb5e4efe0a15a2e8a7e3c436b6a91736ddb8d8c6591b0460a24bb4a1ee245
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"slash@npm:^2.0.0":
  version: 2.0.0
  resolution: "slash@npm:2.0.0"
  checksum: 512d4350735375bd11647233cb0e2f93beca6f53441015eea241fe784d8068281c3987fbaa93e7ef1c38df68d9c60013045c92837423c69115297d6169aa85e6
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "socks-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.3
    socks: ^2.6.2
  checksum: 720554370154cbc979e2e9ce6a6ec6ced205d02757d8f5d93fe95adae454fc187a5cbfc6b022afab850a5ce9b4c7d73e0f98e381879cf45f66317a4895953846
  languageName: node
  linkType: hard

"socks@npm:^2.6.2":
  version: 2.7.1
  resolution: "socks@npm:2.7.1"
  dependencies:
    ip: ^2.0.0
    smart-buffer: ^4.2.0
  checksum: 259d9e3e8e1c9809a7f5c32238c3d4d2a36b39b83851d0f573bfde5f21c4b1288417ce1af06af1452569cd1eb0841169afd4998f0e04ba04656f6b7f0e46d748
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.0.2":
  version: 1.0.2
  resolution: "source-map-js@npm:1.0.2"
  checksum: c049a7fc4deb9a7e9b481ae3d424cc793cb4845daa690bc5a05d428bf41bf231ced49b4cf0c9e77f9d42fdb3d20d6187619fc586605f5eabe995a316da8d377c
  languageName: node
  linkType: hard

"source-map@npm:0.5.6":
  version: 0.5.6
  resolution: "source-map@npm:0.5.6"
  checksum: 390b3f5165c9631a74fb6fb55ba61e62a7f9b7d4026ae0e2bfc2899c241d71c1bccb8731c496dc7f7cb79a5f523406eb03d8c5bebe8448ee3fc38168e2d209c8
  languageName: node
  linkType: hard

"source-map@npm:^0.5.7":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 5dc2043b93d2f194142c7f38f74a24670cd7a0063acdaf4bf01d2964b402257ae843c2a8fa822ad5b71013b5fcafa55af7421383da919752f22ff488bc553f4d
  languageName: node
  linkType: hard

"source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"sourcemap-codec@npm:^1.4.8":
  version: 1.4.8
  resolution: "sourcemap-codec@npm:1.4.8"
  checksum: b57981c05611afef31605732b598ccf65124a9fcb03b833532659ac4d29ac0f7bfacbc0d6c5a28a03e84c7510e7e556d758d0bb57786e214660016fb94279316
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.5
  resolution: "ssri@npm:10.0.5"
  dependencies:
    minipass: ^7.0.3
  checksum: 0a31b65f21872dea1ed3f7c200d7bc1c1b91c15e419deca14f282508ba917cbb342c08a6814c7f68ca4ca4116dd1a85da2bbf39227480e50125a1ceffeecb750
  languageName: node
  linkType: hard

"stack-generator@npm:^2.0.5":
  version: 2.0.10
  resolution: "stack-generator@npm:2.0.10"
  dependencies:
    stackframe: ^1.3.4
  checksum: 4fc3978a934424218a0aa9f398034e1f78153d5ff4f4ff9c62478c672debb47dd58de05b09fc3900530cbb526d72c93a6e6c9353bacc698e3b1c00ca3dda0c47
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: bae1596873595c4610993fa84f86a3387d67586401c1816ea048c0196800c0646c4d2da98c2ee80557fd9eff05877efe33b91ba6cd052658ed96ddc85d19067d
  languageName: node
  linkType: hard

"stacktrace-gps@npm:^3.0.4":
  version: 3.1.2
  resolution: "stacktrace-gps@npm:3.1.2"
  dependencies:
    source-map: 0.5.6
    stackframe: ^1.3.4
  checksum: 85daa232d138239b6ae0f4bcdd87d15d302a045d93625db17614030945b5314e204b5fbcf9bee5b6f4f9e6af5fca05f65c27fe910894b861ef6853b99470aa1c
  languageName: node
  linkType: hard

"stacktrace-js@npm:^2.0.2":
  version: 2.0.2
  resolution: "stacktrace-js@npm:2.0.2"
  dependencies:
    error-stack-parser: ^2.0.6
    stack-generator: ^2.0.5
    stacktrace-gps: ^3.0.4
  checksum: 081e786d56188ac04ac6604c09cd863b3ca2b4300ec061366cf68c3e4ad9edaa34fb40deea03cc23a05f442aa341e9171f47313f19bd588f9bec6c505a396286
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 1cce16cea8405d7a233d32ca5e00a00169cc0e19fbc02aa839959985f267335d435c07f96e5e0edd0eadc6d39c98d5435fb5bbbdefc62c41834eadc5622ad942
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.8":
  version: 4.0.10
  resolution: "string.prototype.matchall@npm:4.0.10"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    get-intrinsic: ^1.2.1
    has-symbols: ^1.0.3
    internal-slot: ^1.0.5
    regexp.prototype.flags: ^1.5.0
    set-function-name: ^2.0.0
    side-channel: ^1.0.4
  checksum: 3c78bdeff39360c8e435d7c4c6ea19f454aa7a63eda95fa6fadc3a5b984446a2f9f2c02d5c94171ce22268a573524263fbd0c8edbe3ce2e9890d7cc036cdc3ed
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.8":
  version: 1.2.8
  resolution: "string.prototype.trim@npm:1.2.8"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
  checksum: 49eb1a862a53aba73c3fb6c2a53f5463173cb1f4512374b623bcd6b43ad49dd559a06fb5789bdec771a40fc4d2a564411c0a75d35fb27e76bbe738c211ecff07
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.9":
  version: 1.2.9
  resolution: "string.prototype.trim@npm:1.2.9"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.0
    es-object-atoms: ^1.0.0
  checksum: ea2df6ec1e914c9d4e2dc856fa08228e8b1be59b59e50b17578c94a66a176888f417264bb763d4aac638ad3b3dad56e7a03d9317086a178078d131aa293ba193
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.7":
  version: 1.0.7
  resolution: "string.prototype.trimend@npm:1.0.7"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
  checksum: 2375516272fd1ba75992f4c4aa88a7b5f3c7a9ca308d963bcd5645adf689eba6f8a04ebab80c33e30ec0aefc6554181a3a8416015c38da0aa118e60ec896310c
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimend@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: cc3bd2de08d8968a28787deba9a3cb3f17ca5f9f770c91e7e8fa3e7d47f079bad70fadce16f05dda9f261788be2c6e84a942f618c3bed31e42abc5c1084f8dfd
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.7":
  version: 1.0.7
  resolution: "string.prototype.trimstart@npm:1.0.7"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
  checksum: 13d0c2cb0d5ff9e926fa0bec559158b062eed2b68cd5be777ffba782c96b2b492944e47057274e064549b94dd27cf81f48b27a31fee8af5b574cff253e7eb613
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: df1007a7f580a49d692375d996521dc14fd103acda7f3034b3c558a60b82beeed3a64fa91e494e164581793a8ab0ae2f59578a49896a7af6583c1f20472bce96
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~0.10.x":
  version: 0.10.31
  resolution: "string_decoder@npm:0.10.31"
  checksum: fe00f8e303647e5db919948ccb5ce0da7dea209ab54702894dd0c664edd98e5d4df4b80d6fabf7b9e92b237359d21136c95bf068b2f7760b772ca974ba970202
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"styled-components@npm:^6.0.4":
  version: 6.0.8
  resolution: "styled-components@npm:6.0.8"
  dependencies:
    "@babel/cli": ^7.21.0
    "@babel/core": ^7.21.0
    "@babel/helper-module-imports": ^7.18.6
    "@babel/plugin-external-helpers": ^7.18.6
    "@babel/plugin-proposal-class-properties": ^7.18.6
    "@babel/plugin-proposal-object-rest-spread": ^7.20.7
    "@babel/preset-env": ^7.20.2
    "@babel/preset-react": ^7.18.6
    "@babel/preset-typescript": ^7.21.0
    "@babel/traverse": ^7.21.2
    "@emotion/is-prop-valid": ^1.2.1
    "@emotion/unitless": ^0.8.0
    "@types/stylis": ^4.0.2
    css-to-react-native: ^3.2.0
    csstype: ^3.1.2
    postcss: ^8.4.23
    shallowequal: ^1.1.0
    stylis: ^4.3.0
    tslib: ^2.5.0
  peerDependencies:
    babel-plugin-styled-components: ">= 2"
    react: ">= 16.8.0"
    react-dom: ">= 16.8.0"
  peerDependenciesMeta:
    babel-plugin-styled-components:
      optional: true
  checksum: 758f19ada2a14e3fc12a0e1094d55b065a3d0afc57e33dbb3c57b8322d4e72ecc3091c9c87e083cec733fc19c6fdd0bc5de9456f299f86ce1c3ffaae21ac6377
  languageName: node
  linkType: hard

"styled-jsx@npm:5.1.1":
  version: 5.1.1
  resolution: "styled-jsx@npm:5.1.1"
  dependencies:
    client-only: 0.0.1
  peerDependencies:
    react: ">= 16.8.0 || 17.x.x || ^18.0.0-0"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-plugin-macros:
      optional: true
  checksum: 523a33b38603492547e861b98e29c873939b04e15fbe5ef16132c6f1e15958126647983c7d4675325038b428a5e91183d996e90141b18bdd1bbadf6e2c45b2fa
  languageName: node
  linkType: hard

"stylis@npm:4.2.0":
  version: 4.2.0
  resolution: "stylis@npm:4.2.0"
  checksum: 0eb6cc1b866dc17a6037d0a82ac7fa877eba6a757443e79e7c4f35bacedbf6421fadcab4363b39667b43355cbaaa570a3cde850f776498e5450f32ed2f9b7584
  languageName: node
  linkType: hard

"stylis@npm:^4.0.6, stylis@npm:^4.3.0":
  version: 4.3.0
  resolution: "stylis@npm:4.3.0"
  checksum: 6120de3f03eacf3b5adc8e7919c4cca991089156a6badc5248752a3088106afaaf74996211a6817a7760ebeadca09004048eea31875bd8d4df51386365c50025
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"swr@npm:^1.3.0":
  version: 1.3.0
  resolution: "swr@npm:1.3.0"
  peerDependencies:
    react: ^16.11.0 || ^17.0.0 || ^18.0.0
  checksum: e7a184f0d560e9c8be85c023cc8e65e56a88a6ed46f9394b301b07f838edca23d2e303685319a4fcd620b81d447a7bcb489c7fa0a752c259f91764903c690cdb
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.1.2":
  version: 6.2.0
  resolution: "tar@npm:6.2.0"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^5.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: db4d9fe74a2082c3a5016630092c54c8375ff3b280186938cfd104f2e089c4fd9bad58688ef6be9cf186a889671bf355c7cda38f09bbf60604b281715ca57f5c
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0, thenify-all@npm:^1.6.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: ">= 3.1.0 < 4"
  checksum: dba7cc8a23a154cdcb6acb7f51d61511c37a6b077ec5ab5da6e8b874272015937788402fd271fdfc5f187f8cb0948e38d0a42dcc89d554d731652ab458f5343e
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: ^1.0.0
  checksum: 84e1b804bfec49f3531215f17b4a6e50fd4397b5f7c1bccc427b9c656e1ecfb13ea79d899930184f78bc2f57285c54d9a50a590c8868f4f0cef5c1d9f898b05e
  languageName: node
  linkType: hard

"throttle-debounce@npm:^3.0.1":
  version: 3.0.1
  resolution: "throttle-debounce@npm:3.0.1"
  checksum: e34ef638e8df3a9154249101b68afcbf2652a139c803415ef8a2f6a8bc577bcd4d79e4bb914ad3cd206523ac78b9fb7e80885bfa049f64fbb1927f99d98b5736
  languageName: node
  linkType: hard

"through2@npm:~0.4.1":
  version: 0.4.2
  resolution: "through2@npm:0.4.2"
  dependencies:
    readable-stream: ~1.0.17
    xtend: ~2.1.1
  checksum: 50e41d272db4a74b10a62b7e92eeeb8d30e426a7a8a772cd85fac0f8e21d92c6e5cb5012d7db5f7a20f6e147e1f14f87062058c77b05bc9d463ae4d8b3eb1e42
  languageName: node
  linkType: hard

"through@npm:^2.3.8":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: a38c3e059853c494af95d50c072b83f8b676a9ba2818dcc5b108ef252230735c54e0185437618596c790bbba8fcdaef5b290405981ffa09dce67b1f1bf190cbd
  languageName: node
  linkType: hard

"tiny-warning@npm:^1.0.2":
  version: 1.0.3
  resolution: "tiny-warning@npm:1.0.3"
  checksum: da62c4acac565902f0624b123eed6dd3509bc9a8d30c06e017104bedcf5d35810da8ff72864400ad19c5c7806fc0a8323c68baf3e326af7cb7d969f846100d71
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toggle-selection@npm:^1.0.6":
  version: 1.0.6
  resolution: "toggle-selection@npm:1.0.6"
  checksum: a90dc80ed1e7b18db8f4e16e86a5574f87632dc729cfc07d9ea3ced50021ad42bb4e08f22c0913e0b98e3837b0b717e0a51613c65f30418e21eb99da6556a74c
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.0.1":
  version: 1.0.3
  resolution: "ts-api-utils@npm:1.0.3"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: 441cc4489d65fd515ae6b0f4eb8690057add6f3b6a63a36073753547fb6ce0c9ea0e0530220a0b282b0eec535f52c4dfc315d35f8a4c9a91c0def0707a714ca6
  languageName: node
  linkType: hard

"ts-easing@npm:^0.2.0":
  version: 0.2.0
  resolution: "ts-easing@npm:0.2.0"
  checksum: e67ee862acca3b2e2718e736f31999adcef862d0df76d76a0e138588728d8a87dfec9978556044640bd0e90203590ad88ac2fe8746d0e9959b8d399132315150
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.14.2":
  version: 3.14.2
  resolution: "tsconfig-paths@npm:3.14.2"
  dependencies:
    "@types/json5": ^0.0.29
    json5: ^1.0.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: a6162eaa1aed680537f93621b82399c7856afd10ec299867b13a0675e981acac4e0ec00896860480efc59fc10fd0b16fdc928c0b885865b52be62cadac692447
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": ^0.0.29
    json5: ^1.0.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 59f35407a390d9482b320451f52a411a256a130ff0e7543d18c6f20afab29ac19fbe55c360a93d6476213cc335a4d76ce90f67df54c4e9037f7d240920832201
  languageName: node
  linkType: hard

"tslib@npm:^2.1.0, tslib@npm:^2.4.0, tslib@npm:^2.4.1, tslib@npm:^2.5.0":
  version: 2.6.2
  resolution: "tslib@npm:2.6.2"
  checksum: 329ea56123005922f39642318e3d1f0f8265d1e7fcb92c633e0809521da75eeaca28d2cf96d7248229deb40e5c19adf408259f4b9640afd20d13aecc1430f3ad
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 4fb3272df21ad1c552486f8a2f8e115c09a521ad7a8db3d56d53718d0c907b62c6e9141ba5f584af3f6830d0872c521357e512381f24f7c44acae583ad517d73
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-buffer@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.1
    is-typed-array: ^1.1.10
  checksum: 3e0281c79b2a40cd97fe715db803884301993f4e8c18e8d79d75fd18f796e8cd203310fec8c7fdb5e6c09bedf0af4f6ab8b75eb3d3a85da69328f28a80456bd3
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "typed-array-buffer@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.7
    es-errors: ^1.3.0
    is-typed-array: ^1.1.13
  checksum: 02ffc185d29c6df07968272b15d5319a1610817916ec8d4cd670ded5d1efe72901541ff2202fcc622730d8a549c76e198a2f74e312eabbfb712ed907d45cbb0b
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-byte-length@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    for-each: ^0.3.3
    has-proto: ^1.0.1
    is-typed-array: ^1.1.10
  checksum: b03db16458322b263d87a702ff25388293f1356326c8a678d7515767ef563ef80e1e67ce648b821ec13178dd628eb2afdc19f97001ceae7a31acf674c849af94
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.1":
  version: 1.0.1
  resolution: "typed-array-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-proto: ^1.0.3
    is-typed-array: ^1.1.13
  checksum: f65e5ecd1cf76b1a2d0d6f631f3ea3cdb5e08da106c6703ffe687d583e49954d570cc80434816d3746e18be889ffe53c58bf3e538081ea4077c26a41055b216d
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-byte-offset@npm:1.0.0"
  dependencies:
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    for-each: ^0.3.3
    has-proto: ^1.0.1
    is-typed-array: ^1.1.10
  checksum: 04f6f02d0e9a948a95fbfe0d5a70b002191fae0b8fe0fe3130a9b2336f043daf7a3dda56a31333c35a067a97e13f539949ab261ca0f3692c41603a46a94e960b
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.2":
  version: 1.0.2
  resolution: "typed-array-byte-offset@npm:1.0.2"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-proto: ^1.0.3
    is-typed-array: ^1.1.13
  checksum: c8645c8794a621a0adcc142e0e2c57b1823bbfa4d590ad2c76b266aa3823895cf7afb9a893bf6685e18454ab1b0241e1a8d885a2d1340948efa4b56add4b5f67
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-length@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.2
    for-each: ^0.3.3
    is-typed-array: ^1.1.9
  checksum: 2228febc93c7feff142b8c96a58d4a0d7623ecde6c7a24b2b98eb3170e99f7c7eff8c114f9b283085cd59dcd2bd43aadf20e25bba4b034a53c5bb292f71f8956
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.6":
  version: 1.0.6
  resolution: "typed-array-length@npm:1.0.6"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-proto: ^1.0.3
    is-typed-array: ^1.1.13
    possible-typed-array-names: ^1.0.0
  checksum: f0315e5b8f0168c29d390ff410ad13e4d511c78e6006df4a104576844812ee447fcc32daab1f3a76c9ef4f64eff808e134528b5b2439de335586b392e9750e5c
  languageName: node
  linkType: hard

"typescript@npm:5.x":
  version: 5.2.2
  resolution: "typescript@npm:5.2.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 7912821dac4d962d315c36800fe387cdc0a6298dba7ec171b350b4a6e988b51d7b8f051317786db1094bd7431d526b648aba7da8236607febb26cf5b871d2d3c
  languageName: node
  linkType: hard

"typescript@patch:typescript@5.x#~builtin<compat/typescript>":
  version: 5.2.2
  resolution: "typescript@patch:typescript@npm%3A5.2.2#~builtin<compat/typescript>::version=5.2.2&hash=f3b441"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 0f4da2f15e6f1245e49db15801dbee52f2bbfb267e1c39225afdab5afee1a72839cd86000e65ee9d7e4dfaff12239d28beaf5ee431357fcced15fb08583d72ca
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "unbox-primitive@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
    has-bigints: ^1.0.2
    has-symbols: ^1.0.3
    which-boxed-primitive: ^1.0.2
  checksum: b7a1cf5862b5e4b5deb091672ffa579aa274f648410009c81cca63fed3b62b610c4f3b773f912ce545bb4e31edc3138975b5bc777fc6e4817dca51affb6380e9
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.0"
  checksum: 39be078afd014c14dcd957a7a46a60061bc37c4508ba146517f85f60361acf4c7539552645ece25de840e17e293baa5556268d091ca6762747fdd0c705001a45
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.1.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.1.0"
  checksum: 8d6f5f586b9ce1ed0e84a37df6b42fdba1317a05b5df0c249962bd5da89528771e2d149837cad11aa26bcb84c35355cb9f58a10c3d41fa3b899181ece6c85220
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: ^4.0.0
  checksum: 8e2f59b356cb2e54aab14ff98a51ac6c45781d15ceaab6d4f1c2228b780193dc70fae4463ce9e1df4479cb9d3304d7c2043a3fb905bdeca71cc7e8ce27e063df
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 0884b58365af59f89739e6f71e3feacb5b1b41f2df2d842d0757933620e6de08eff347d27e9d499b43c40476cbaf7988638d3acb2ffbcb9d35fd035591adfd15
  languageName: node
  linkType: hard

"universal-cookie@npm:^4.0.4":
  version: 4.0.4
  resolution: "universal-cookie@npm:4.0.4"
  dependencies:
    "@types/cookie": ^0.3.3
    cookie: ^0.4.0
  checksum: bb2bafa7eb7e213e5448924329572dd1a913be00e23906189be2a0dc889b0eea1750f9c33462fc1c911d89092dbd82f6e220c2d61c4057bb3f69e7665d9d8ddf
  languageName: node
  linkType: hard

"unload@npm:2.2.0":
  version: 2.2.0
  resolution: "unload@npm:2.2.0"
  dependencies:
    "@babel/runtime": ^7.6.2
    detect-node: ^2.0.4
  checksum: 88ba950c5ff83ab4f9bbd8f63bbf19ba09687ed3c434efd43b7338cc595bc574df8f9b155ee6eee7a435de3d3a4a226726988428977a68ba4907045f1fac5d41
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.0.13":
  version: 1.0.13
  resolution: "update-browserslist-db@npm:1.0.13"
  dependencies:
    escalade: ^3.1.1
    picocolors: ^1.0.0
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 1e47d80182ab6e4ad35396ad8b61008ae2a1330221175d0abd37689658bdb61af9b705bfc41057fd16682474d79944fb2d86767c5ed5ae34b6276b9bed353322
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"url-toolkit@npm:^2.2.1":
  version: 2.2.5
  resolution: "url-toolkit@npm:2.2.5"
  checksum: c784040bd4dbd78647a62218b6b8c1abd9a2f7fd8adce1851daf21dc2d98e2a5d69b78a628ec07dcfa112c16a112a182e109b7c872b6e8a1e4a1547b49b81f7b
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.0.0":
  version: 1.2.0
  resolution: "use-sync-external-store@npm:1.2.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: 5c639e0f8da3521d605f59ce5be9e094ca772bd44a4ce7322b055a6f58eeed8dda3c94cabd90c7a41fb6fa852210092008afe48f7038792fd47501f33299116a
  languageName: node
  linkType: hard

"usehooks-ts@npm:^2.9.1":
  version: 2.9.1
  resolution: "usehooks-ts@npm:2.9.1"
  peerDependencies:
    react: ^16.8.0  || ^17.0.0 || ^18.0.0
    react-dom: ^16.8.0  || ^17.0.0 || ^18.0.0
  checksum: 36f1e4142ce23bc019b81d2e93aefd7f2c350abcf255598c21627114a69a2f2f116b35dc3a353375f09c6e4c9b704a04f104e3d10e98280545c097feca66c30a
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 39931f6da74e307f51c0fb463dc2462807531dc80760a9bff1e35af4316131b4fc3203d16da60ae33f07fdca5b56f3f1dd662da0c99fea9aaeab2004780cc5f4
  languageName: node
  linkType: hard

"video.js@npm:^6 || ^7 || ^8, video.js@npm:^7 || ^8":
  version: 8.6.0
  resolution: "video.js@npm:8.6.0"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/http-streaming": 3.6.0
    "@videojs/vhs-utils": ^4.0.0
    "@videojs/xhr": 2.6.0
    aes-decrypter: ^4.0.1
    global: 4.4.0
    keycode: 2.2.0
    m3u8-parser: ^6.0.0
    mpd-parser: ^1.0.1
    mux.js: ^6.2.0
    safe-json-parse: 4.0.0
    videojs-contrib-quality-levels: 4.0.0
    videojs-font: 4.1.0
    videojs-vtt.js: 0.15.5
  checksum: de950f685ba584dfa76cb3cf2f1c18038bc7d6d0a06a50a50ee58af769c67f0abcdedbfb421e32be93557fd0b83d1239e9b2d8df1434b64f1a8d7bbc43ddd3ab
  languageName: node
  linkType: hard

"video.js@npm:^6 || ^7, video.js@npm:^7.17.0, video.js@npm:^7.5.5, video.js@npm:^7.6.0":
  version: 7.21.5
  resolution: "video.js@npm:7.21.5"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@videojs/http-streaming": 2.16.2
    "@videojs/vhs-utils": ^3.0.4
    "@videojs/xhr": 2.6.0
    aes-decrypter: 3.1.3
    global: ^4.4.0
    keycode: ^2.2.0
    m3u8-parser: 4.8.0
    mpd-parser: 0.22.1
    mux.js: 6.0.1
    safe-json-parse: 4.0.0
    videojs-font: 3.2.0
    videojs-vtt.js: ^0.15.5
  checksum: 8986953804d5fd2e584b98895ff35400fd04735bbc3286f851e9650ced5012084856a639147b9fb0e6bc9ba66fb709971c17276e135e8ac7ac6dcdf96daca441
  languageName: node
  linkType: hard

"videojs-contextmenu-ui@npm:^5.2.0":
  version: 5.2.0
  resolution: "videojs-contextmenu-ui@npm:5.2.0"
  dependencies:
    global: ^4.4.0
    video.js: ^7.6.0
  checksum: 65f7720e8a07fdace13c4ca9037b47a9791d76ca8376b575532153b1fb324fe26de0d7ae1ad6ac6fc2928ff357e63a11078eddbbe94b72f9e6b31ef256f2e4d2
  languageName: node
  linkType: hard

"videojs-contrib-quality-levels@npm:4.0.0":
  version: 4.0.0
  resolution: "videojs-contrib-quality-levels@npm:4.0.0"
  dependencies:
    global: ^4.4.0
  peerDependencies:
    video.js: ^8
  checksum: 08d6b1159f0c2a97994213ef5bf84498c29020b06ab8ac0095d5cf6e3400748eede5daf27877c1f52bcc8604e34046ba45e3ec69bb7fc184a33bca00237fab25
  languageName: node
  linkType: hard

"videojs-contrib-quality-levels@npm:^2.0.9, videojs-contrib-quality-levels@npm:^2.1.0":
  version: 2.2.1
  resolution: "videojs-contrib-quality-levels@npm:2.2.1"
  dependencies:
    global: ^4.3.2
    video.js: ^6 || ^7 || ^8
  peerDependencies:
    video.js: ^6 || ^7 || ^8
  checksum: 0903dd1d42f7ce347ba6c90f2fe1baca10c7e604d32957139d8d3a8784ccfea54387e44fe81fb1183fe940e63edf15d15d2e79976c8219895aa7b2a48072685d
  languageName: node
  linkType: hard

"videojs-font@npm:3.2.0":
  version: 3.2.0
  resolution: "videojs-font@npm:3.2.0"
  checksum: 3c773d4a64123c237c26029a5d04c51919e3b6e688db0a20c6f9c647198ec815425d96a62dd24989046f27aa583c96cecae715f789f8d98452b4df419c23158a
  languageName: node
  linkType: hard

"videojs-font@npm:4.1.0":
  version: 4.1.0
  resolution: "videojs-font@npm:4.1.0"
  checksum: 7259a9e1855e269cfc08c72b031e4fa1e0008a8da7f3352b407a990177a06176c3ba69d732d91f7c9b2c1ae8b62f6aa9b5b6049ad8e0a98b86f5baea44c3d31f
  languageName: node
  linkType: hard

"videojs-vtt.js@npm:0.15.5, videojs-vtt.js@npm:^0.15.5":
  version: 0.15.5
  resolution: "videojs-vtt.js@npm:0.15.5"
  dependencies:
    global: ^4.3.1
  checksum: 2658de26830e412f7539f2674019348374d36f05fcdae548be001d596798f50c0d2e026163afdeff34286919de7aae08cb97af612925164167204b301d246241
  languageName: node
  linkType: hard

"void-elements@npm:3.1.0":
  version: 3.1.0
  resolution: "void-elements@npm:3.1.0"
  checksum: 0390f818107fa8fce55bb0a5c3f661056001c1d5a2a48c28d582d4d847347c2ab5b7f8272314cac58acf62345126b6b09bea623a185935f6b1c3bbce0dfd7f7f
  languageName: node
  linkType: hard

"watchpack@npm:2.4.0":
  version: 2.4.0
  resolution: "watchpack@npm:2.4.0"
  dependencies:
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.1.2
  checksum: 23d4bc58634dbe13b86093e01c6a68d8096028b664ab7139d58f0c37d962d549a940e98f2f201cecdabd6f9c340338dc73ef8bf094a2249ef582f35183d1a131
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-boxed-primitive@npm:1.0.2"
  dependencies:
    is-bigint: ^1.0.1
    is-boolean-object: ^1.1.0
    is-number-object: ^1.0.4
    is-string: ^1.0.5
    is-symbol: ^1.0.3
  checksum: 53ce774c7379071729533922adcca47220228405e1895f26673bbd71bdf7fb09bee38c1d6399395927c6289476b5ae0629863427fd151491b71c4b6cb04f3a5e
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.1.3":
  version: 1.1.3
  resolution: "which-builtin-type@npm:1.1.3"
  dependencies:
    function.prototype.name: ^1.1.5
    has-tostringtag: ^1.0.0
    is-async-function: ^2.0.0
    is-date-object: ^1.0.5
    is-finalizationregistry: ^1.0.2
    is-generator-function: ^1.0.10
    is-regex: ^1.1.4
    is-weakref: ^1.0.2
    isarray: ^2.0.5
    which-boxed-primitive: ^1.0.2
    which-collection: ^1.0.1
    which-typed-array: ^1.1.9
  checksum: 43730f7d8660ff9e33d1d3f9f9451c4784265ee7bf222babc35e61674a11a08e1c2925019d6c03154fcaaca4541df43abe35d2720843b9b4cbcebdcc31408f36
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.1":
  version: 1.0.1
  resolution: "which-collection@npm:1.0.1"
  dependencies:
    is-map: ^2.0.1
    is-set: ^2.0.1
    is-weakmap: ^2.0.1
    is-weakset: ^2.0.1
  checksum: c815bbd163107ef9cb84f135e6f34453eaf4cca994e7ba85ddb0d27cea724c623fae2a473ceccfd5549c53cc65a5d82692de418166df3f858e1e5dc60818581c
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.11, which-typed-array@npm:^1.1.9":
  version: 1.1.11
  resolution: "which-typed-array@npm:1.1.11"
  dependencies:
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-tostringtag: ^1.0.0
  checksum: 711ffc8ef891ca6597b19539075ec3e08bb9b4c2ca1f78887e3c07a977ab91ac1421940505a197758fb5939aa9524976d0a5bbcac34d07ed6faa75cedbb17206
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.14, which-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "which-typed-array@npm:1.1.15"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-tostringtag: ^1.0.2
  checksum: 65227dcbfadf5677aacc43ec84356d17b5500cb8b8753059bb4397de5cd0c2de681d24e1a7bd575633f976a95f88233abfd6549c2105ef4ebd58af8aa1807c75
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.5":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: d5fc37cd561f9daee3c80e03b92ed3e84d80dde3365a8767263d03dacfc8fa06b065ffe1df00d8c2a09f731482fcacae745abfbb478d4af36d0a891fad4834d3
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"xtend@npm:~2.1.1":
  version: 2.1.2
  resolution: "xtend@npm:2.1.2"
  dependencies:
    object-keys: ~0.4.0
  checksum: a8b79f31502c163205984eaa2b196051cd2fab0882b49758e30f2f9018255bc6c462e32a090bf3385d1bda04755ad8cc0052a09e049b0038f49eb9b950d9c447
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: ce4ada136e8a78a0b08dc10b4b900936912d15de59905b2bf415b4d33c63df1d555d23acb2a41b23cf9fb5da41c256441afca3d6509de7247daa062fd2c5ea5f
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zod@npm:3.21.4":
  version: 3.21.4
  resolution: "zod@npm:3.21.4"
  checksum: f185ba87342ff16f7a06686767c2b2a7af41110c7edf7c1974095d8db7a73792696bcb4a00853de0d2edeb34a5b2ea6a55871bc864227dace682a0a28de33e1f
  languageName: node
  linkType: hard
