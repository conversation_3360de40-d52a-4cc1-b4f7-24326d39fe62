import type { GetServerSideProps, NextPage } from 'next';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import { useTranslation } from 'next-i18next';
import i18n from '@/utils/i18n';
import Terms from '@/components/Terms';
import PageContent from '@/components/_PageComponents/PageContent';
import { useMemo } from 'react';

const TermsPage: NextPage = () => {
    const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
    const title = useMemo(() => seoTranslation("page.termsAndCondition.title"), [seoTranslation]);
    return (
        <PageContent
            title={title}
            content={<Terms />}
        />
    );
};

export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    context
});

export default TermsPage;