@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

$tablet-header-height: map.get(theme.$height, "tablet-header");
$desktop-header-height: map.get(theme.$height, "desktop-header");
$background-color: map.get(theme.$color, "background");
$primary-color: map.get(theme.$color, "primary");
$secondary-color: map.get(theme.$color, "secondary");

.label {
    font-size: 0.7rem !important;
    background-color: $primary-color;
    color: white;
    padding: 4px 8px;
    border-radius: 16px;
}
div[class~="MuiMenu-paper"] .optionValue
{
    padding-left: 8px;
}

.stepperButton {
    background-color: $primary-color !important;
    color: white !important;
    border-radius: 8px !important;
    font-size: 2rem !important;
    padding: 1rem 1.5rem !important;
    box-shadow: 10px -8px 5px -2px rgb(0 0 0 / 10%), 6px -2px 2px 0px rgb(0 0 0 / 2%) !important;

    &:hover {
        border: 1px solid $primary-color !important;
        color: #fde5ca !important;
    }

    &.stepperButtonDisabled {
        color: rgba(0, 0, 0, 0.26) !important;
        background-color: white !important;
        border: 1px solid #ff7802 !important;
    }

    &.clearAll {
        background-color: red;

        &.stepperButtonDisabled {
            color: rgba(0, 0, 0, 0.26);
            background-color: white;
            border: 1px solid #ff7802;
        }
    }
}
.quantity {
    font-size: 1.1rem;
    color: rgb(87, 87, 87) !important;
}
.kisokSectionBox {
    max-height: calc(100vh - 80px);
    height: calc(100vh - 80px);
    background: #fafafa;

    &:nth-child(2n) {
        background: white;
    }
}

.sessionSelected {
    background-color: #ff7802;
    color: #fafafa;

    &:hover {
        background-color: #ff7802;
    }
}