@use 'sass:map';
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;

$highlight-text-color: map.get(theme.$color, "highlight-text");
$success-text-color: map.get(theme.$color, "success-text");
$primary-color: map.get(theme.$color, "primary-color");
.terms {
    text-decoration: underline;
    display: inline-block;
    &:hover {
      color: $primary-color;
      transition: all animations.$fast-animation-speed;
    }
};

.warningText {
    color: $highlight-text-color;
    font-weight: bold;
    margin: 30px auto 10px auto;
    padding: 2px 4px;
};

.successText {
    color: $success-text-color;
    font-weight: bold;
    margin: 30px auto 10px auto;
    padding: 2px 4px;
};