import * as React from "react";
import Grid from "@mui/material/Grid";
import CircularProgress from "@mui/material/CircularProgress";
import { ReactNode } from "react";

interface LoadingBarProps {
  size?: number;
  width?: string;
  maxWidth?: string;
  height?: string;
  margin?: string;
  children?: ReactNode;
  disableShrink?: boolean;
}

export function LoadingCircle(props: LoadingBarProps) {
  const { size, height, width, maxWidth, margin, disableShrink } = props;

  return (
    <>
      <Grid
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
        style={{
          width: width ?? "100%",
          maxWidth: maxWidth,
          height: height,
          margin: margin,
          padding: "40px",
        }}
      >
        {props.children}
        <CircularProgress
          size={!size ? 100 : size}
          disableShrink={disableShrink}
          color="secondary"
        />
      </Grid>
    </>
  );
}
