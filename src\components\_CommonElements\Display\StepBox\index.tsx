import styles from "@/components/_CommonElements/Display/display.module.scss";
import classNames from "classnames";

interface StepBoxProps {
    className?: string;
    label: string;
    icon: React.ReactNode;
    numbering: number;
    helpText?: React.ReactNode;
}

const StepBox = (props: StepBoxProps) => {
    const {
        className,
        label,
        icon,
        numbering,
        helpText
    } = props;

    return (<>
        <li className={classNames(className, styles.stepBox)}>
            <div className={styles.stepNumberContainer}>
                <div className={styles.stepNumber}>
                    {numbering}
                </div>
            </div>
            <div className={styles.stepIconBox}>{icon}</div>
            <div><div className={styles.stepLabel}>{label}</div>
                {helpText && <div className={styles.helpText}>{helpText}</div>}
            </div>
        </li>

    </>);
};

export default StepBox;