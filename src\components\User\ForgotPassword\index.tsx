import styles from "@/components/User/ForgotPassword/forgotpassword.module.scss";
import Form from "@/components/_CommonElements/Form";
import { FormEvent, useState } from "react";
import {
    FieldContainer,
    TextInput,
    PhoneInput,
} from "@/components/_CommonElements/Input";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import InputValidation from "@/utils/regex/InputValidation";
import { useFormState } from "@/hooks/Utils/FormState";
import { Grid } from "@mui/material";
import { TextButton } from "@/components/_CommonElements/Button";
import { useConfirmForgotPassword } from "@/hooks/Authentication";
import { useRouter } from "next/router";
import RouteMap from "@/constants/config/RouteMap";
import { useSnackbar } from "notistack";


interface ForgotPassword {
    token: string;
    email: string;
}
const ForgotPassword = (props: ForgotPassword) => {
    const { token, email } = props;
    const { t: accountTranslation } = useTranslation(EnumTranslationJson.Account);
    const { t: validatorTranslation } = useTranslation(EnumTranslationJson.Validation);
    const { enqueueSnackbar } = useSnackbar();
    const confirmForgotPassswordAsync = useConfirmForgotPassword();
    const router = useRouter();
    const forgotPasswordInitialState = {
        email: {
            value: email,
            isRequired: true,
            validator: InputValidation.validateEmail,
            errorMessage: {
                empty: validatorTranslation(`email.errorMessage.empty`),
                format: validatorTranslation("email.errorMessage.format"),
            },
        },
        verificationCode: {
            value: token,
            isRequired: true,
            errorMessage: {
                empty: validatorTranslation(`verificationCode.errorMessage.empty`),
                format: validatorTranslation(`verificationCode.errorMessage.format`),
            },
        },
        newPassword: {
            value: "",
            isRequired: true,
            validator: InputValidation.validatePassword,
            errorMessage: {
                empty: validatorTranslation(`password.errorMessage.empty`),
                format: validatorTranslation(`password.errorMessage.format`),
            },
        },
        confirmNewPassword: {
            value: "",
            isRequired: true,
            validator: (): boolean => {
                return newPassword.value === confirmNewPassword.value;
            },
            errorMessage: {
                empty: validatorTranslation(`confirmPassword.errorMessage.empty`),
                format: validatorTranslation(`confirmPassword.errorMessage.format`),
            },
        },
    };
    const forgotPasswordFields = useFormState(forgotPasswordInitialState);
    const { newPassword, confirmNewPassword } =
        forgotPasswordFields.data;

    const onSubmit = async (e: FormEvent<HTMLButtonElement>) => {
        e.preventDefault();
        forgotPasswordFields.set({
            field: "email",
            value: email,
        });
        forgotPasswordFields.set({
            field: "verificationCode",
            value: token,
        });
        if (!forgotPasswordFields.checkIfAllValid()) return;


        const data = forgotPasswordFields.extractStateValueToObject();

        try {
            await confirmForgotPassswordAsync(data);
            router.push(RouteMap.Login);
        } catch (error: any) {
            enqueueSnackbar(error.message || accountTranslation("forgotPassword.tryAgain"));
        }
    };
    return (

        <>
            {(token != "" && email != "") ? <Form.Container size="small">
                <Form.Body rowSpacing={2} columnSpacing={2}>
                    <FieldContainer>
                        <TextInput
                            size="medium"
                            name="email"
                            label={accountTranslation(`email`)}
                            inputType="email"
                            disabled
                            value={email}
                        />
                    </FieldContainer>

                    <FieldContainer >
                        <Grid item xs={12}>
                            <TextInput
                                name="verificationCode"
                                label={accountTranslation(`forgotpasswordText.verifyCode`)}
                                disabled
                                value={token}
                            />
                        </Grid>
                    </FieldContainer>

                    <FieldContainer>
                        <TextInput
                            name="new-password"
                            type="password"
                            label={accountTranslation(`forgotpasswordText.newPassword`)}
                            value={newPassword.value}
                            error={Boolean(
                                newPassword.errorType &&
                                newPassword.errorMessage?.[newPassword.errorType]
                            )}
                            helperText={
                                newPassword.errorType &&
                                newPassword.errorMessage?.[newPassword.errorType]
                            }
                            onChange={(e) => {
                                forgotPasswordFields.set({
                                    field: "newPassword",
                                    value: e.target.value,
                                });
                                confirmNewPassword.isInteracted &&
                                    forgotPasswordFields.set({
                                        field: "confirmNewPassword",
                                        value: confirmNewPassword.value,
                                    });
                            }}
                        />
                    </FieldContainer>

                    <FieldContainer>
                        <TextInput
                            name="corfirm-new-password"
                            type="password"
                            label={accountTranslation(`forgotpasswordText.confirmNewPassword`)}
                            value={confirmNewPassword.value}
                            error={Boolean(
                                confirmNewPassword.errorType &&
                                confirmNewPassword.errorMessage?.[
                                confirmNewPassword.errorType
                                ]
                            )}
                            helperText={
                                confirmNewPassword.errorType &&
                                confirmNewPassword.errorMessage?.[confirmNewPassword.errorType]
                            }
                            onChange={(e) => {
                                forgotPasswordFields.set({
                                    field: "confirmNewPassword",
                                    value: e.target.value,
                                });
                            }}
                        />
                    </FieldContainer>

                    <FieldContainer >
                        <TextButton label={accountTranslation("forgotpasswordText.confirmNewPassword")} onClick={onSubmit} />
                    </FieldContainer>
                </Form.Body>
            </Form.Container> 
            :<></>}

        </>
    );
};

export default ForgotPassword;