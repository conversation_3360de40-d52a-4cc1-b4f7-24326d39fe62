@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;
@use "@/styles/utils/viewport.module.scss" as viewport;
@import "rfs/scss";

.list {
  display: flex;
  flex: {
    grow: 1;
    direction: column;
    wrap: nowrap;
  }
  // padding: 0 5px;
}

div.itemContainer {
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 10px;
  border: map.get(theme.$border, "width") solid map.get(theme.$color, "border");
  border-radius: map.get(theme.$border, "radius");
  margin: 10px 0;
}

.emptyRecord {
  display: flex;
  flex-direction: column;
  justify-content: center !important;
  align-items: center !important;
  text-align: center;
  border: map.get(theme.$border, "width") solid map.get(theme.$color, "border");
  border-radius: map.get(theme.$border, "radius");
  height: 300px;
  margin: 10px 0 20px 0;
}

div.thumbnail {
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;

  & img {
    object-fit: contain;
    max-width: 300px;
    height: auto;
    width: 100%;
  }
}

.itemButtonFullWidth {
  width: 100%;
}

.orderItems {
  width: 100%;
}
.grandTotal {
    & .grandTotalValue {
        border-bottom: 1px solid black;
        position: relative;
        &:after {
            border-bottom: 1px solid black;
            content: '';
            width: 100%;
            position: absolute;
            bottom: -5px;
            left: 0;
        }
    }
}
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
}
.discountCaption {
  display: inline-block;
  padding: 0.2rem 0.8rem;
  background: #fff6ef;
  color: #f0004d;
  border: 1px solid map.get(theme.$color, "primary");
  border-radius: 1.5rem;
}
.itemChipLabel {
  font-size: 0.8rem !important;
  padding: 4px 16px !important;
  margin-right: 0.5rem !important;
}