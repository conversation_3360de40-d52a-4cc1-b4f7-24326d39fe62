import OneTimePaymentOrderItem from "@/models/props/OneTimePaymentOrderItem";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";

export interface OneTimePaymentSliceState {
    items: OneTimePaymentOrderItem[];
}

const initialState: OneTimePaymentSliceState = {
    items: [],
};

export const oneTimePaymentSlice = createSlice({
    name: "oneTimePaymentOrder",
    initialState,
    reducers: {
        addItem:(state, action: PayloadAction<OneTimePaymentOrderItem>) => {
            state.items.push(action.payload);
        },
        clearItems:(state) => {
            state.items = [];
        },
    }
});

export const { addItem, clearItems } = oneTimePaymentSlice.actions;

export default oneTimePaymentSlice.reducer;

export const selectAllItems = (state: RootState) => state.oneTimePaymentOrder.items;