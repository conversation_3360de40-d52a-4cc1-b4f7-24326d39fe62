@use "sass:map";

$color: (
  "background": rgb(255, 255, 255),
  "background-hover": rgba(255, 255, 255, 0.25),
  "primary": #ff7802,
  "secondary": #429b3f,
  "text": rgb(18, 18, 18),
  "highlight-text": rgb(1,175,199),
  "success-text": rgba(248, 255, 136, 1),
  "text-disabled": rgba(255, 255, 255, 0.5),
  "contrast-text": rgba(255, 255, 255, 1),
  "light-border": rgb(220, 220, 220, 0.5),
  "dark-border": rgb(0, 0, 0, 0),
);

$border: (
  "width": 2px,
  "radius": 12px,
);

$presets: (
  "light-border": map.get($border, "width") solid map.get($color, "light-border"),
  "dark-border": map.get($border, "width") solid map.get($color, "dark-border"),
);

$height: (
  "tablet-header": 75px,
  "desktop-header": 80px,
  "footer": 300px,
  "secondary-navigation": 46px,
  "secondary-navigation-offset": 146px
);

$font-family: 'Chocolate Classical Sans', 'Noto Sans TC', Roboto;