/* eslint-disable @next/next/no-img-element */
import { useMemo } from "react";
import styles from "./autoSizedImage.module.scss";
import classNames from "classnames";
import ImageNotSupportedIcon from '@mui/icons-material/ImageNotSupported';

interface AutoSizedImageProps {
    className?: string;
    src?: string;
    alt?: string;
    ratioWxH?: string // w:h
}
const AutoSizedImage = (props: AutoSizedImageProps) => {
    const { className, src, ratioWxH = "16x9", alt = "Image alt" } = props;
    const ratio = useMemo(() => ratioWxH.split("x"), [ratioWxH]);
    return (
        <figure
            className={classNames(className, styles.container)}
            style={{paddingBottom: `${parseFloat(ratio[1]) / parseFloat(ratio[0]) * 100}%`}}
        >

            { /* // eslint-disable-next-line @next/next/no-img-element */ }
            {
                src ? <img alt={alt} src={src} /> :
                <ImageNotSupportedIcon /> 
            }
        </figure>
    );

}

export default AutoSizedImage;