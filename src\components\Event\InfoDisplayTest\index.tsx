import { useMemo } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import classNames from "classnames";
import dayjs from "dayjs";
import { Stack, Icon, Typography, Tooltip, Divider, Skeleton, Paper } from "@mui/material";
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import ReceiptIcon from '@mui/icons-material/Receipt';
import LocalAtmIcon from '@mui/icons-material/LocalAtm';

import { TextButton } from "@/components/_CommonElements/Button";
import Section from "@/components/_CommonElements/Section";
import Label from "@/components/_CommonElements/Label";
import EventBanner from "@/components/Event/InfoDisplay/Banner";
import EventDetails from "@/models/api/models/EventDetails";
import EnumEventPreviewPosition from "@/constants/enum/EventPreviewPosition";
import RouteMap from "@/constants/config/RouteMap";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";


import EventMetadata from "../EventMetadata";
import EventBuyContainer from "../Buy";
import RateCard from "./RateCard";

import styles from "@/components/Event/InfoDisplay/event.module.scss";

interface EventInfoDisplayProps {
    eventDetails: EventDetails;
}
// add session dropdown;
const EventInfoDisplayTest = (props: EventInfoDisplayProps) => {
    const { eventDetails } = props;
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);

    const router = useRouter();

    const isForSale = useMemo(() => eventDetails.eventBundleList.length > 0, [eventDetails.eventBundleList.length]);
    const saleStartDateTime_Unix = useMemo(() => dayjs.utc(eventDetails.saleStartDateTimeForDisplay), [eventDetails.saleStartDateTimeForDisplay]);
    const saleEndDateTime_Unix = useMemo(() => dayjs.utc(eventDetails.saleEndDateTimeForDisplay), [eventDetails.saleEndDateTimeForDisplay]);
    const saleStartDateTime_LocalTime = useMemo(() => saleStartDateTime_Unix.tz(eventDetails.eventTimeZone), [eventDetails.eventTimeZone, saleStartDateTime_Unix]);
    const saleEndDateTime_LocalTime = useMemo(() => saleEndDateTime_Unix.tz(eventDetails.eventTimeZone), [eventDetails.eventTimeZone, saleEndDateTime_Unix]);
    
    const eventStartDateTime_LocalTime = useMemo(() => dayjs.utc(eventDetails.eventStartDateTime).tz(eventDetails.eventTimeZone), [eventDetails.eventStartDateTime, eventDetails.eventTimeZone]);
    const eventEndDateTime_LocalTime = useMemo(() => dayjs.utc(eventDetails.eventEndDateTime).tz(eventDetails.eventTimeZone), [eventDetails.eventEndDateTime, eventDetails.eventTimeZone]);

    const banner = useMemo(() => {
        const banners = eventDetails.previewList.filter((preview) => preview.position === EnumEventPreviewPosition.BANNER);
        return banners.length > 0 ? banners : undefined;
    }, [eventDetails.previewList]);

    return (
        <>
            <Stack direction={"column"} gap={2}>
                <Section label={eventDetails.eventName} labelSize="xlarge" className={{ label: styles.eventTitle }} />
                <Stack direction={"row"} gap={2} useFlexGap>
                    {banner && <EventBanner items={banner} />}
                    <Stack direction={"column"} flex={1} flexGrow={1}>
                        <Paper elevation={0} className={classNames(styles.field, styles.border)}>
                            <Paper elevation={0} className={styles.basicInfoTable}>
                                <EventMetadata
                                    icon={
                                        <Tooltip title={eventTranslation("detail.event")}>
                                            <CalendarMonthIcon fontSize="large" />
                                        </Tooltip>
                                    }
                                    content={
                                        <Stack direction={"column"}>
                                            <Typography>{eventStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")}</Typography>
                                            <Typography> ~ </Typography>
                                            <Typography>{eventEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")}</Typography>
                                        </Stack>
                                    }
                                />
                                <Divider />
                                <EventMetadata
                                    icon={
                                        <Tooltip title={eventTranslation("detail.salesDate")}>
                                            <ReceiptIcon fontSize="large" />
                                        </Tooltip>
                                    }
                                    content={
                                        <Stack direction={"column"}>
                                            <Typography>{saleStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")}</Typography>
                                            <Typography> ~ </Typography>
                                            <Typography>{saleEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")}</Typography>
                                        </Stack>
                                    }
                                />
                                { isForSale &&
                                    <>
                                        <Divider />
                                        <Stack direction={"row"} useFlexGap flexWrap={"wrap"}>
                                            <LocalAtmIcon fontSize="large" />
                                            <div className={styles.rateCard}>{eventTranslation("detail.rateCardLabel")}</div>
                                            <RateCard dataSource={eventDetails.eventBundleList} />
                                        </Stack>
                                    </>
                                }
                            </Paper>
                        </Paper>
                        <div className={styles.toBuy}>
                            <TextButton
                                    size="extra"
                                    onClick={()=>{window.location.href="#buy";}}
                                    variant="contained"
                                >
                                <Icon baseClassName="fa" className="fa-ticket-simple" />
                                &nbsp; {eventTranslation("detail.buy")}
                            </TextButton>
                        </div>
                    </Stack>
                </Stack>
            </Stack>

            <Section
                labelSize="large"
                content={eventDetails.eventDescription}
            />
            <Section
                labelSize="large"
                label={eventTranslation("detail.buy")}
                id={'buy'}
                content={
                    <EventBuyContainer eventDetails={eventDetails} />
                }
            />
            <button
                className={styles.back}
                onClick={() => {
                    void router.push(RouteMap.Main);
                }}
            >
                <Icon baseClassName="fa" className="fa-chevron-left" />
                {" "}
                {eventTranslation("detail.back")}
            </button>
        </>
    );
};
export default EventInfoDisplayTest;
