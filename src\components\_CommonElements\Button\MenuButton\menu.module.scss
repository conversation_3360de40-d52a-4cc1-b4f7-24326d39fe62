@use "sass:map";

@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/theme.module.scss" as theme;

$primary-color: map.get(theme.$color, "primary");
$secondary-color: map.get(theme.$color, "secondary");
$background-color: map.get(theme.$color, "background");

$border-color: "white";
$border-width: map.get(theme.$border, "width");
$border-radius: map.get(theme.$border, "radius");

$text-color: map.get(theme.$color, "text");
$text-color-contrast: map.get(theme.$color, "contrast-text");
$text-color-disabled: map.get(theme.$color, "text-disabled");

.wrapper {
  position: relative;

  & .menu {
    position: absolute;
    top: 100%;
    right: 0;
    color: $text-color;
    background-color: $background-color;
    padding: 5px 0;
    border: {
      color: $border-color;
      width: $border-width;
      radius: $border-radius;
      style: solid;
    }
  }

  & .list {
    padding: 0;
  }

  & .item {
    &:hover {
      color: $primary-color;
    }
  }
}
