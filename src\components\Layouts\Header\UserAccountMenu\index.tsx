import MenuButton from "@/components/_CommonElements/Button/MenuButton";
import { BACKEND } from "@/constants/config";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import ProfileSectionArray from "@/constants/navigation/profileSection";
import AuthenicationHooks from "@/hooks/Authentication";
import ENDPOINT from "@/models/api/endpoint";
import LogoutAPIResult from "@/models/api/result/user/logout";
import { clearAll } from "@/redux/slices/identitySlice";
import { ModalActions } from "@/redux/slices/uiSlice";
import { clearUserInfo } from "@/redux/slices/userInfoSlice";
import { dispatch, useSelector } from "@/redux/store";
import LogoutIcon from "@mui/icons-material/Logout";
import { MenuItemProps, Typography } from "@mui/material";
import Icon from "@mui/material/Icon";
import ListItemIcon from "@mui/material/ListItemIcon";
import classNames from "classnames";
import { deleteCookie } from "cookies-next";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useSnackbar } from "notistack";
import { useCallback, useMemo } from "react";
import styles from "./userAccountMenu.module.scss";

const UseLogout = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async () => {
        return await fetchAsync<LogoutAPIResult>(
            ENDPOINT.Logout(),
            {
                method: "POST"
            }
        );
    };
    return requestAsync;
}
const UserAccountMenu = () => {
    const { enqueueSnackbar } = useSnackbar();
    const router = useRouter();
    const { isAuthorized } = AuthenicationHooks.useUserInfo();
    const isMembership = useSelector(state => state.user.isMembership);
    const { t: navigationTranslation } = useTranslation(EnumTranslationJson.Navigation);
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    
    const changeTab = useCallback((index: number) => {
        dispatch(ModalActions.openLoginModal({tabIndex: index}));
    }, []);
    const changeLoginTab = useCallback(() => {
        changeTab(0);
    }, [changeTab]);
    const changeRegisterTab = useCallback(() => {
        changeTab(1);
    }, [changeTab]);
    const logoutAsync = UseLogout();
    const logout = useCallback(async () => {
        // Always logout user even the APi is failed
        // Otherwise we would probably exposing user's secret when they dont know they failed to logout.
        dispatch(clearAll());
        dispatch(clearUserInfo());
        Object.values(EnumCookieKey).forEach((values) => {
            deleteCookie(values);
        });
        router.reload();

        try {
            await logoutAsync();
        } catch {
        }
        enqueueSnackbar(snackbarTranslation("messages.logout.success"), { variant: "success" });
    }, [router, logoutAsync, enqueueSnackbar, snackbarTranslation]);

    const onLogoutButtonClick = useCallback(() => {
        void logout();
    }, [logout]);

    const onPathClick = useCallback((path?: string) => {
        void router.push(path || "");
    }, [router]);

    const LoginRegisterButton = () => {
        return (
            <>
                <button
                    className={classNames(styles.outlined)}
                    onClick={changeLoginTab}
                >
                    <span>{navigationTranslation("main.login")}</span>
                </button>
                <button
                    className={classNames(styles.contained)}
                    onClick={changeRegisterTab}
                >
                    <span>{navigationTranslation("main.register")}</span>
                </button>
            </>
        );
    };

    const MemberInfoItems: MenuItemProps[] = useMemo(() => {
        const profileSections = new ProfileSectionArray(router.pathname);
        const items = profileSections.getItems()
        return [
            ...items.reduce((allowedMenu, item, i) => {
                if (item.visible) {
                    const requiredMembership = item?.requiredMembership ?? item.requiredMembership;
                    if (requiredMembership) {
                        if (isMembership) {
                            allowedMenu.push({
                                onClick: () => onPathClick(item.path),
                                children: profileTranslation(item.displayName),
                                divider: i === items.length - 1,
                                selected: item.path === router.pathname
                            });
                        }
                    } else {
                        allowedMenu.push({
                            onClick: () => onPathClick(item.path),
                            children: profileTranslation(item.displayName),
                            divider: i === items.length - 1,
                            selected: item.path === router.pathname
                        });
                    }
                }
                return allowedMenu;
            }, [] as MenuItemProps[]),
            {
                onClick: onLogoutButtonClick,
                children: (
                    <>
                        <ListItemIcon>
                            <LogoutIcon />
                        </ListItemIcon>
                        <Typography variant="inherit">{navigationTranslation("main.logout")}</Typography>
                    </>
                )
            }
        ];
    }, [navigationTranslation, onLogoutButtonClick, onPathClick, profileTranslation, router.pathname]);

    return (
        <div className={styles.container}>
            {isAuthorized ?
                <MenuButton
                    className={styles.userDropdown}
                    variant="contained"
                    size="medium"
                    startIcon={<Icon baseClassName="fa" className="fa-user" />}
                    label=" "
                    endIcon={<Icon baseClassName="fa" className="fa-caret-down" />}
                    dataSource={MemberInfoItems}
                />
             : <LoginRegisterButton />}
        </div>
    );
};

export default UserAccountMenu;
