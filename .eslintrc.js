module.exports = {
    root: true,
    parser: "@typescript-eslint/parser",
    plugins: [
        "import",
        "@typescript-eslint",
        "@stoneleigh"
    ],
    extends: [
        "next/core-web-vitals",
        "eslint:recommended",
        "plugin:eslint-plugin/recommended",
        "plugin:@typescript-eslint/recommended-type-checked",
        "plugin:@stoneleigh/recommended"
    ],
    parserOptions: {
        project: true,
        tsconfigRootDir: __dirname,
    },
    overrides: [
        {
          files: ['*.js'],
          extends: ['plugin:@typescript-eslint/disable-type-checked'],
        },
    ],
    rules: {
        "import/order": [
            "error",
            {
                groups: [
                    "builtin",
                    "external",
                    "internal",
                    "parent",
                    "sibling",
                    "index",
                    "object"
                ]
            }
        ]
    }
}