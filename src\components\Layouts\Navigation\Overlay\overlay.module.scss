@use "sass:map";

@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/theme.module.scss" as theme;

$desktop-header-height: map.get(theme.$height, "desktop-header");
$tablet-header-height: map.get(theme.$height, "tablet-header");

.divRef {
  display: contents;
}

.overlay {
  position: fixed;
  top: calc($desktop-header-height - 1px);
  height: calc(100% - $desktop-header-height);
  left: 0;
  right: 0;
  background-color: white;
  display: flex;
  flex-direction: column;
  z-index: 160;
  overflow-y: hidden;
  padding: 30px 0;
  justify-content: center;

  & > * {
    padding: 0 clamp(20px, 5%, 100px);
  }

  @include viewport.within("tablet") {
    height: calc(100% - $tablet-header-height);
  }
}
