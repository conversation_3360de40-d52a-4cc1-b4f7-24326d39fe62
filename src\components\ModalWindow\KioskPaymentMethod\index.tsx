import Modal from "@/components/_CommonElements/Modal";

import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useCallback, useMemo, useState } from "react";
import { dispatch, useSelector } from "@/redux/store";
import { ModalActions } from "@/redux/slices/uiSlice";
import { 
    Box, 
    Card, 
    CardActionArea, 
    CardContent, 
    CardHeader, 
    Dialog, 
    DialogActions, 
    DialogContent, 
    DialogContentText, 
    DialogTitle, 
    Icon, 
    IconButton, 
    Button,
    Stack, 
    Typography 
} from "@mui/material";

import styles from "./kioskPaymentMethod.module.scss";
import { enqueueSnackbar } from "notistack";
import { clearCart } from "@/redux/slices/cartSlice";
import { clearEmail } from "@/redux/slices/kioskSlice";
import Grid from "@mui/material/Unstable_Grid2";

interface PaymentResponse {
    status: boolean;
    message: string | undefined;
}

const ModalKioskPaymentMethodWindow = () => {
    const kioskPaymentMethodModalState = useSelector((state) => state.ui.modal.kioskPaymentMethod);
    const customerEmailAddr = useSelector(state => state.kiosk.email);
    const cartItems = useSelector(state => state.cart.items);
    const eventId = useSelector(state => state.cart.eventId);
    const eventName = useSelector(state => state.cart.eventName);
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const { t: kioskTranslation } = useTranslation(EnumTranslationJson.Kiosk);

    const [messageDialogIsOpen, setMessageDialogIsOpen] = useState<boolean>(false);
    const [messageDialogContent, setMessageDialogContent] = useState<string>("");

    const paymentMethods = useMemo(() => [
        {
            "name": "creditCard",
            "description": "VISA/MASTER/UnionPay",
            "value": "card",
            "icons": [
                "https://assets.incutix.com/icons/payment/payment_icon_visa_lg.png",
                "https://assets.incutix.com/icons/payment/payment_icon_mastercard_lg.png",
                "https://assets.incutix.com/icons/payment/payment_icon_unionpay_lg.png",
            ],
        },
        {
            "name": "alipay",
            "description": "",
            "value": "alipay",
            "icons": [
                "https://assets.incutix.com/icons/payment/payment_icon_alipay_lg.png",
            ],
        },
        {
            "name": "wechatPay",
            "description": "",
            "value": "wechatpay",
            "icons": [
                "https://assets.incutix.com/icons/payment/payment_icon_wechatpay_lg.png",
            ],
        },
        {
            "name": "fps",
            "description": "",
            "value": "fps",
            "icons": [
                "https://assets.incutix.com/icons/payment/payment_icon_fps_lg.png",
            ],
        },
    ], []);

    const handleMessageDialogClose = useCallback(() => setMessageDialogIsOpen(false), []);

    const onClose = useCallback(() => {
        dispatch(clearCart());
        dispatch(ModalActions.closeKioskPaymentMethodModal());
    }, []);

    const onPurchareTickets = useCallback((paymentMethod: string) => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
        const webviewCallHandler = window.flutter_inappwebview.callHandler;
        if ((webviewCallHandler != null) && typeof window !== "undefined") {
            const flattenedEventSessionBundles: unknown[] = cartItems.flatMap((item) => {
                const flattenedByQuantity: unknown[] = [];
                for (let i = item.quantity; i > 0; i--) {
                    flattenedByQuantity.push({
                        eventId: eventId,
                        eventName: eventName,
                        eventSessionBundleId: item.eventSessionBundleId,
                        eventSessionBundleName: item.eventBundleName,
                        price: item.price,
                        currency: item.currency,
                        sessionStartDateTime: item.sessionStartDateTime,
                        sessionEndDateTime: item.sessionEndDateTime
                    });
                }
                return flattenedByQuantity;
            });
            const args = [{
                'email': customerEmailAddr,
                'paymentMethod': paymentMethod,
                'items': flattenedEventSessionBundles
            }];
            try {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
                webviewCallHandler('onPaymentResponse', ...args)
                .then((result: PaymentResponse) => {
                    if (result.status) {
                        dispatch(clearEmail());
                    } else {
                        if (result.message) {
                            console.log(result?.message);
                            // setMessageDialogContent(kioskTranslation(result.message));
                            // setMessageDialogIsOpen(true);
                            void webviewCallHandler('showDialog', ...[kioskTranslation("dialog.title"), kioskTranslation(result.message), true]);
                        }
                        enqueueSnackbar(kioskTranslation("errorMessages.webviewConnectKioskFail"), { variant: "error" });
                    }
                })
                .catch((e: unknown) => {
                    console.log(e);
                })
                .finally(() => onClose());
            } catch(error) {
                console.log(error);
                enqueueSnackbar(kioskTranslation("errorMessages.webviewConnectKioskFail"), { variant: "error" });
            }
        } else {
            enqueueSnackbar(kioskTranslation("errorMessages.webviewConnectKioskFail"), { variant: "error" });
        }
    }, [cartItems, customerEmailAddr, eventId, eventName, kioskTranslation, onClose]);

    return (
        <>
            <Modal
                onCancel={onClose}
                onBackdropClick={() => {}}
                hideConfirmButton
                hideCloseButton
                visible={kioskPaymentMethodModalState.visible}
                classes={{
                    paperContainer: styles.kioskModalPaper
                }}
            >
                <Box sx={{ flexGrow: 1, maxWidth: '90vmax' }}>
                    <Typography gutterBottom variant="h5">
                        {modalTranslation(`modals.kioskPaymentMethod.title`)}
                    </Typography>
                    <Grid container rowSpacing={2} columnSpacing={{ xs: 1, sm: 2, md: 3 }} margin={0}>
                        {paymentMethods.map((paymentMethod) => 
                            <Grid xs sm={4} md={6} key={`grid-payment-method-${paymentMethod.value}`}>
                                <Card 
                                    elevation={3} 
                                    key={`card-payment-method-${paymentMethod.value}`} 
                                    className={styles.paymentCard}
                                >
                                    <CardActionArea onClick={() => onPurchareTickets(paymentMethod.value)}>
                                        <CardHeader
                                            title={modalTranslation(`modals.kioskPaymentMethod.methods.${paymentMethod.name}`)}
                                        />
                                        <CardContent>
                                            <IconButton className={styles.iconButtonScaleLarger}>
                                                <Stack direction="row" spacing={1}>
                                                    {paymentMethod.icons?.map((iconUrl, index) => 
                                                        <Icon classes={{root: styles.iconRoot}} key={`payment-method-${paymentMethod.value}-icon-${index}`}>
                                                            <img className={styles.imageIcon} src={iconUrl} />
                                                        </Icon>
                                                    )}
                                                </Stack>
                                            </IconButton>
                                        </CardContent>
                                    </CardActionArea>
                                </Card>
                            </Grid>
                        )}
                    </Grid>
                </Box>
            </Modal>
            <Dialog
                open={messageDialogIsOpen}
                onClose={handleMessageDialogClose}
            >
                <DialogTitle>{kioskTranslation("dialog.title")}</DialogTitle>
                <DialogContent>
                    <DialogContentText>{messageDialogContent}</DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleMessageDialogClose}>{kioskTranslation("dialog.button.close")}</Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default ModalKioskPaymentMethodWindow;