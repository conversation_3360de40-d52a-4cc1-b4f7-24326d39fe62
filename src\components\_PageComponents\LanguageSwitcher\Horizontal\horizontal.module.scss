@use "@/styles/utils/theme.module.scss"as theme;
@use "@/styles/utils/viewport.module.scss"as viewport;

div.horizontal {
  padding: 0;
  user-select: none;
  display: flex;

  // @include viewport.within("navigation") {
  //   display: none;
  // }

  & div.select {
    padding: 0 0 0 5px;
    font-size: 16px;

    & fieldset[class~="MuiOutlinedInput-notchedOutline"] {
      border-color: white !important;
    }

    &:hover {
      background: unset;
    }

    &>div {
      padding: 0;
    }
  }
}
