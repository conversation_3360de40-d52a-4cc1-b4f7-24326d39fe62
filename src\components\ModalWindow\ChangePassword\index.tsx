import { useTranslation } from "next-i18next";
import Modal from "@/components/_CommonElements/Modal";
import ChangePasswordForm, { ChangePasswordFormRef } from '@/components/User/ChangePassword/Form';
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useCallback, useRef } from "react";
interface ModalChangePasswordWindowProps {
    visible: boolean;
    onClose: () => void;
}

const ModalChangePasswordWindow = (props: ModalChangePasswordWindowProps) => {
    const { visible, onClose } = props;
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const formRef = useRef<ChangePasswordFormRef>(null);
    const onSubmit = useCallback(() => {
        void (async () => {
            if (!formRef.current) {
                return;
            }
            await formRef.current.onSubmit();
        })();
    }, []);

    return (<>
        <Modal
            visible={visible}
            title={modalTranslation("modals.changePassword.title")}
            onConfirm={onSubmit}
            onCancel={onClose}
            hideConfirmButton
            hideCancelButton
        >
            <ChangePasswordForm ref={formRef} onCompleted={onClose} />
        </Modal>
    </>);
};

export default ModalChangePasswordWindow;