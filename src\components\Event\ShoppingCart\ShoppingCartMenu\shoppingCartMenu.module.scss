@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;
@use "@/styles/utils/viewport.module.scss" as viewport;
@import "rfs/scss";

$primary-color: map.get(theme.$color, "primary");
$secondary-color: map.get(theme.$color, "secondary");
$border-radius: map.get(theme.$border, "radius");
$text-color: map.get(theme.$color, "text");
$text-color-contrast: map.get(theme.$color, "contrast-text");

.shoppingCartPopper {
    & .shoppingCartPopperContainer {
        border-radius: 8px;
        overflow: hidden;
    }
}

.cartItemList {
    /*box-shadow: inset 0px -5px 5px -3px rgba(0,0,0,0.14);*/
    max-height: fit-content;
    overflow-y: auto;
}

.cartPromoteAndOverview {
    box-shadow: inset 0px -5px 5px -3px rgba(0,0,0,0.14);
    max-height: 45vh;
    overflow-y: auto;
}

.itemCardContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;

    &:nth-child(2n-1) {
        background-color: #fbfbfb;
    }

    & .itemNameContainer {
        display: flex;
        flex-direction: row;
        flex: 1;
        flex-basis: 75%;
    }

    & .itemQuantityContainer {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        flex-basis: 25%;
    }

    &.outOfStock {
        background-color: #d3d3d3;
    }
}

.confirmClaim {
    font-weight: bold;
    margin: 1rem auto;
    padding: 2px 16px;
    color: red;
    user-select: none;
}

.totalPrice {
    width: 100%;
    text-align: left;
    font-size: 28px;
    
    & span {
        margin-right: 20px;
        padding-right: 10px;
        font-size: 16px;
        border-right: 3px solid map.get(theme.$color, "secondary");
    }
}
.discountPrice {
    width: 100%;
    text-align: right;
    font-size: 14px;
    
    & span {
        margin-right: 20px;
        padding-right: 10px;
        font-size: 12px;
        border-right: 3px solid map.get(theme.$color, "secondary");
    }

    & .appliedClip {
        & span {
            border: 0 none;
            font-weight: 800;
            color: #f0004d;
        }
    }
}
.quantity {
    font-size: 1.1rem;
    color: rgb(87, 87, 87) !important;
}
.discountCaption {
    display: inline-block;
    padding: 0.2rem 0.8rem;
    background: #fff6ef;
    color: #f0004d;
    border: 1px solid map.get(theme.$color, "primary");
    border-radius: 1.5rem;
}

.paymenttitleContainer {
    display: flex;
    flex-direction: column;
    padding: 1rem 0;
    gap: 20px !important;

    .group {
        display: flex;
        flex-direction: column;
        padding: 1.25rem;
        border-radius: 5px;
        box-shadow: 1px 2px 5px 1px #F0F0F0;
        gap: 15px;
        width: 100%;

        @include viewport.within("mobile") {
            padding: 0;
            box-shadow: none;
        }

        &.noElevation {
            box-shadow: none;
        }

        & .applyDiscountCodeButton {
            width: auto;
            text-wrap: nowrap;
        }
        h2 {

        }
        & .highlight {
            color: red;
        }

        .row {
            display: flex;
            align-items: center;
            &.select {
                // display: grid;
                // grid-template-columns: 50% 50%;
            }

            .col {
                display: initial;
                padding: 0.25rem 0 0 0.75rem;
                flex-grow: 1;
                width: 50%;

                .radio {
                    padding: 0.25rem 0;
                    border: 1px solid #B2BCCA;
                    display: flex;
                    border-radius: 5px;
                    gap: 0;
                    &.active {
                        background-color: #E8EFFA;
                        border-color: #1660CF;
                    }
                    &.disabled {
                        pointer-events: none;
                        opacity: 0.5;
                    }

                    &+.radio {
                        margin-top: 1rem;

                    }
                }

                .labelContainer {
                    display: flex;
                    padding: 9px;
                    width: 100%;

                    &.shipment {
                        .price {
                            padding-right: 2rem;
                            max-width: 70px;
                            width: 100%;
                        }

                        .content {
                            font-size: 13px;
                        }

                        .logo {
                            margin-left: auto;
                            padding-right: 1rem;
                            color: rgb(79, 79, 79);

                            svg {
                                font-size: 2.5rem;
                            }
                        }
                    }

                    &.payment {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        gap: 10px;
                        @include viewport.within("tablet") {
                            flex-direction: column;
                        }
                        .content {
                            font-weight: bolder;
                            .note {
                                font-weight: normal;
                                font-size: 13px;
                            }
                        }

                        .logo {
                            display: flex;
                            gap: 5px;
                            img {
                                max-width: 50px;
                                max-height: 35px;
                                height: 1.5rem;
                            }


                        }
                    }
                }
            }
        }

        .overView {
            padding: 0;

            h2 {
                margin: 0;
            }

            div[class~="MuiAccordionSummary-content"] {
                margin: 0;
            }
        }

        .details {
            padding: 0;
            &.disabled {
                pointer-events: none;
                opacity: 0.5;
            }
            .itemWrapper {  
                display: flex;
                flex-direction: column;
                gap: 5px;
                hr{
                    margin: 0.5rem 0 ;
                }
                .item {
                    display: flex;
                    gap: 15px;
                    .image {
                        img {
                            border-radius: 10px;
                        }
                    }
                    .content {
                        width: 100%;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        
                        .lower {
                            display: flex;
                            justify-content: space-between;
                            .qtyBox {
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                gap: 10px;
                                & .boxContainer {
                                    display: flex;
                                    flex-direction: row;
                                    gap: 0;
                                    & .box {
                                        width: 30px;
                                        height: 30px;
                                        border: 1px solid gray;
                                        text-align: center;
                                        font-size: 20px;
                                        display: flex;
                                        justify-content: center;
                                        border-radius: 5px;
                                        cursor: pointer;
                                        user-select: none;
                                        &.noBorder{
                                            border: none;
                                            height: 28px;
                                        }
                                    }
                                }
                                & .box {
                                    width: 30px;
                                    height: 30px;
                                    border: 1px solid gray;
                                    text-align: center;
                                    font-size: 20px;
                                    display: flex;
                                    justify-content: center;
                                    border-radius: 5px;
                                    cursor: pointer;
                                    &.noBorder{
                                        border: none;
                                        height: 28px;
                                    }
                                }
                                & .unitPrice {
                                    color: rgb(150, 150, 150);
                                }
                            }
                        }
                    }
                    & .actionContainer {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        align-items: flex-end;
                        width: 200px;
                        .price {
                            font-size: 1.1rem;
                            padding-top: 10px;
                        }
                    }
                }

                .billRow {
                    padding: 0.25rem 0 ;
                    display: flex;
                    font-size: 1.1rem;
                    justify-content: space-between;
                    & .label {

                    }
                    & .value {
                        font-weight: bold;
                        font-size: 1.3rem;
                    }
                    & .loading {
                        margin: 0 auto;
                    }
                    & .confirmButton {
                        width: 100%;
                    }
                }
            }
        }
    }
}

.eventTitle {
    border-left-color: #ea7a3e !important;
}