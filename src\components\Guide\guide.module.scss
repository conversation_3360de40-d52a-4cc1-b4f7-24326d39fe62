@use 'sass:map';
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/animations.module.scss" as animations;

$background-color: map.get(theme.$color, "background");
$tablet-header-height: map.get(theme.$height, "tablet-header");
$desktop-header-height: map.get(theme.$height, "desktop-header");
$secondary-navigation-offset: map.get(theme.$height, "secondary-navigation-offset");

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    gap: 20px;

    & .contents {
        display: flex;
        flex-direction: column;
        gap: inherit;
    }
}

.flowContainer {
    margin: 0 24px;
    display: flex;
    justify-content: center;
    padding: 16px;
    margin: 0;
    list-style: none;
    -webkit-flex-direction: row;
    /* Safari */
    flex-direction: row;
    flex-wrap: wrap;

    li {
        position: relative;
        flex: 1;
        display: inline;

        &:not(:first-child):before {
            content: '\00BB';
            color: map.get(theme.$color, "primary");
            position: absolute;
            top: 35px;
            left: -25px;
            font-size: 100px;
            z-index: 1;
            text-shadow: -5px 0 0 $background-color, 0 -5px 0 $background-color, 5px 0 0 $background-color, 0 5px 0 $background-color;

            @include viewport.within("tablet") {
                content: none;
            }
        }
    }

    @include viewport.within("tablet") {
        flex-direction: column;
        justify-content: center;
    }

}

.chipLabel {
    margin-bottom: 0;
}

.accordion {
    border-bottom: 0;
    box-shadow: none !important;

    & ::before {
        background-color: initial;
    }

    div[role=button] {
        max-width: 400px;
        margin: auto;
        background-color: map.get(theme.$color, "secondary");
        border-radius: 50px;
        padding: 0 36px;
        letter-spacing: 3px;

        @include viewport.within("tablet") {
            padding: 0 16px;

            font: {
                size: 1rem;
            }
        }

        &>div[class~=MuiAccordionSummary-content] {
            text-align: center;
            display: inline;
        }
    }
}

.guideSection {
    width: 100%;
}