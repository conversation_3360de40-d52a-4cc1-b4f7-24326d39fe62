import { Enum<PERSON><PERSON>ie<PERSON><PERSON> } from "@/constants/enum/Cookies";
import { APIErrorResult } from "@stoneleigh/api-lib";
import { AxiosError } from "axios";
import { getCookie, getCookies, setCookie } from "cookies-next";
import type { GetServerSidePropsContext } from "next";
class API {
    private static ServerSideCookies: { [key in string]: string };
    static GetServerSideCookies = (context: GetServerSidePropsContext) => {
        const cookie = getCookies({ req: context.req, res: context.res });
        return cookie;
    }
    static GetCookie = (key: string) => {
        if (typeof window === 'undefined') {
            return this.ServerSideCookies[key];
        }
        return getCookie(key) as string;
    }
    static GetUserJWT = () => {
        const cookie = this.GetCookie(EnumCookieKey.USER_JWT) as unknown;
        return cookie ? cookie as string : undefined;
    }
    static SetServerSideCookies = (ServerSideCookies: { [key in string]: string }) => {
        this.ServerSideCookies = ServerSideCookies;
    }
    static SetCookie = (key: string, value: string) => {
        if (typeof window === 'undefined') {
            return API.SetServerSideCookies({ ...this.ServerSideCookies, [key]: value });
        }
        setCookie(key, value);
    }
    static ToFormData(obj: {[key: string]: unknown}) {
        const result = new FormData();
        for (const key in obj) {
            if (obj[key]) {
              const value = String(obj[key]);
              result.append(key, value);
            }
          }
        return result;
    }
    static GetErrorMessage = (error: unknown): string => {
        const apiError = this.GetAPIErrorResult(error);
        console.warn(apiError);
        if (apiError) {
            const ErrorCode = apiError.error.code;
            return `${ErrorCode ? `[Code: ${ErrorCode}] `: ""}${Object.keys(apiError.error).filter(key => key !== "code").map(key => apiError.error[key]).join("\n")}`;
        }
        return JSON.stringify(error);
    };
    private static GetAPIErrorResult(error: unknown) {
        try {
            console.warn(error);
            return (error as AxiosError<APIErrorResult>)!.response?.data;
        }
        catch {
            return undefined;
        }
    }
}
export default API;