buttons:
  submit: Submit
  confirm: Confirm
  cancel: Cancel
  close: Close
resendAfter: "{{SEC}} seconds"
obtainCode: "Obtain"
modals:
  login:
    content:
      welcomeBackMessage: Welcome Back, {{USERNAME}} !
  emailVerification:
    title: Email Verification
    content:
      codeLabel: Verification Code
      codeEmailSent: Verification Code is sending to your email inbox
      reminder: |
        Please click "Obtain" to receive the verification code, and check your registered email address for the verification email. Please enter 6-digit code to “Confirm”. Or you can perform email verification under the "Account Information" section. 
        (Note: If you don't receive the verification code email, please check your spam folder.)
  forgotPassword:
    title: Forgot Password
  changePassword:
    title: Change Password
    content:
      oldPasswordLabel: Old Password
      newPasswordLabel: New Password
      confirmNewPasswordLabel: Confirm New Password
  admissionQrcode:
    title: Admission
    content:
      reminder: Please show QR Code to the staff.
  clearShoppingCart_userrequest:
    title: Caution! You are clearing items in cart
    content: After this, any items in shopping cart will be removed and you will no longer to restore.
  clearShoppingCart_systemrequest:
    title: Caution! Only items from the same event can be purchased in a single order.
    content: If you add an item from this event, the items from the previous event will be removed from your shopping cart.
  promoCode:
    title: Apply Promo Code
    content:
      promoCode: Promo Code
      helper: (If any) Please enter the code
      promoCodeApply: Apply
      validation:
        missingCode: Please fill in the code before apply
  kioskPaymentMethod:
    title: Choose Payment Method
    methods:
      creditCard: Credit Card (Visa/Mastercard/UnionPay)
      alipay: Alipay
      wechatPay: WeChat Pay
      fps: FPS
  membershipReserveConfirm:
    title: Confirm Reservation
    content: You chose {{SELECTED_DATE}}
    apiResponse:
      success: Congratulations! You have reserved the day.
  membershipReserveCancel:
    title: Confirm Cancel Reservation
    apiResponse:
      success: You have cancelled the reservation.
