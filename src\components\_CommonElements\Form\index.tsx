import styles from "@/components/_CommonElements/Form/form.module.scss";
import classNames from "classnames";

import Grid from '@mui/material/Grid';
import React from "react";

interface FormContainerProps {
  size?: "small" | "medium" | "large";
  children?: any;
}
interface FormTitleProps {
    children?: any;
    noFormHead?: boolean;
}
interface FormBodyProps {
    flexDirection?: "row" | "column";
    columnSpacing?: number;
    rowSpacing?: number;
    spacing?: number;
    children?: any;
    className?: string;
    onEnter?(): void;
}
const FormContainer = React.memo((props: FormContainerProps) => {
  const {
    size,
    children,
  } = props;

  return (
    <div className={classNames(styles.container, size && styles[size])}>
      {children}
    </div>
  );
});
FormContainer.displayName = "FormContainer";

const FormTitle = React.memo((props: FormTitleProps) => {
  const {
    children,
    noFormHead,
  } = props;

  return (<>
    <div className={classNames(styles.title, noFormHead && styles.hidden)}>
      <span>{children}</span>
    </div>
  </>);
});
FormTitle.displayName = "FormTitle";

const FormBody = React.memo((props: FormBodyProps) => {
  const {
    flexDirection,
    columnSpacing,
    rowSpacing,
    spacing,
    children,
    onEnter,
    className,
  } = props;

  const handleEnter = (e: any) => {
    if (e.code === "Enter" || e.code === "NumpadEnter") {
      onEnter && onEnter();
    };
  };

  return (<>
    <form className={classNames(styles.body)} onKeyDown={handleEnter}>
      <Grid
        container
        direction={flexDirection ?? "row"}
        columnSpacing={columnSpacing}
        rowSpacing={rowSpacing}
        spacing={spacing}
        {...(className ? {className} : {} )}
      >
        {children}
      </Grid>
    </form>
  </>);
});
FormBody.displayName = "FormBody";

const Form = {
    Container: FormContainer,
    Title: FormTitle,
    Body: FormBody
}
export default Form;