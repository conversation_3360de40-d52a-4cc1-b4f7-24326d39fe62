@use 'sass:map';

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

.container {
  color: map.get(theme.$color, "text");
  margin: {
    right: auto;
    left: auto;
  };
  min-width: 280px;
};

.title {
  font: {
    size: 24px;
    weight: bold;
  };
  padding: 5px 15px;

  &.hidden {
    display: none;
  };

  & span {
    display: inline-block;
    position: relative;
    padding: 0 5px;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      width: 55%;
      border-bottom: 3px solid #{map.get(theme.$color, "primary")};
    };
  
    &::after {
      content: "";
      position: absolute;
      top: 0;
      right: -20%;
      bottom: 0;
      width: 65%;
      border-bottom: 3px solid #{map.get(theme.$color, "secondary")};
    };
  };
};

.body {
  padding: 30px;
  @include viewport.within("mobile") {
    padding: 30px 0;
  };
};

.narrow {
  max-width: min(400px, 90vw);
};

.medium {
  max-width: min(600px, 90vw);
};

.wide {
  max-width: min(800px, 90vw);
};