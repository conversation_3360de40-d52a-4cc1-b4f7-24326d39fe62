import classNames from "classnames";
import type { SelectChangeEvent, SelectProps } from "@mui/material/Select";
import InputLabel from '@mui/material/InputLabel';
import FormHelperText from '@mui/material/FormHelperText';
import FormControl from '@mui/material/FormControl';
import MUISelect from '@mui/material/Select';
import { ReactNode } from "react";
import styles from "./box.module.scss";

type SelectBoxProps = SelectProps & {
    id?: string;
    className?: string;
    label?: string;
    labelId?: string;
    children?: ReactNode;
    defaultValue?: string;
    error?: boolean;
    fullWidth?: boolean;
    helperText?: string;
    onChange?: (params: SelectChangeEvent) => void;
    value?: string;
    variant?: "outlined" | "filled" | "standard";
    size?: "small" | "medium";
    boxStyle?: { [key in string]: string };
    gridStyle?: { [key in string]: string };
    width?: string;
    minWidth?: string;
    maxWidth?: string;
    gridWidth?: string;
    gridMinWidth?: string;
    gridMaxWidth?: string;
    disabled?: boolean;
    menuProps?: { [key: string]: any };
}

const Box = (props: SelectBoxProps) => {
    const {
        id,
        className,
        label,
        labelId,
        children,
        defaultValue,
        fullWidth,
        error,
        helperText,
        onChange,
        value,
        variant,
        size,
        boxStyle,
        width,
        minWidth,
        maxWidth,
        disabled,
        menuProps,
        IconComponent
    } = props;

    return (<>
        <FormControl className={styles.container} fullWidth={fullWidth}>
            <InputLabel id={labelId}>{label}</InputLabel>
            <MUISelect
                className={classNames(className, styles.selectBox)}
                disabled={disabled}
                labelId={labelId}
                id={id}
                label={label}
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                value={value}
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                defaultValue={defaultValue}
                onChange={onChange}
                variant={variant || "outlined"}
                size={size || "small"}
                error={error}
                sx={{
                    ...boxStyle,
                    width: boxStyle?.width ?? width,
                    minWidth: boxStyle?.minWidth ?? minWidth,
                    maxWidth: boxStyle?.maxWidth ?? maxWidth,
                }}
                MenuProps={menuProps}
                {...(IconComponent && {IconComponent: IconComponent})}
            >
                {children}
            </MUISelect>
            { helperText && <FormHelperText>{helperText}</FormHelperText> }
        </FormControl>
    </>);
};

export default Box;