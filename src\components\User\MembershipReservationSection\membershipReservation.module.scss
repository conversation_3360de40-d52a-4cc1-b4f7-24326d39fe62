@use 'sass:map';
@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;
@import "rfs/scss";

$sm-breakpoint: 480px;
$md-breakpoint: 768px;
$lg-breakpoint: 1024px;
$xl-breakpoint: 1440px;
$--override-rdp-day-height: 2.0rem;
$--override-rdp-day-width: 2.0rem;

.membershipReservationContainer {
    padding: 1rem 0;
}

.divider {
    text-align: center;
    align-items: center;
    justify-content: center;
}

.userReservedContainer {
    margin: 0 0 2rem 0 !important;
}

.enqueryContainer {
    display: flex;
    padding: 2px 1rem 6px;
    align-items: center;
    border-bottom: 6px solid #5f8300;
    margin-bottom: 2rem !important;

    .enqueryContent {
        justify-content: center;
        align-items: flex-end;
        
        @media (max-width: $sm-breakpoint) {
            align-items: flex-start;
        }
    }

    .selectBox {
        background-color: white;
    }

    .daypickerDisplayBox {
        display: flex;
        flex: 2;
    }
}

.daypickerDrawer {
    @media (min-width: $sm-breakpoint) {
        max-width: 100%;
        min-height: 90vh;
        max-height: 90vh;
        margin: 0 auto;
    };

    @media (min-width: $md-breakpoint) {
        min-width: 600px;
        max-width: 600px;
        min-height: 340px;
        max-height: 340px;
        margin: 0 auto;
    };

    @media (min-width: $lg-breakpoint) {
        min-width: 700px;
        max-width: 700px;
        max-height: 450px;
        margin: 0 auto;
    };

    @media (min-width: $xl-breakpoint) {
        min-width: 1000px;
        max-width: 1000px;
        margin: 0 auto;
    };

    .drawerActionArea {
        margin: 1.3rem 0;
        align-items: center;
        justify-content: center;
    }
}

.daypickerOverrideRoot {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem !important;

    .daypickerOverrideMonths {
        justify-content: center;

        .daypickerOverrideMonth {
            justify-content: center;
    
            .daypickerOverrideDay {
                width: $--override-rdp-day-width !important;
                height: $--override-rdp-day-height !important;

                &.daypickerOverrideSelected {
                    border: 0;
                    border-radius: 0.3rem;
                    background-color: rgb(242, 225, 37);
                    color: rgb(33, 33, 33);
                }
            
                .daypickerOverrideDayButton {
                    width: $--override-rdp-day-width;
                    height: $--override-rdp-day-height;
                }
            }
        }
    }

    .reservableSessionModifier {
        background-color: rgb(72, 167, 0);
        color: white;
        border-radius: 0.3rem;
    }

    .userBookedSessionModifier {
        background-color: tomato;
        color: white;
        border-radius: 0.3rem;
    }
}

.modifierBox {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.sessionModifierLabel {
    display: inline-flex;
    border-radius: 0.3rem;
    width: 1rem;
    height: 1rem;

    &.sessionReservable {
        background-color: rgb(72, 167, 0);
    }

    &.userBooked {
        background-color: tomato;
    }
}