@import "rfs/scss";

.detailExpand {
    position: relative;
    height: auto;
    min-height: max-content;
    border-top: 0;
    margin-bottom: 48px;

    & > input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;

        & + label {
            background: #fff;
            display: block;
            width: 100%;
            min-height: 48px;
            cursor: pointer;
            position: absolute;
            top: 0;
            transition: top .45s cubic-bezier(.44, .99, .48, 1);
            box-shadow: inset -1px -2px 2px #d3d3d3;

            &:before {
                position: absolute;
            }
            
            &:before {
                content: "▼";
                text-align: center;
                justify-items: center;
                width: 100%;
                max-height: 48px;
                top: 0.5rem;
                font-size: 1.3rem;
            }
        }

        & ~ div.detailExpandContent {
            width: 100%;
            overflow: hidden;
            max-height: 0;
            transition: max-height .45s cubic-bezier(.44, .99, .48, 1);
        }

        &:checked ~ div.detailExpandContent {
            display: flex;
            flex-direction: column;
            gap: 10px;
            line-height: 2rem;
            font-size: 1rem;
            overflow: hidden;
            max-height: 300px;

            & > div:last-child {
                margin-bottom: 1rem !important;
            }
        }
          
        &:checked + label {
            top: 100%;
        }
          
        &:checked + label:before {
            content: "▲";
        }
    }
}
@keyframes sudo {
    from {
        transform: translateY(-2px);
    }
    to {
        transform: translateY(2px);
    }
}