import { TextButton } from "@/components/_CommonElements/Button";
import Section from "@/components/_CommonElements/Section";
import EventBanner from "@/components/Kiosk/InfoDisplay/Banner";
import styles from "@/components/Kiosk/InfoDisplay/event.module.scss";
import EnumEventPreviewPosition from "@/constants/enum/EventPreviewPosition";
import { EventPreviewType } from "@/constants/enum/EventPreviewType";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import UtilityHooks from '@/hooks/Utility';
import EventDetails from "@/models/api/models/EventDetails";
import TimerIcon from '@mui/icons-material/Timer';
import { Box, Paper, Stack, Tab, Tabs } from "@mui/material";
import Icon from "@mui/material/Icon";
import classNames from "classnames";
import dayjs from "dayjs";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useCallback, useEffect, useMemo, useState } from "react";

import EventBuyContainer from "../Buy";

// interface TabPanelProps {
//     children?: React.ReactNode;
//     index: number;
//     activedIndex: number;
// }

// function TabPanel(props: TabPanelProps) {
//     const { children, activedIndex, index, ...other } = props;
  
//     return (
//         <div
//             role="tabpanel"
//             hidden={activedIndex !== index}
//             id={`vertical-tabpanel-${index}`}
//             aria-labelledby={`vertical-tab-${index}`}
//             {...other}
//         >
//             {activedIndex === index && (
//                 <Box>
//                     {children}
//                 </Box>
//             )}
//         </div>
//     );
// }

interface EventInfoDisplayProps {
    eventDetails: EventDetails;
}
// add session dropdown;
const EventInfoDisplay = (props: EventInfoDisplayProps) => {
    const { eventDetails } = props;
    const router = useRouter();
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);
    const { counter, setCounter } = UtilityHooks.useCountDown(-1);

    const timeoutInMinute = 7;
    const [buyEntryOpen, setBuyEntryOpen] = useState(false);
    // const [activedTabIndex, setActivedTabIndex] = useState(0);

    // const isForSale = useMemo(() => eventDetails.eventBundleList.length > 0, [eventDetails.eventBundleList.length]);
    // const saleStartDateTime_Unix = useMemo(() => dayjs.utc(eventDetails.saleStartDateTimeForDisplay), [eventDetails.saleStartDateTimeForDisplay]);
    // const saleEndDateTime_Unix = useMemo(() => dayjs.utc(eventDetails.saleEndDateTimeForDisplay), [eventDetails.saleEndDateTimeForDisplay]);
    // const saleStartDateTime_LocalTime = useMemo(() => saleStartDateTime_Unix.tz(eventDetails.eventTimeZone), [eventDetails.eventTimeZone, saleStartDateTime_Unix]);
    // const saleEndDateTime_LocalTime = useMemo(() => saleEndDateTime_Unix.tz(eventDetails.eventTimeZone), [eventDetails.eventTimeZone, saleEndDateTime_Unix]);
    
    // const eventStartDateTime_LocalTime = useMemo(() => dayjs.utc(eventDetails.eventStartDateTime).tz(eventDetails.eventTimeZone), [eventDetails.eventStartDateTime, eventDetails.eventTimeZone]);
    // const eventEndDateTime_LocalTime = useMemo(() => dayjs.utc(eventDetails.eventEndDateTime).tz(eventDetails.eventTimeZone), [eventDetails.eventEndDateTime, eventDetails.eventTimeZone]);

    const banner = useMemo(() => {
        const banners = eventDetails.previewList.filter((preview) => preview.position === EnumEventPreviewPosition.BANNER && preview.previewType === EventPreviewType.JPEG);
        return banners.length > 0 ? banners : undefined;
    }, [eventDetails.previewList]);

    const counterMessage = useMemo(() =>
        <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            height={50}
            fontSize={"3rem"}
        >
            <Stack direction="row" sx={{ alignItems: "center" }}>
                <TimerIcon sx={{ color: "#322500", fontSize: "3rem" }} />
                {new Date(counter * 1000).toISOString().substring(14, 19)}
            </Stack>
        </Box>
    , [counter]);

    const startPurchaseProcess = useCallback(() => setBuyEntryOpen(!buyEntryOpen), [buyEntryOpen]);

    // const handleTabOnChange = useCallback((event: React.SyntheticEvent, newValue: number) => {
    //     setActivedTabIndex(newValue);
    // }, []);

    useEffect(() => {
        if (buyEntryOpen) {
            setCounter(timeoutInMinute * 60);
        }
    }, [buyEntryOpen, setCounter]);
    useEffect(() => {
        if (counter === 0) {
            // setBuyEntryOpen(!buyEntryOpen);
            // setCounter(-1);
            router.reload();
        }
    }, [counter, router]);

    if (!buyEntryOpen) {
        return (
            <>
                <Box className={styles.tabBoxSamsungGalaxyTabS9Ultra}>
                    {banner && <EventBanner items={banner} disableGallery />}
                    {/* <Tabs
                        orientation="vertical"
                        variant="fullWidth"
                        value={activedTabIndex}
                        onChange={handleTabOnChange}
                        aria-label="Vertical tabs example"
                        sx={{ borderRight: 1, borderColor: 'divider' }}
                    >
                        <Tab icon={<HomeMaxIcon fontSize="large" />} className={styles.tabIcon} />
                        <Tab icon={<LocalActivityIcon fontSize="large" />} className={styles.tabIcon} />
                    </Tabs>
                    <TabPanel activedIndex={activedTabIndex} index={0}>
                        {banner && <EventBanner items={banner} />}
                    </TabPanel>
                    <TabPanel activedIndex={activedTabIndex} index={1}>
                        <Section
                            content={
                                <div className={classNames(styles.field, styles.border)}>
                                    <div className={styles.basicInfoTable}>
                                        <EventMetadata
                                            title={eventTranslation("detail.event")}
                                            content={`${eventStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")} ~ ${eventEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")}`}
                                        />
                                        <EventMetadata
                                            title={eventTranslation("detail.salesDate")}
                                            content={`${saleStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")} ~ ${saleEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")}`}
                                        />
                                        { isForSale &&
                                            <>
                                                <Label title={eventTranslation("detail.rateCardLabel")} />
                                                <RateCard dataSource={eventDetails.eventBundleList} />
                                            </>
                                        }
                                    </div>
                                </div>
                            }
                        />
                    </TabPanel> */}
                </Box>
                <div className={styles.toBuy}>
                    <TextButton
                            size="extra"
                            onClick={startPurchaseProcess}
                            variant="contained"
                        >
                        <Icon baseClassName="fa" className="fa-ticket-simple" />
                        &nbsp; {eventTranslation("detail.buy")}
                    </TextButton>
                </div>
            </>
        );
    }

    return (
        <Section
            containerSize="wide"
            content={
                <EventBuyContainer 
                    eventDetails={eventDetails}
                    counterMessage={counterMessage}
                />
            }
        />
    );
};
export default EventInfoDisplay;
