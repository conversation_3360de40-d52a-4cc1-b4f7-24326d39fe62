import styles from "@/components/Guide/guide.module.scss";
import classNames from "classnames";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  StepBox,
  DetailStep,
} from "@/components/_CommonElements/Display";
import {
  SecondaryNavigation,
  NavItem,
} from "@/components/_CommonElements/SecondaryNavigation";
import Markdown from "markdown-to-jsx";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import { Fragment, ReactNode } from "react";
import Accordion from "@/components/_CommonElements/Accordion";
import Icon from '@mui/material/Icon';
interface MarkdownProps {
  content: string & ReactNode;
}
interface NavTitleProps {
  linkTo: string;
  title: string;
}
interface StepContainerProps {
  linkTo: string;
  children: ReactNode;
}

interface StepTitleProps {
  children: ReactNode;
}

interface StepContainerProps {
  children: ReactNode;
}

interface StepBoxProps {
  numbering: string;
  icon: string;
  label: string;
  helpText: string;
}
interface DetailStepProps {
    numbering: string;
    image: string;
    label: string;
    contents: string;
  }

const NavTitle = (props: NavTitleProps) => {
  const { linkTo, title } = props;
  return <NavItem link={`#${linkTo}`} title={title} />;
};

const StepContainer = (props: StepContainerProps) => {
  const { linkTo, children } = props;
  return (
    <div id={`${linkTo}`} className={styles.guideSection}>
      {children}
    </div>
  );
};

const StepBoxsContainer = (props: StepContainerProps) => {
  const { children } = props;
  return <ul className={classNames(styles.flowContainer)}>{children}</ul>;
};

const StepBoxs = (props: StepBoxProps) => {
  const { numbering, icon, label, helpText } = props;

  return (
    <StepBox
      numbering={parseInt(numbering)}
      icon={<Icon baseClassName="fa" className={icon} />}
      label={label}
      helpText={helpText}
    />
  );
};

const StepTitleContainer = (props: StepTitleProps) => {
  const { children } = props;
  return <ChipLabel className={styles.chipLabel}>{children}</ChipLabel>;
};

interface AccordionContainerProps {
    title: string;
    children: ReactNode;
}
const AccordionContainer = (props: AccordionContainerProps) => {
  const { children, title } = props;
  return (
    <Accordion className={styles.accordion} title={`+ ${title}詳細教學`}>
      {children}
    </Accordion>
  );
};

const DetailStepContainer = (props: DetailStepProps) => {
  const { numbering, image, label, contents } = props;
  return (
    <DetailStep
      label={label}
      image={`/${image}`}
      numbering={parseInt(numbering)}
      contents={contents}
    />
  );
};
const MarkdownContainer = (props: MarkdownProps) => {
  const { content } = props;
  return (
    <Markdown
      options={{
        wrapper: Fragment,
        forceWrapper: false,
        overrides: {
          Nav: {
            component: NavTitle,
          },
          StepContainer: {
            component: StepContainer,
          },
          StepTitle: {
            component: StepTitleContainer,
          },
          StepBoxsContainer: {
            component: StepBoxsContainer,
          },
          StepBoxs: {
            component: StepBoxs,
          },
          Accordion: {
            component: AccordionContainer,
          },
          DetailStep:{
            component:DetailStepContainer,
          }
        },
      }}
    >
      {content}
    </Markdown>
  );
};


const Guide = () => {
  const { t: guideTranslation } = useTranslation(EnumTranslationJson.Guide);
  return (
    <>
      <div className={styles.container}>
        <SecondaryNavigation>
          <MarkdownContainer content={guideTranslation("complete.title")} />
          <MarkdownContainer content={guideTranslation("register.title")} />
          <MarkdownContainer content={guideTranslation("purchase.title")} />
          <MarkdownContainer content={guideTranslation("merchandise.title")} />
        </SecondaryNavigation>
        <div className={styles.contents}>
            <MarkdownContainer content={guideTranslation("complete.content")} />
            <MarkdownContainer content={guideTranslation("register.content")} />
            <MarkdownContainer content={guideTranslation("purchase.content")} />
            <MarkdownContainer content={guideTranslation("merchandise.content")} />
        </div>
      </div>
    </>
  );
};

export default Guide;
