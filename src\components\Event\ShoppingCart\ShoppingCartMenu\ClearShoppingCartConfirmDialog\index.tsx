import { useCallback, useState } from "react";
import { useTranslation } from "next-i18next";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { dispatch, useSelector } from "@/redux/store";
import { clearCart, closeClearShoppingCartConfirmDialog } from "@/redux/slices/cartSlice";
import { DeleteAllShoppingCartItemAPIResult } from "@/models/api/result/user/shoppingCart";
import { BACKEND } from "@/constants/config";
import ENDPOINT from "@/models/api/endpoint";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import API from "@/utils/API";
import { useSnackbar } from "notistack";
import { CircularProgress } from "@mui/material";

const useShoppingCart = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const deleteAllAsync = async () => {
        return await requestAsync<DeleteAllShoppingCartItemAPIResult>(
            ENDPOINT.DeleteAllShoppingCartItem(),
            {
                method: RequestMethod.DELETE
            }
        );
    };
    return { deleteAllAsync };
};

const ClearShoppingCartConfirmDialog = () => {
    const clearShoppingCartConfirmDialog = useSelector(state => state.cart.clearShoppingCartConfirmDialog);
    const [ isDeleting, setIsDeleting ] = useState(false);
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const { deleteAllAsync } = useShoppingCart();
    const { enqueueSnackbar } = useSnackbar();
    
    const handleClose = useCallback(() => {
        dispatch(closeClearShoppingCartConfirmDialog());
    }, []);

    const handleAllow = useCallback(() => {
        void (async () => {
            setIsDeleting(true);
            try {
                await deleteAllAsync();
                dispatch(clearCart());
                dispatch(closeClearShoppingCartConfirmDialog());
            }
            catch (error) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
            }
            setIsDeleting(false);
            handleClose();
        })();
    }, [enqueueSnackbar, deleteAllAsync, handleClose]);

    return (
        <Dialog
            open={clearShoppingCartConfirmDialog.open}
            onClose={handleClose}
            disableEscapeKeyDown
        >
            <DialogTitle>
                {modalTranslation(clearShoppingCartConfirmDialog.titleI18nKey)}
            </DialogTitle>
            <DialogContent>
                <DialogContentText>
                    {modalTranslation(clearShoppingCartConfirmDialog.contentI18nKey)}
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button onClick={handleClose} color="error">{modalTranslation("buttons.cancel")}</Button>
                <Button disabled={isDeleting} onClick={handleAllow} autoFocus>
                    { isDeleting ? <CircularProgress /> : modalTranslation("buttons.confirm") }
                </Button>
            </DialogActions>
        </Dialog>
    );
};

ClearShoppingCartConfirmDialog.displayName = "ClearShoppingCartConfirmDialog";
export default ClearShoppingCartConfirmDialog;