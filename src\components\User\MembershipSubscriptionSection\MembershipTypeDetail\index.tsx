import Markdown from "markdown-to-jsx";
import { Fragment, ReactNode } from "react";

import styles from "../membership.module.scss";

interface ParagraphProps {
    children: ReactNode;
}
const Paragraph = (props: ParagraphProps) => {
    const { children } = props;
    return <div className={styles.paragraph}>{children}</div>
};

interface ListItemProps {
    children: ReactNode;
}
const ListItem = (props: ListItemProps) => {
    const { children } = props;
    return (
        <div className={styles.listItem}>{children}</div>
    );
}

interface MarkdownProps {
    content: string;
}

const MarkdownContainer = (props: MarkdownProps) => {
    const { content } = props;
    return (
      <Markdown
        options={{
            wrapper: Fragment,
            forceWrapper: false,
            overrides: {
                p: {
                    component: Paragraph
                },
                ListItem: {
                    component: ListItem,
                },
            },
        }}
      >
        {content}
      </Markdown>
    );
};
  
interface MembershipTypeDetailProps {
    description: string;
}

const MembershipTypeDetail = (props: MembershipTypeDetailProps) => {
    const { description } = props;
    return (
        <MarkdownContainer content={description} />
    );
};
  
  export default MembershipTypeDetail;