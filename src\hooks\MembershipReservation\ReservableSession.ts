import { <PERSON><PERSON><PERSON><PERSON> } from "@/constants/config";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import ENDPOINT from "@/models/api/endpoint";
import ReservableSessionsAPIResult from "@/models/api/result/reserve/reservableSessions";
import ReserveSessionAPIResult from "@/models/api/result/reserve/reserveSession";
import UserReservedSessionsAPIResult from "@/models/api/result/user/reservedSessions";

const useReservableSession = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION, EnumRequestHeader.LANGUAGE] });

    const postReservableSessionAsync = async (membershipReservableSessionId: string) => {
        const formData = new FormData();
        formData.append("membershipReservableSessionId", membershipReservableSessionId);
        return await requestAsync<ReserveSessionAPIResult>(
            ENDPOINT.ApplyReserveSession(),
            {
                method: RequestMethod.POST,
                data: formData
            }
        );
    };

    const getSessionsAsync = async (siteId: string) => {
        return await requestAsync<ReservableSessionsAPIResult>(
            ENDPOINT.GetReservableSessions(siteId),
            {
                method: RequestMethod.GET,
            }
        );
    };

    const getUserReservedSessionAsync = async (status: string = "RESERVE") => {
        return await requestAsync<UserReservedSessionsAPIResult>(
            ENDPOINT.GetUserReservedSession(status),
            {
                method: RequestMethod.GET,
            }
        );
    };

    const cancelReservedSessionAsync = async (reserveId: string) => {
        return await requestAsync<ReserveSessionAPIResult>(
            ENDPOINT.CancelReservedSession(reserveId),
            {
                method: RequestMethod.DELETE,
            }
        );
    };

    return { getSessionsAsync, getUserReservedSessionAsync, postReservableSessionAsync, cancelReservedSessionAsync };
};

export { useReservableSession };

