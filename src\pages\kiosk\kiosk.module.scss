@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

$mobile-header-height: map.get(theme.$height, "mobile-header");
$desktop-header-height: map.get(theme.$height, "desktop-header");
$footer-height: map.get(theme.$height, "footer");
$background-color: map.get(theme.$color, "background");
$text-color: map.get(theme.$color, "text");

.kioskSectionLabel {
    padding: 0 6rem;
}