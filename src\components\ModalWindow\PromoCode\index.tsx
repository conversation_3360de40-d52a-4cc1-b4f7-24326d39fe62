import Modal from "@/components/_CommonElements/Modal";
import { TextInput } from "@/components/_CommonElements/Input";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { dispatch, useSelector } from "@/redux/store";
import { useShoppingCart } from "@/hooks/Order/ShoppingCart";
import { useDiscountCode } from "@/hooks/Order/DiscountCode";
import { setItems, setDiscountInfo, setTotalPrice } from "@/redux/slices/cartSlice";

import React, { useEffect } from "react";
import { useCallback, useState } from "react";
import { useTranslation } from "next-i18next";
import { Box, SxProps, Theme } from "@mui/material";
import { enqueueSnackbar } from "notistack";
import API from "@/utils/API";
import styles from "@/components/ModalWindow/PromoCode/promoCode.module.scss";
import { ShoppingCartSummarizedItem } from "@/models/api/models/ShoppingCart";
import ApplyDiscountCodeAPIResult from "@/models/api/result/order/applyDiscountCode";

interface PromoCodeDialogProps {
    visible: boolean;
    defaultValue?: string;
    sx?: SxProps<Theme>;
    onChange?: (value: string) => void;
    onSuccess?: (cartItems: ShoppingCartSummarizedItem[]) => void;
    onError?: (error: string) => void;
    onClose: () => void;
}


const PromoCodeDialog = (props: PromoCodeDialogProps) => {
    const { 
        visible,
        defaultValue,
        onChange,
        onSuccess,
        onError,
        onClose,
        sx,
    } = props;

    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const { t: snackBarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const [promoteCode, setPromoteCode] = useState<string>("");
    const cartItems = useSelector(state => state.cart.items);
    const { fetchAsync, updateAsync, deleteAsync } = useShoppingCart();
    const { applyDiscountCodeAsync } = useDiscountCode();

    const _onChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setPromoteCode(e.target.value);
    }, []);

    useEffect(() => {
        if (onChange) {
            onChange(promoteCode);
        }
    }, [promoteCode, onChange]);

    const onClickApply = useCallback(() => {
        if (promoteCode == "") {
            enqueueSnackbar(modalTranslation("modals.promoCode.content.validation.missingCode"), { variant: "error" });
            return false;
        }
        void (async () => {
            try {
                const shoppingCart = (await fetchAsync()).data!;
                if (shoppingCart) {
                    for await (const item of cartItems) {
                        const { eventSessionBundleId, quantity } = item;
                        const savedItemIndex = shoppingCart.items.findIndex(_item => _item.eventSessionBundleId == eventSessionBundleId);
                        if (savedItemIndex == -1) {
                            await updateAsync({
                                eventSessionBundleId: eventSessionBundleId,
                                quantity: quantity
                            });
                        } else if (shoppingCart.items[savedItemIndex].quantity > quantity || shoppingCart.items[savedItemIndex].quantity < quantity) {
                            await updateAsync({
                                eventSessionBundleId: eventSessionBundleId,
                                quantity: (quantity - shoppingCart.items[savedItemIndex].quantity)
                            });   
                        }
                    }

                    for await (const savedItem of shoppingCart.items) {
                        if ( cartItems.findIndex(_item => _item.eventSessionBundleId == savedItem.eventSessionBundleId) == -1 ) {
                            await deleteAsync(savedItem.eventSessionBundleId);
                        }
                    }

                    const { data: responseData } : ApplyDiscountCodeAPIResult = await applyDiscountCodeAsync(promoteCode);
                    const discountCodeInfo = responseData?.discountCodeInfo;
                    const orderPreviewResult = responseData?.orderPreviewResult;
                    if (orderPreviewResult) {
                        const updatedShoppingCartItems = cartItems.map(item => {
                            const discountPrice = orderPreviewResult.items.find(_item => _item.eventSessionBundleId == item.eventSessionBundleId)?.discountPrice ?? 0;
                            const newItem = {
                                ...item,
                                originalPrice: item.price,
                                price: discountPrice
                            }
                            return newItem;
                        });
                        dispatch(setItems(updatedShoppingCartItems));
                        dispatch(setDiscountInfo(discountCodeInfo));
                        dispatch(setTotalPrice(orderPreviewResult.orderDiscountPrice));
                        if (onSuccess) onSuccess(updatedShoppingCartItems);
                    }
                }
            }
            catch (error) {
                console.error(API.GetErrorMessage(error));
                if (onError) onError(snackBarTranslation(`messages.discountCode.apply.error.${API.GetErrorMessage(error)}`));
            }
        })();
    }, [applyDiscountCodeAsync, cartItems, deleteAsync, fetchAsync, modalTranslation, onError, onSuccess, promoteCode, snackBarTranslation, updateAsync]);

    return (<>  
        <Modal
            visible={visible}
            confirmButtonLabel={modalTranslation("modals.promoCode.content.promoCodeApply")}
            title={modalTranslation("modals.promoCode.title")}
            onConfirm={onClickApply}
            onBackdropClick={onClose}
            onCancel={onClose}
        >
            <Box sx={{
                minWidth: "320px"
            }}>
                <TextInput
                    defaultValue={defaultValue}
                    className={styles.promoteCode}
                    name="promoCode"
                    label={modalTranslation("modals.promoCode.content.promoCode")}
                    helperText={modalTranslation("modals.promoCode.content.helper")}
                    size="small"
                    value={promoteCode}
                    onChange={_onChanged}
                    sx={sx}
                />
            </Box>
        </Modal>
    </>);
};

export default PromoCodeDialog;