import { useEffect } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { Typography } from "@mui/material";
import { useSnackbar } from "notistack";
import QRCode from "react-qr-code";

import RouteMap from "@/constants/config/RouteMap";
import { OrderType } from "@/constants/enum/OrderType";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import { useQueryPaymentOrderState } from "@/hooks/Order/QueryPaymentOrderState";

import styles from "@/components/Checkout/checkout.module.scss";

interface QRCodePaymentDisplayProp {
    image: string;
    orderId: string;
    paymentGatewayId: string;
    fns?: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        [key: string]: (...args: any[]) => void | Promise<void>
    };
}

const QRCodePaymentDisplay = (props: QRCodePaymentDisplayProp) => {
    const { image, orderId, paymentGatewayId, fns } = props;

    const { enqueueSnackbar } = useSnackbar();
    const router = useRouter();
    const { t: purchaseTranslation } = useTranslation(EnumTranslationJson.Purchase);
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { data, isFetched, isError } = useQueryPaymentOrderState(orderId, paymentGatewayId);

    useEffect(() => {
        if (isError) {
            void enqueueSnackbar(snackbarTranslation("messages.purchase.error.fail"), { variant: "error" });
            router.back();
        }

        if (isFetched) {
            if (data?.result && data?.data) {
                switch (data.data.transactionState as OrderType)
                {
                    case OrderType.SUCCESS:
                    case OrderType.PAID:
                    case OrderType.EXPIRED:
                    case OrderType.FAILED:
                        if (fns) {
                            void fns.clearCartItems();
                            void fns.deleteCookie(EnumCookieKey.TRACED_REFERRAL_CODE);
                            void router.push(RouteMap.EventPurchasedResult(orderId));
                        }
                        break;
                    case OrderType.CLOSED:
                        if (fns) {
                            void fns.clearCartItems();
                            void fns.deleteCookie(EnumCookieKey.TRACED_REFERRAL_CODE);
                            void router.back();
                        }
                        break;
                    default:
                        
                        break;
                }
            }
        }
    }, [data?.data, data?.result, enqueueSnackbar, fns, isError, isFetched, orderId, router, snackbarTranslation]);

    return (
        <>
            <div className={styles.container}>
                <Typography variant="h6">{purchaseTranslation("paymentTypeReminder.qrCode")}</Typography>
                <QRCode value={image} />
            </div>
        </>
    );
  };
  
  export default QRCodePaymentDisplay;