import classNames from "classnames";
import { Dispatch, ReactNode, SetStateAction, useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "next-i18next";
import { FormControl, Radio, RadioGroup } from "@mui/material";

import { useSelector } from "@/redux/store";
import { BACKEND } from "@/constants/config";
import ENDPOINT from "@/models/api/endpoint";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import EventPaymentGatewayAndMethodsAPIResult from "@/models/api/result/events/paymentGatewayAndMethods";
import EventPaymentMethod from "@/models/api/models/EventPaymentMethod";
import EventPaymentGatewayAndMethod from "@/models/api/models/EventPaymentGatewayAndMethod";

import shoppingCartMenuStyles from "@/components/Event/ShoppingCart/ShoppingCartMenu/shoppingCartMenu.module.scss";
import { LoadingCircle } from "@/components/_CommonElements/LoadingBar";
import { selectAllItems } from "@/redux/slices/oneTimePaymentSlice";

interface PaymentSelectorType extends EventPaymentMethod, EventPaymentGatewayAndMethod {}

interface PaymentSelectorProps {
    allPaymentMethods: EventPaymentMethod[];
    defaultValue?: string;
    isCheckingOut: boolean;
    selectedPaymentState: [EventPaymentGatewayAndMethod | undefined, Dispatch<SetStateAction<EventPaymentGatewayAndMethod | undefined>>];
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    responseContent?: ReactNode;
}

const PaymentSelector = (props: PaymentSelectorProps) => {
    const { allPaymentMethods, isCheckingOut, selectedPaymentState, responseContent } = props;

    const { t: purchaseTranslation } = useTranslation(EnumTranslationJson.Purchase);
    const cartItems = useSelector(state => state.cart.items);
    const eventId = useSelector(state => state.cart.eventId);
    const oneTimePayments = useSelector(selectAllItems) ?? null;
    const [selectedPayment, setSelectedPayment] = selectedPaymentState;
    
    const [paymentMethods, setPaymentMethods] = useState<PaymentSelectorType[]>([]);
    
    const isEmptyCart = useMemo(() => (cartItems.length | oneTimePayments.length) <= 0, [cartItems.length, oneTimePayments.length]);

    const getEventSupportPaymentGatewayMethod = useCallback(async () => {
        if (!eventId && !oneTimePayments) return;
        const eventPaymentMethodGatewayResponseData = eventId ? (await BACKEND.Gateway.fetchQuery<EventPaymentGatewayAndMethodsAPIResult>({
            url: ENDPOINT.GetEventSupportPaymentGatwayMethods(eventId),
            params: {
                queryKey: "event-support-payment-gateway",
            },
        })).data! : [];

        if (eventPaymentMethodGatewayResponseData.length == 0) {
            const iconsValue = allPaymentMethods.find(method => method.value === 'card');
            const gatewayValue = {
                paymentGatewayPaymentMethodName: 'card',
                paymentGatewayId: 'LABPAY_AIRWALLETX'
            };
            if (iconsValue && gatewayValue) {
                const defaultPaymentMethod = {
                    ...iconsValue,
                    ...gatewayValue,
                };
                setPaymentMethods([
                    defaultPaymentMethod,
                ]);
            }
        } else {
            const eventSupportPaymentMethods = allPaymentMethods.reduce((selectorTypes: PaymentSelectorType[], method) => {
                const gatewayValue = eventPaymentMethodGatewayResponseData.find(gateway => gateway.paymentGatewayPaymentMethodName == method.value);
                if (gatewayValue) {
                    selectorTypes.push({
                        ...method,
                        ...gatewayValue,
                    });
                }
                return selectorTypes;
            }, []);
            if (eventSupportPaymentMethods) {
                setPaymentMethods(eventSupportPaymentMethods);
            }
        }
    }, [allPaymentMethods, eventId]);

    const onPaymentMethodChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const newPaymentMethod = paymentMethods?.find(method => method.paymentGatewayPaymentMethodName == e.target.value);
        if (newPaymentMethod) {
            setSelectedPayment({
                paymentGatewayId: newPaymentMethod.paymentGatewayId,
                paymentGatewayPaymentMethodName: newPaymentMethod.paymentGatewayPaymentMethodName
            });
        }
    }, [paymentMethods, setSelectedPayment]);


    useEffect(() => {
        void getEventSupportPaymentGatewayMethod();
    }, [getEventSupportPaymentGatewayMethod]);

    return (
        <FormControl>
            {!eventId && !oneTimePayments ? 
                <LoadingCircle /> :
                    responseContent ? responseContent :  
                    <RadioGroup
                        value={selectedPayment}
                        aria-labelledby="payment-radio-buttons-group-label"
                        onChange={onPaymentMethodChanged}
                    >
                        {paymentMethods.map(paymentMethod => 
                            <div 
                                key={`payment-method-${paymentMethod.value}`}
                                className={
                                    classNames(shoppingCartMenuStyles.radio, { 
                                        [shoppingCartMenuStyles.active]: selectedPayment?.paymentGatewayPaymentMethodName == paymentMethod.value, 
                                        [shoppingCartMenuStyles.disabled]: isEmptyCart || isCheckingOut 
                                    })
                                }
                            >
                                <div className={shoppingCartMenuStyles.radioContainer}>
                                    <Radio color="primary" value={paymentMethod.value} />
                                </div>
                                <div className={classNames(shoppingCartMenuStyles.labelContainer, shoppingCartMenuStyles.payment)}>
                                    <div className={shoppingCartMenuStyles.content}>
                                        {purchaseTranslation(`paymenttitle.${paymentMethod.value}.title`)}
                                        <div className={shoppingCartMenuStyles.note}>{purchaseTranslation(`paymenttitle.${paymentMethod.value}.remark`) ?? ''}</div>
                                    </div>
                                    <div className={shoppingCartMenuStyles.logo}>
                                        {paymentMethod?.icons?.map((img: string, index) => 
                                            <img key={`payment-method-icon-${index}`} src={img} alt={`${paymentMethod.value} icon`} />
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}
                    </RadioGroup>
            }
        </FormControl>
    );
};

export default PaymentSelector;