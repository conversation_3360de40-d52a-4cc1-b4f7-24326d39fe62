import ModalContainer from "@/components/_CommonElements/Modal/Container";

import LoginForm from "@/components/User/Login/Form";
import SignUpForm from '@/components/User/SignUp/Form';
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useCallback, useMemo } from "react";
import { dispatch, useSelector } from "@/redux/store";
import { ModalActions } from "@/redux/slices/uiSlice";

const ModalLoginWindow = () => {
    const loginModalState = useSelector((state) => state.ui.modal.login);
    const { t: navigationTranslation } = useTranslation(EnumTranslationJson.Navigation);

    const onClose = useCallback(() => {
        dispatch(ModalActions.closeLoginModal());
    }, []);

    const tabsArray = useMemo(() => {
        return [{
            name: navigationTranslation("main.login"),
            contents: <LoginForm noFormHead />
        },
        {
            name: navigationTranslation("main.register"),
            contents: <SignUpForm noFormHead />
        }].map((item, index) => ({
            ...item,
            active: loginModalState.tabIndex === index,
            action: () => dispatch(ModalActions.setLoginModalState({tabIndex: index}))
        }));
    }, [loginModalState.tabIndex, navigationTranslation]);

    return (
        <ModalContainer
            onClickClose={onClose}
            disableBackdropClickClose
            open={loginModalState.visible}
            modalTabs={tabsArray}
        />
    );
};

export default ModalLoginWindow;