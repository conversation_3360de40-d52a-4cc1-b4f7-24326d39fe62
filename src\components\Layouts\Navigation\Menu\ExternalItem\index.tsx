import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { INavigationExternalItem } from "@/constants/navigation";
import { OverlayActions } from "@/redux/slices/uiSlice";
import { dispatch } from "@/redux/store";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import { useCallback, useMemo } from "react";
import { MenuItem } from "@stoneleigh/navigation-menu";
import classNames from "classnames";
import { useRouter } from "next/router";
import styles from "./item.module.scss";

const NavigationMenuExternalItem = (props: INavigationExternalItem) => {
    const {
        translationKey,
        path,
        badge,
        newTab,
        beforeLine,
        direction,
        content
    } = props;
    const { t: navigationTranslation } = useTranslation(EnumTranslationJson.Navigation);
    const onClick = useCallback(() => {
        dispatch(OverlayActions.closeNavigationMenu());
    }, []);
    const router = useRouter();
    const Content = useMemo(() => {
        if (content) {
            return <>{content}</>;
        }
        return (
            <MenuItem
                badge={badge}
                onClick={onClick}
                className={classNames(
                    styles.hoverStyle,
                    {
                        [styles.mobileItem]: direction === "vertical",
                        [styles.beforeLine]: beforeLine,
                        [styles.active]: path === router.pathname
                    }
                )}
            >
                <div>{navigationTranslation(`main.${translationKey}`)}</div>
            </MenuItem>
        );
    }, [router, content, badge, beforeLine, direction, navigationTranslation, onClick, translationKey]);

    if (path === undefined) {
        return Content;
    }
    if (content) {
        return Content;
    }
    return (
        <Link
            href={path}
            {...(newTab ? { target: "_blank", rel: "noreferrer noopener" } : {})}
        >
            {Content}
        </Link>
    );
};
export default NavigationMenuExternalItem;