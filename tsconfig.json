{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/api/*": ["src/api/*"], "@/components/*": ["src/components/*"], "@/constants/*": ["src/constants/*"], "@/hooks/*": ["src/hooks/*"], "@/models/*": ["src/models/*"], "@/pages/*": ["src/pages/*"], "@/public/*": ["public/*"], "@/redux/*": ["src/redux/*"], "@/styles/*": ["src/styles/*"], "@/utils/*": ["src/utils/*"], "@/types": ["src/types/*"]}, "typeRoots": ["./src/types"], "incremental": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}