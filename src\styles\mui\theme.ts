import { createTheme, responsiveFontSizes } from "@mui/material/styles";
import { Noto_Sans, Noto_Sans_TC, <PERSON>o } from "next/font/google";
import componentThemes from "./componentThemes";

const notoSans = Noto_Sans({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700', '800', '900']
});

const notoSansTC = Noto_Sans_TC({
  subsets: ['latin'],
  weight: ['100', '300', '400', '500', '700', '900']
});

const roboto = Roboto({ 
  subsets: ['latin'], 
  weight: ['100', '300', '400', '500', '700', '900']
});

// Create a theme instance.
let theme = createTheme({
  breakpoints: {
    values: {
      xs: 0,
      sm: 480,
      md: 768,
      lg: 1100,
      xl: 1400,
    },
  },
  palette: {
    primary: {
      main: "#ff7802",
      contrastText: "#121212"
    },
    secondary: {
      main: "rgb(1,175,199)",
      contrastText: "#fff"
    },
  },
  typography: {
    fontFamily: `${notoSans.style.fontFamily} ${notoSansTC.style.fontFamily} ${roboto.style.fontFamily}`,
    button: {
      fontFamily: `${notoSans.style.fontFamily} ${notoSansTC.style.fontFamily} ${roboto.style.fontFamily}`,
      fontWeight: "bold",
      fontSize: "1rem",
      color: "red",
    },
  },
  components: {
    MuiIcon: componentThemes.MuiIcon,
    MuiDialog: componentThemes.MuiDialog,
    MuiMenu: componentThemes.MuiMenu
  },
});

theme = responsiveFontSizes(theme);

export default theme;