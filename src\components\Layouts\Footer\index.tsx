import { Fragment } from "react";
import classNames from "classnames";
import Link from "next/link";
import { useTranslation } from "next-i18next";
import Markdown from "markdown-to-jsx";

import styles from "@/components/Layouts/layouts.module.scss";
import Sitemap from "@/components/Layouts/Footer/Sitemap";

import Grid from "@mui/material/Grid";
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import FacebookIcon from '@mui/icons-material/Facebook';
import InstagramIcon from '@mui/icons-material/Instagram';
import TwitterIcon from '@mui/icons-material/Twitter';
import YouTubeIcon from '@mui/icons-material/YouTube';

import ContactInfos from "@/constants/contents/contactInfo";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import RouteMap from "@/constants/config/RouteMap";

const Footer = () => {
    const { t: footerTranslation } = useTranslation(EnumTranslationJson.Footer);
    return (<footer className={classNames(styles.footer)}>
        <Grid container direction="column" className={styles.container} sx={{ height: "100%", maxWidth: "1140px" }}>
            <Grid item container justifyContent="center">
                <Grid container item direction="column" xs={12} sm={8} md={6} sx={{ marginBottom: "20px" }}>
                    <Grid item container>
                        <h2 className={styles.contactUs}>{footerTranslation("ContactUs")}</h2>
                    </Grid>
                    <Grid item container alignItems="center">
                        <ul className={styles.contactInfo}>
                            { ContactInfos?.email && <li><Link href={`mailto:${ContactInfos.email.url}`}><MailOutlineIcon />{ContactInfos.email.name}</Link></li> }
                            { ContactInfos?.whatsapp && <li><Link href={ContactInfos.whatsapp.url} target="_blank"><WhatsAppIcon />{ContactInfos.whatsapp.name}</Link> ({footerTranslation("textOnly")})</li> }
                            { ContactInfos?.facebook && <li><Link href={ContactInfos.facebook.url} target="_blank"><FacebookIcon />{ContactInfos.facebook.name}</Link></li> }
                            { ContactInfos?.twitter && <li><Link href={ContactInfos.twitter.url} target="_blank"><TwitterIcon />{ContactInfos.twitter.name}</Link></li> }
                            { ContactInfos?.instagram && <li><Link href={ContactInfos.instagram.url} target="_blank"><InstagramIcon />{ContactInfos.instagram.name}</Link></li> }
                            { ContactInfos?.youtube && <li><Link href={ContactInfos.youtube.url} target="_blank"><YouTubeIcon />{ContactInfos.youtube.name}</Link></li> }
                        </ul>
                    </Grid>
                </Grid>
                <Grid container item xs={12} sm={4} md={6}>
                    <Sitemap />
                </Grid>
            </Grid>
            <Grid item container justifyContent="center">
                <Grid item xs={12} className={styles.copyright}>
                    <Link href={RouteMap.TermsAndConditions}>{footerTranslation("Terms")}</Link>
                    <span className={styles.verticalDivider} />
                    <Link href={RouteMap.PrivacyPolicy}>{footerTranslation("PrivacyPolicy")}</Link>
                </Grid>
                <Grid item xs={12} className={styles.copyright}>
                    {footerTranslation("copyrightClaim", {
                        YEAR: new Date().getFullYear(),
                        PLATFORM_NAME: "INCUTix"
                    })}
                </Grid>
                <Grid item xs={12} className={classNames(styles.copyright, styles.footerPoweredClaim)}>
                    {footerTranslation("poweredClaim.platformName", {
                        PLATFORM_NAME: "EasyLive Show Limited"
                    })}
                </Grid>
                <Grid item xs={12} className={classNames(styles.copyright, styles.footerPoweredClaimAdress)}>
                    <Markdown
                        options={{
                            wrapper: Fragment,
                            forceWrapper: false
                        }}
                    >
                        {footerTranslation("poweredClaim.businessAddress", {
                            REGISTERED_ADDRESS: "B6, 25/F., TML Tower, <br />3 Hoi Shing Road, Tsuen Wan, Hong Kong"
                        })}
                    </Markdown>
                </Grid>
            </Grid>
        </Grid>
    </footer>);
};

export default Footer;