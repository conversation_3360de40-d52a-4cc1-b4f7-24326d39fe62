import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import PhoneNumber from '@easylive-show/react-phone-input';
import zh from '@/components/_CommonElements/Input/PhoneInput/lang/zh.json';
import styles from "@/components/_CommonElements/Input/input.module.scss";

interface onChangeProps {
    value: string;
    country: { name: string, dialCode: string, countryCode: string };
    e: any;
    formattedValue: string;
}

interface PhoneInputProps {
    value: string | number;
    country?: string;
    preferredCountries?: string[];
    onlyCountries?: string[];
    excludeCountries?: string[];
    placeholder?: string;
    autoFormat?: boolean;
    disableDropdown?: boolean;
    disableCountryCode?: boolean;
    enableLongNumbers?: boolean;
    countryCodeEditable?: boolean;
    enableSearch?: boolean;
    disableSearchIcon?: boolean;
    onChange?(value: string, country: { name: string, dialCode: string, countryCode: string }, e: any, formattedValue: string): void;
    disabled?: boolean;
    specialLabel?: string;
    tabIndex?: number;
    inputProps?: any;
    defaultErrorMessage?: string;
    isValid?: boolean;
    showHelperText?: boolean;
    helperText?: string;
};

const PhoneInput = (props: PhoneInputProps) => {
    const {
        value,
        country,
        preferredCountries,
        onlyCountries,
        excludeCountries,
        placeholder,
        autoFormat,
        disableDropdown,
        disableCountryCode,
        enableLongNumbers,
        countryCodeEditable,
        enableSearch,
        disableSearchIcon,
        onChange,
        disabled,
        specialLabel,
        tabIndex,
        inputProps,
        defaultErrorMessage,
        isValid,
        showHelperText,
        helperText
    } = props;

    return (<>
        <FormControl fullWidth>
            <PhoneNumber
                country={country || 'hk'}
                preferredCountries={preferredCountries || ['hk', 'mo', 'tw', 'cn']}
                onlyCountries={onlyCountries}
                excludeCountries={excludeCountries}
                placeholder={placeholder || ""}
                autoFormat={autoFormat}
                disableDropdown={disableDropdown}
                disableCountryCode={disableCountryCode}
                enableLongNumbers={enableLongNumbers}
                countryCodeEditable={countryCodeEditable || false}
                enableSearch={enableSearch ?? true}
                disableSearchIcon={disableSearchIcon}
                onChange={onChange}
                disabled={disabled}
                inputProps={{
                    ...inputProps,
                    tabIndex: tabIndex,
                    autoComplete: 'off',
                }}
                value={value.toString()}
                localization={zh}
                specialLabel={specialLabel || "手機號碼"}
                defaultErrorMessage={defaultErrorMessage || "請輸入正確的手機號碼"}
                isValid={isValid}
            />
            {/* {showHelperText && <FormHelperText className={styles.helperText}>{helperText || "請確認輸入之電話能接收SMS訊息以進行驗證"}</FormHelperText>} */}
        </FormControl>
    </>);
};

export default PhoneInput;