import styles from "@/components/Layouts/Footer/Sitemap/sitemap.module.scss";
import classNames from "classnames";

import NavigationArray, { NavigationItem }  from "@/constants/navigation/sitemap";
import { useRouter } from "next/router";
import Link from "next/link";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";

const Sitemap = () => {
  const router = useRouter();
  const navigation = new NavigationArray(router.pathname);
  const { t: navigationTranslation } = useTranslation(EnumTranslationJson.Navigation);

  return (<div className={styles.container}>

    <ul className={styles.sitemap}>
      {navigation.getItems().map((each: NavigationItem, i: number) => {return (
        <li 
          className={classNames(
            styles.item,
            each.active && styles.active,
          )} 
          key={i}
        >
          {each.path
            ? <Link href={each.path}>
                {navigationTranslation(`main.${each.displayName}`)}
              </Link>
            : each.displayName
          }
        </li>
      )})}
    </ul>

  </div>);
};

export default Sitemap;