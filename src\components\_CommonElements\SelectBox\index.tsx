import React, { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "next-i18next";
import { 
    useMediaQuery,
    SwipeableDrawer,
    List,
    TextField,
    Box,
    ListItemButton,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';

import Select from "@/components/_CommonElements/Input/Select";
import DataSourceProps from "@/models/props/DataSourceProps";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import styles from "./selectBox.module.scss";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';

interface Props {
    classes?: {
        box?: string;
        option?: string;
        optionValue?: string;
        header?: string;
        headerLabel?: string;
    };
    disabled: boolean;
    dataSource?: DataSourceProps[];
    onLoadIcon?: React.ElementType<any> | undefined;
    onChange: (id: string | undefined) => void;
}

const SelectBox = React.memo((props: Props) => {
    const theme = useTheme();
    const screenBreakpointUpDetect = useMediaQuery(theme?.breakpoints?.up('sm'));

    const { disabled, dataSource, onLoadIcon, onChange, classes = {} } = props;
    const prevDataSource = useRef<DataSourceProps[] | undefined>(dataSource);
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);
    const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
    const [selectedId, setSelectedId] = useState<string>("disable");
    const [selectedName, setSelectedName] = useState<string>("");

    const onItemSelected = useCallback((id: string, name: string) => {
        setSelectedId(id);
        setSelectedName(name);
    }, []);

    const onSelectedChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const id = e.target.value;
        const item = dataSource!.find(item => item.id === id)!;
        onItemSelected(id, item.primaryValue);
    }, [dataSource, onItemSelected]);

    const toggleDrawer = useCallback(() => {
        setDrawerOpen(!drawerOpen);
    }, [drawerOpen]);

    const onDrawerItemSelected = useCallback((id: string, name: string) => {
        onItemSelected(id, name);
        toggleDrawer();
    }, [toggleDrawer, onItemSelected]);

    useEffect(() => {
        prevDataSource.current = dataSource;
        if (prevDataSource.current !== undefined && dataSource !== undefined) {
            if (prevDataSource.current.length === dataSource.length) {
                const mismatchedItem = dataSource.filter((item, i) => item.id !== prevDataSource.current![i].id);
                if (mismatchedItem.length === 0) {
                    // The data source contains same item, and item index, so dont change the selected id.
                    return;
                }
            }
            
        }
        setSelectedId("disable");
    }, [dataSource]);

    useEffect(() => {
        onChange(selectedId);
    }, [onChange, selectedId]);

    if (screenBreakpointUpDetect) {
        return (
            <Select.Box      
                disabled={disabled}
                className={classes.box ?? ""}
                value={selectedId}
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                onChange={onSelectedChanged}
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                boxStyle={styles.box}
                menuProps={{
                    PaperProps: {
                        style: {
                            maxHeight: '60vh',
                        },
                    },
                    anchorOrigin: {
                        vertical: "center",
                        horizontal: "center"
                    },
                    transformOrigin: {
                        vertical: "center",
                        horizontal: "center"
                    }
                }}
                {...(onLoadIcon && {IconComponent: onLoadIcon})}
            >
                <Select.Option className={classes.option} disabled value="disable">
                    {eventTranslation("detail.select")}
                </Select.Option>
                {
                    dataSource &&  dataSource.map(item => {
                        if (item.isHeader) {
                            return (
                                <ListItem key={item.id} divider value={item.value}>
                                    <span className={classes.headerLabel}>
                                        { item.primaryValue }
                                        { item.secondaryValue }
                                    </span>
                                </ListItem>
                            );
                        }
                        return (
                            <Select.Option key={item.id} divider className={classes?.option} disabled={item.disabled} value={item.value}>
                                <ListItemText primary={item.outOfStock ? item.primaryValue + " " + eventTranslation("detail.soldOut") : item.primaryValue} secondary={item.secondaryValue} />
                            </Select.Option>
                        );
                    })
                }
            </Select.Box>
        )
    }
    return (
        <Box
            sx={{
                width: "100%"
            }}
        >
            <TextField
                className={styles.mobileInputField}
                fullWidth
                label={""}
                placeholder={eventTranslation("detail.select")}
                variant="outlined"
                onClick={toggleDrawer}
                value={selectedName}
                InputProps={{
                    autoComplete: "false",
                    endAdornment: <ArrowDropDownIcon />
                }}
            />
            <SwipeableDrawer 
                anchor={"bottom"} 
                open={drawerOpen} 
                onOpen={toggleDrawer} 
                onClose={toggleDrawer}
                PaperProps={{
                    style: {
                        maxHeight: "45vmax",
                    }
                }}
            >
                <List disablePadding>
                    {dataSource && dataSource.map(item => {
                        if (item.isHeader) {
                            return (
                                <ListItem divider className={styles.header} key={item.id}>
                                    <span className={classes.headerLabel}>
                                        { item.primaryValue }
                                        { item.secondaryValue }
                                    </span>
                                </ListItem>
                            );
                        }
                        return (
                            <ListItem divider key={item.id} disablePadding>
                                <ListItemButton onClick={() => onDrawerItemSelected(item.id, `${item.primaryValue} ${item.secondaryValue}`)} disabled={item.disabled}>
                                    <ListItemText primary={item.outOfStock ? item.primaryValue + " " + eventTranslation("detail.soldOut") : item.primaryValue} secondary={item.secondaryValue} />
                                </ListItemButton>
                            </ListItem>
                        );
                    })}
                </List>
            </SwipeableDrawer>
        </Box>
    );
});
SelectBox.displayName = "SelectBox";

export default SelectBox;