import MembershipSubscription from "./MembershipSubscription";

interface UserAccount {
    isVerified: boolean;
    userId: string;
    userIdHash: string;
    email: string;
    nickname: string;
    country: string;
    countryCode: string;
    mobilePhoneNumber: string;
    optIn: boolean;
    permissions: string[],
    createdDateTime: number,
    membershipSubscription?: MembershipSubscription;
    portraitURL?: string;
    firstName?: string;
    lastName?: string;
    dateOfBirth?: string;
}
export default UserAccount;