@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

$tablet-header-height: map.get(theme.$height, "tablet-header");
$desktop-header-height: map.get(theme.$height, "desktop-header");
$background-color: map.get(theme.$color, "background");
$primary-color: map.get(theme.$color, "primary");
$secondary-color: map.get(theme.$color, "secondary");

.developmentOnly {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}
.button {
    padding: 5px 10px;
    background: white;
    border-radius: 20px;
    font-size: 2rem;
}
.cardActionArea {
    height: 100%;
}
.cardContentContainer {
    --font-size: 1rem;
    width: 100%;
    display: block;
    overflow: hidden;
    font-size: var(--font-size);
    & .cardContent {
        --line-height: 1.5rem;
        --total-line: 3;
        display: -webkit-box;
        overflow: hidden;
        overflow-wrap: normal;
        -webkit-line-clamp: var(--total-line);
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        word-wrap: break-word;
        line-height: var(--line-height);
        height: calc(var(--line-height) * var(--total-line));
    }

    & .eventDate {
        color: #d3661e;
        font-weight: 600;
        text-transform: uppercase;
    }
}