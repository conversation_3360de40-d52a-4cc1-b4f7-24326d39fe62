@use 'sass:map';

@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;

$breakpoint: 800px;

.container[class~=MuiGrid-root] {
  flex-wrap: nowrap;
  @media (max-width: $breakpoint) {
    flex-direction: column;
    align-items: center;
  };
};

.box {
  padding: 1rem 0;
  overflow: auto;
}

.list {
  max-width: 200px;
  width: 200px;
  margin: 0 20px;
  border: 1px solid #efefef;
  border-radius: 4px;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);

  @media (max-width: $breakpoint) {
    max-width: 700px;
    width: 90%;
    flex-direction: row;
    flex-wrap: wrap;
    margin: 0 auto 20px auto;
    z-index: 1;
  };

  & .item {
    word-break: keep-all;

    @media (max-width: $breakpoint) {
      width: 45%;
    };

    & span {
      display: inline-block;
      padding: 5px;
      cursor: pointer;
      user-select: none;

      &:hover {
        color: map.get(theme.$color, "primary");        
      };
    };

    &.active span {
      color: map.get(theme.$color, "primary");
      font-weight: 700;
      cursor: context-menu;
    };
  };
};

.content[class~=MuiGrid-root] {
  width: 100%;
  padding: 0 5px;
};