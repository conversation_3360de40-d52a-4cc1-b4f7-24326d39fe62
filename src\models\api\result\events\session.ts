import { APISuccessResult } from "@stoneleigh/api-lib";
import EventSession from "../../models/EventSession";
import EventSessionBundlesByDate from "../../models/EventSessionBundlesByDate";
import EventSessionDetails from "../../models/EventSessionWithDetails";

type EventSessionAPIResult = APISuccessResult<EventSession[]>;
type EventSessionDetailsAPIResult = APISuccessResult<EventSessionDetails>;
type EventSessionBundleAPIResult = APISuccessResult<EventSessionBundlesByDate>;
export type { EventSessionAPIResult, EventSessionDetailsAPIResult, EventSessionBundleAPIResult };