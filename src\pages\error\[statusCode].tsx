import type { NextPage } from "next";
import { GetServerSideProps } from "next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import i18n from "@/utils/i18n";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { Box } from "@mui/material";
import TextButton from "@/components/_CommonElements/Button/TextButton";
import { useCallback } from "react";
import styles from "./error.module.scss";
import PageContent from "@/components/_PageComponents/PageContent";

const ErrorPage: NextPage = () => {
    const router = useRouter();
    const { query } = router;
    const { statusCode, route } = query;
    const { t: errorTranslation } = useTranslation(EnumTranslationJson.Error);

    const supportedStatusCode = [404, 500];
    const isSupportedStatusCode = supportedStatusCode.find(item => item === Number.parseInt(statusCode as string)) !== undefined;
    const i18nStatusCode = isSupportedStatusCode ? statusCode as string : "unknown";
    const onRetryButtonClick = useCallback(() => {
        void router.push((route as string));
    }, [router, route]);

    const title = errorTranslation(`errors.${i18nStatusCode}.title`);
    const message = errorTranslation(`errors.${i18nStatusCode}.message`);

    return (
        <PageContent
            title={title}
            robotIndex="noindex"
            content={
                <div className={styles.container}>
                    <div className={styles.message}>
                        <p>{message}</p>
                    </div>
                    <Box justifyContent="center" alignItems="center">
                        <TextButton
                            label={errorTranslation("content.retryButton")}
                            onClick={onRetryButtonClick}
                        />
                    </Box>
                </div>
            }
        />
    );
};
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [EnumTranslationJson.Error],
    context,
});
export default ErrorPage;
