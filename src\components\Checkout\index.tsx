import ShoppingCartCheckoutIcon from '@mui/icons-material/ShoppingCartCheckout';
import { Box, Chip, CircularProgress, Container, Divider } from "@mui/material";
import Grid from "@mui/material/Unstable_Grid2";
import classNames from "classnames";
import { deleteCookie, getCookie } from "cookies-next";
import Markdown from "markdown-to-jsx";
import { useTranslation } from "next-i18next";
import Router, { useRouter } from "next/router";
import { useSnackbar } from "notistack";
import React, { Fragment, ReactNode, useCallback, useEffect, useMemo, useState } from "react";

import { BACKEND } from "@/constants/config";
import RouteMap from "@/constants/config/RouteMap";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import { EnumDiscountCodeType } from "@/constants/enum/DiscountCodeType";
import { EnumPaymentGateway } from "@/constants/enum/PaymentGateway";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import PaymentMethodList from "@/constants/paymentMethods";

import ShoppingCartLayoutActionArea, { type ActionComponent } from "@/components/Event/ShoppingCart/ShoppingCartMenu/ActionArea";
import ShoppingCartItems from "@/components/Event/ShoppingCart/ShoppingCartMenu/ItemList";
import PaymentSelector from "@/components/Event/ShoppingCart/ShoppingCartMenu/PaymentSelector";
import PromoteCodeInput from "@/components/Event/ShoppingCart/ShoppingCartMenu/PromoteCodeInput";
import { Checkbox } from "@/components/_CommonElements/Input";
import { dispatch, useSelector } from "@/redux/store";

import styles from "@/components/Checkout/checkout.module.scss";
import shoppingCartMenuStyles from "@/components/Event/ShoppingCart/ShoppingCartMenu/shoppingCartMenu.module.scss";

import API from "@/utils/API";
import i18n from "@/utils/i18n";

import AuthenicationHooks from "@/hooks/Authentication";
import { useShoppingCart } from "@/hooks/Order/ShoppingCart";

import ENDPOINT from "@/models/api/endpoint";
import EventPaymentGatewayAndMethod from "@/models/api/models/EventPaymentGatewayAndMethod";
import EventPaymentMethod from "@/models/api/models/EventPaymentMethod";
import CreateOrderAPIResult from "@/models/api/result/order/create";

import OneTimePaymentProps from "@/models/props/OneTimePaymentProps";
import { clearCart, setDiscountInfo, setItems, selectCheckoutItemsByIdSelector } from "@/redux/slices/cartSlice";
import { selectAllItems } from "@/redux/slices/oneTimePaymentSlice";
import { ModalActions } from "@/redux/slices/uiSlice";
import Section from "../_CommonElements/Section";
import QRCodePaymentDisplay from "./Payment/QRCode";
import { useDiscountCode } from '@/hooks/Order/DiscountCode';

const AgreementLabel = React.memo(() => {
    const { t: purchaseTranslation } = useTranslation(EnumTranslationJson.Purchase);
    const Router = useRouter();

    return (
        <span>
            <Markdown
                options={{
                    wrapper: Fragment,
                    forceWrapper: false
                }}
            >
                {purchaseTranslation("modal.content.agreementClaim", {
                    TNC_URL: `/${Router.locale}${RouteMap.TermsAndConditions}`,
                    POLICY_URL: `/${Router.locale}${RouteMap.PrivacyPolicy}`
                })}
            </Markdown>
        </span>
    );
});
AgreementLabel.displayName = "AgreementLabel";

interface CheckoutProps {
}

const useCreateOrder = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION, EnumRequestHeader.LANGUAGE] });
    const createOrderAsync = async ({ inCartItemEventSessionBundleIdList, userComment, paymentMethod }: 
        { 
            inCartItemEventSessionBundleIdList: string[], 
            userComment: string,
            paymentMethod: { id: string, gateway: string } 
        } ) => {
        const formData = new FormData();
        formData.append("PaymentGatewayId", paymentMethod.gateway);
        formData.append("PaymentType", paymentMethod.id);
        for (let i = 0; i < inCartItemEventSessionBundleIdList.length; i++) {
            const eventSessionBundleId = inCartItemEventSessionBundleIdList[i];
            if (eventSessionBundleId) {
                formData.append(`Items[${i}].eventSessionBundleId`, eventSessionBundleId);
            }
        }
        formData.append("UserComment", userComment);
        return await requestAsync<CreateOrderAPIResult>(
            ENDPOINT.OrderPayment(),
            {
                method: RequestMethod.POST,
                data: formData
            }
        );
    };
    const createOneTimePaymentOrderAsync = async ({ membershipTypeId, paymentMethod }: 
        { 
            membershipTypeId: string,
            paymentMethod: { id: string, gateway: string } 
        } ) => {
        const formData = new FormData();
        formData.append("PaymentGatewayId", paymentMethod.gateway);
        formData.append("PaymentType", paymentMethod.id);
        formData.append("MembershipTypeId", membershipTypeId);
        return await requestAsync<CreateOrderAPIResult>(
            ENDPOINT.MembershipSubscribeOrderPayment(),
            {
                method: RequestMethod.POST,
                data: formData
            }
        );
    };
    return { createOrderAsync, createOneTimePaymentOrderAsync };
};

const Checkout = (props: CheckoutProps) => {
    const { enqueueSnackbar } = useSnackbar();
    const router = useRouter();
    const { isAuthorized } = AuthenicationHooks.useUserInfo();
    const { deleteAllAsync } = useShoppingCart();
    const { createOrderAsync, createOneTimePaymentOrderAsync } = useCreateOrder();
    const { removeAppliedDiscountCodeAsync } = useDiscountCode();
    const { t: checkoutTranslation } = useTranslation(EnumTranslationJson.Checkout);
    const { t: shoppingCartTranslation } = useTranslation(EnumTranslationJson.ShoppingCart);
    const { t: purchaseTranslation } = useTranslation(EnumTranslationJson.Purchase);
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);

    const cartA = useSelector(state => state.cart);
    const cartItems = useSelector(state => state.cart.items);
    const eventName = useSelector(state => state.cart.eventName);
    const oneTimePayments = useSelector(selectAllItems);
    const hasOneTimePayment = useMemo(() => oneTimePayments.length > 0, [oneTimePayments]);

    const numOfItemsInCart = useMemo(() => {
        if (hasOneTimePayment) return oneTimePayments.reduce((numInCart: number, item) => numInCart += item.itemQuantity, 0);
        return cartItems.reduce((numInCart: number, item) => numInCart += item.quantity, 0);
    }, [cartItems]);
    const eventCurrency = useMemo(() => {
        if (hasOneTimePayment) return (oneTimePayments.length > 0) ? oneTimePayments[0].currency : undefined;
        return (cartItems.length > 0) ? cartItems[0].currency : undefined;
    }, [cartItems]);
    const discountForOrder = useSelector(state => state.cart.discountInfo);
    const discountForOrderTotalPrice = useSelector(state => state.cart.totalPrice);
    const discountValueAndType = useMemo(() => {
        if (!discountForOrder) return undefined;
        let _result = "";
        const _discountValueType: EnumDiscountCodeType = discountForOrder.discountValueType as EnumDiscountCodeType;
        switch (_discountValueType) {
            case EnumDiscountCodeType.FIXED:
                _result = i18n.GetCurrency(eventCurrency as string, discountForOrder.discountValue, router.locale);
                break;
            case EnumDiscountCodeType.PERCENTAGE:
                _result = discountForOrder.discountValue * 100 + "%";
                break;
            default:
                console.log(eventCurrency);
                _result = discountForOrder.discountValue.toString();
        }
        return " -" + _result;
    }, [discountForOrder, eventCurrency, router.locale]);
    const sortAndGroupBySessionDateTime = useMemo(() => {
        const groupBySessionDateTime = {};
        cartItems.reduce((group: { [char: string]: (typeof item)[] }, item) => {
            const groupName = `s_${item.sessionStartDateTime}_e_${item.sessionEndDateTime}`;
            if (!Object.keys(group).includes(groupName)) {
                group[groupName] = [];
            }
            group[groupName].push(item);
            return group;
        }, groupBySessionDateTime);
        const sortedGroupBySessionDateTime: { [char: string]: (typeof cartItems) } = (Object.keys(groupBySessionDateTime) as Array<keyof typeof groupBySessionDateTime>)
            .sort()
            .reduce((r, k) => ({ ...r, [k]: groupBySessionDateTime[k] }), {});
        return sortedGroupBySessionDateTime;
    }, [cartItems]);
    const subTotalPriceInCart = useMemo(() => {
        if (hasOneTimePayment) oneTimePayments.reduce((subTotal: number, item) => (subTotal += (item.itemFee * item.itemQuantity)) && subTotal, 0);
        return Object.values(sortAndGroupBySessionDateTime).flat().reduce((subTotal: number, item) => (subTotal += (item.price * item.quantity)) && subTotal, 0);
    }, [sortAndGroupBySessionDateTime]);
    const totalPriceInCart = useMemo(() => {
        if (hasOneTimePayment) return oneTimePayments.reduce((total: number, item) => (total += (item.itemFee * item.itemQuantity)) && total, 0);
        if (!discountForOrder) return Object.values(sortAndGroupBySessionDateTime).flat().reduce((total: number, item) => (total += (item.price * item.quantity)) && total, 0);
        return discountForOrderTotalPrice;
    }, [sortAndGroupBySessionDateTime, discountForOrder, discountForOrderTotalPrice]);
    const referralCode = getCookie(EnumCookieKey.TRACED_REFERRAL_CODE)?.toString() ?? "";

    const [selectedPayment, setSelectedPayment] = useState<EventPaymentGatewayAndMethod>();
    const [promoteCode, setPromoteCode] = useState<string>("");
    const [isAcceptedAgreement, setIsAcceptedAgreement] = useState<boolean>(false);
    const [isCheckingOut, setIsCheckingOut] = useState<boolean>(false);
    const [isCreating, setIsCreating] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [responseContent, setResponseContent] = useState<ReactNode | null>(null);
    const [isQrCodeReady, setIsQrCodeReady] = useState<boolean>(false);

    const allPaymentMethods: EventPaymentMethod[] = PaymentMethodList.items;

    const inCartItemEventSessionBundleIdList = useSelector(selectCheckoutItemsByIdSelector);
    const isEmptyCart = useMemo(() => (cartItems.length | oneTimePayments?.length) === 0, [cartItems.length]);

    const clearCartItems = useCallback(async () => {
        setIsDeleting(true);
        try {
            await deleteAllAsync();
            dispatch(clearCart());
        } catch (error) {
            const errorMessage = API.GetErrorMessage(error);
            void enqueueSnackbar(errorMessage, { variant: "error" });
        }
        setIsDeleting(false);
    }, [enqueueSnackbar, deleteAllAsync]);
    const finishCheckoutAndclearCartItems = useCallback(async () => {
        setIsDeleting(true);
        try {
            await deleteAllAsync();
            dispatch(clearCart());
        } catch (error) {
            const errorMessage = API.GetErrorMessage(error);
            void enqueueSnackbar(errorMessage, { variant: "error" });
        }
        setIsDeleting(false);
    }, [enqueueSnackbar, deleteAllAsync]);

    const onChangeIsAcceptedAgreement = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setIsAcceptedAgreement(e.target.checked);
    }, []);
    const checkIsAcceptedAgreement = useCallback(() => isAcceptedAgreement, [isAcceptedAgreement]);
    const onPromoteCodeChanged = useCallback((code: string) => {
        setPromoteCode(code);
    }, []);

    const showQRCodePaymentResponse = useCallback((paymentOrderId: string, qrcodeImageUrl: string) => {
        if (selectedPayment) {
            setIsQrCodeReady(true);
            setResponseContent(
                <QRCodePaymentDisplay 
                    image={qrcodeImageUrl} 
                    orderId={paymentOrderId} 
                    paymentGatewayId={selectedPayment.paymentGatewayId}
                    fns={{
                        clearCartItems: clearCartItems,
                        deleteCookie: deleteCookie,
                    }}
                />);
        }
    }, [clearCartItems, selectedPayment]);

    const submitOrder = useCallback(async (
        isAcceptedAgreement: boolean, 
        userComment: string, 
        paymentMethod: { id: string, gateway: string }) => {
        try {
            setIsCreating(true);
            setIsCheckingOut(true);
            if (!isAcceptedAgreement) {
                void enqueueSnackbar(snackbarTranslation("messages.purchase.error.acceptAgreement"), { variant: "error" })
                throw new Error("IsAcceptedAgreement: False");
            }
            if (!isAuthorized) {
                dispatch(ModalActions.openLoginModal());
                throw new Error("IS_AUTHORIZED: False");
            }
            if (!hasOneTimePayment && !cartItems) {
                void enqueueSnackbar(snackbarTranslation("messages.purchase.error.unknowItem"), { variant: "error" })
                await clearCartItems();
                throw new Error("EMPTY_CART_ITEMS");
            }
            try {
                let res = null;
                if (hasOneTimePayment) {
                    if (oneTimePayments.length <= 0) throw new Error("EMPTY_CART_ITEMS");
                    res = (await createOneTimePaymentOrderAsync({ membershipTypeId: oneTimePayments[0].itemId, paymentMethod})).data!;
                } else {
                    res = (await createOrderAsync({inCartItemEventSessionBundleIdList, userComment, paymentMethod})).data!;
                }
                if (paymentMethod.gateway == EnumPaymentGateway.LABPAY_AIRWALLETX.toString()) {
                    void finishCheckoutAndclearCartItems();
                    deleteCookie(EnumCookieKey.TRACED_REFERRAL_CODE);
                    if (res?.paymentUrl) {
                        void Router.push(res.paymentUrl);
                    } else {
                        void enqueueSnackbar(snackbarTranslation("messages.purchase.error.unknowItem"), { variant: "error" });
                    }
                } else if ( res?.paymentQRCodeURL ) {
                    void showQRCodePaymentResponse(res.paymentOrderId, res.paymentQRCodeURL);
                }
            }
            catch (error) {
                const errorMessage = API.GetErrorMessage(error);
                void enqueueSnackbar(errorMessage, { variant: "error" });
                throw error;
            }
        } catch {
            setIsCreating(false);
        }
    }, [isAuthorized, cartItems, enqueueSnackbar, snackbarTranslation, clearCartItems, createOrderAsync, inCartItemEventSessionBundleIdList, finishCheckoutAndclearCartItems, showQRCodePaymentResponse]);
    const createOrder = useCallback(() => {
        setIsCheckingOut(true);
        if (selectedPayment) {
            void submitOrder(isAcceptedAgreement, referralCode, { id: selectedPayment.paymentGatewayPaymentMethodName, gateway: selectedPayment.paymentGatewayId });
        } else {
            void enqueueSnackbar(checkoutTranslation('confirm.missingPaymentMethod'), { variant: "error" });
        }
        setIsCheckingOut(false);
    }, [selectedPayment, submitOrder, isAcceptedAgreement, referralCode, enqueueSnackbar, checkoutTranslation]);
    
    const removeAppliedDiscount = useCallback(async () => {
        try {
            const removeResponse = await removeAppliedDiscountCodeAsync();
            if (removeResponse.data) {
                const rollbackCartItems = cartItems.map(item => ({ ...item, price: item.originalPrice ?? item.price }));
                dispatch(setItems(rollbackCartItems));
                dispatch(setDiscountInfo(undefined));
            }
        } catch (error) {
            enqueueSnackbar(snackbarTranslation("messages.discountCode.apply.error.invalid"), { variant: "error" });
        }
    }, [cartItems, enqueueSnackbar, removeAppliedDiscountCodeAsync, snackbarTranslation]);
    
    const Actions = useMemo<ActionComponent[]>(() => ([
        {
            label: 'popover.button.goToPay',
            onClick: createOrder,
            icon: <ShoppingCartCheckoutIcon />,
            disableCheckingOn: checkIsAcceptedAgreement,
        }
    ]), [checkIsAcceptedAgreement, createOrder]);

    useEffect(() => {
        if (isEmptyCart) {
            router.push(RouteMap.Main);
        }
    }, [isEmptyCart]);

    return (
        <Container className={styles.container}>
            <Section id="Checkout" labelSize="xlarge" label={checkoutTranslation('title')} />
            {
                <Grid container columnSpacing={0} flexDirection="row" sx={{gap: 'inherit'}}>
                    <Grid xs={12} sm={12} md={6} lg={6} flexDirection="column">
                        <div className={classNames(shoppingCartMenuStyles.group, shoppingCartMenuStyles.noElevation)}>
                            <div className={styles.overView}>
                                <Section id="cart" labelSize="medium" label={checkoutTranslation("cart.title")} />
                                <Section id="eventName" labelSize="medium" label={eventName} className={{ label: shoppingCartMenuStyles.eventTitle }} />
                            </div>
                            <div className={classNames(styles.details, {[styles.disabled]: isCheckingOut})}>
                                {isEmptyCart ? <>{checkoutTranslation("cart.empty")}</> 
                                    :  <ShoppingCartItems dataSource={hasOneTimePayment ? oneTimePayments : sortAndGroupBySessionDateTime} isCheckingOut={isCreating} />
                                }
                            </div>
                        </div>
                    </Grid>
                    <Grid xs={12} sm={12} md={5} lg={5}>
                        <Section id="paymentMethod" labelSize="medium" label={purchaseTranslation('paymenttitle.title')} />
                        <Box className={shoppingCartMenuStyles.paymenttitleContainer}>
                            <div className={shoppingCartMenuStyles.group}>
                                <div className={shoppingCartMenuStyles.row}>
                                    <div className={shoppingCartMenuStyles.col}>
                                        {isEmptyCart ? <>{checkoutTranslation("cart.empty")}</> 
                                            : 
                                            <PaymentSelector 
                                                allPaymentMethods={allPaymentMethods}
                                                isCheckingOut={isCreating}
                                                selectedPaymentState={[selectedPayment, setSelectedPayment]}
                                                responseContent={responseContent}
                                            />
                                        }
                                    </div>
                                </div>
                                {isEmptyCart ? null : 
                                    <>
                                        <Divider />
                                        <div className={classNames(styles.details, {[styles.disabled]: isEmptyCart})}>
                                            <div className={styles.itemWrapper}>
                                                {discountForOrder ?
                                                    <div className={shoppingCartMenuStyles.discountPrice}>
                                                        <span>{purchaseTranslation("modal.subTotal")}</span>
                                                        { eventCurrency && <b>{i18n.GetCurrency(eventCurrency, subTotalPriceInCart, router.locale)}</b> }
                                                        <br />
                                                        <span>{purchaseTranslation("modal.discountCaption")}</span>
                                                        <Chip
                                                            label={discountValueAndType}
                                                            variant="outlined"
                                                            className={shoppingCartMenuStyles.appliedClip}
                                                            onClick={() => void removeAppliedDiscount()}
                                                            onDelete={() => void removeAppliedDiscount()}
                                                        />
                                                        {/* <Markdown
                                                            options={{
                                                                wrapper: Fragment,
                                                                forceWrapper: false,
                                                            }}
                                                        >          
                                                            {purchaseTranslation("modal.discountCaption", { 
                                                                "DISCOUNT_VALUE_TYPE": discountValueAndType,
                                                                "DISCOUNT_CAPTION_CLASS": shoppingCartMenuStyles.discountCaption
                                                            })}
                                                        </Markdown> */}
                                                    </div> : null
                                                }
                                                <div className={styles.billRow}>
                                                    {(!hasOneTimePayment && !discountForOrder) && 
                                                        <Box sx={{ alignSelf: 'flex-end' }}>
                                                            <PromoteCodeInput onChange={onPromoteCodeChanged} disabled={isCreating} />
                                                        </Box>
                                                    }
                                                </div>
                                                <div className={styles.billRow}>
                                                    <div className={shoppingCartMenuStyles.totalPrice}>
                                                        <span>{purchaseTranslation("modal.total")}</span>
                                                        {eventCurrency && <b>{i18n.GetCurrency(eventCurrency, totalPriceInCart, router.locale)}</b>}
                                                    </div>
                                                </div>
                                                <div className={styles.billRow}>
                                                    { isCheckingOut || isCreating ?
                                                        <div className={styles.loading}>
                                                            <CircularProgress />
                                                        </div> :
                                                        isQrCodeReady ? null : 
                                                        <>
                                                            <ShoppingCartLayoutActionArea 
                                                                agreementComponent={
                                                                    <Checkbox
                                                                        label={<AgreementLabel />}
                                                                        checked={isAcceptedAgreement}
                                                                        onChange={onChangeIsAcceptedAgreement}
                                                                    />
                                                                }
                                                                actions={Actions}
                                                            />
                                                        </>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </>
                                }
                            </div>
                        </Box>
                    </Grid>
                </Grid>
            }
        </Container>
    );
};



export default Checkout;

