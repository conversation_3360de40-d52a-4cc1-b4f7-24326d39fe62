@use 'sass:math';
@use 'sass:map';

@use "@/styles/utils/theme.module.scss"as theme;
@use "@/styles/utils/animations.module.scss"as animations;

$border-radius: map.get(theme.$border, "radius");
$primary-color: map.get(theme.$color, "primary");

div.textField {
  align-items: center;

  // & .native {
  //   border-radius: $border-radius;
  // };

  &.solidBackground {
    & label {
      background-color: map.get(theme.$color, "background");

      &[class~=MuiInputLabel-shrink] {
        border: map.get(theme.$presets, "dark-border");
        border-color: map.get(theme.$color, "primary");
      }

      ;
    }

    ;

    & .native {
      background-color: map.get(theme.$color, "background");
    }

    ;
  }

  ;
}

;

div.textInput {
  & .native {
    background-color: transparent;
  }

  ;

  & textarea[class~=MuiOutlinedInput-input] {
    padding: 0px;
  }

  ;
}

;

div.numberInputContainer {
  display: flex;

  &.small {
    height: 40px;
  }

  ;

  &.medium {
    height: 56px;
  }

  ;

  @mixin mutual-styles {
    display: flex;
    border: map.get(theme.$presets, "dark-border");
    align-items: center;
    user-select: none;
    padding: 0 12px;
    cursor: pointer;

    font: {
      size: 20px;
      weight: bold;
    }

    ;
    height: 100%;
  }

  ;

  & .decrement {
    @include mutual-styles;
    border-radius: $border-radius 0 0 $border-radius;
  }

  ;

  & .increment {
    @include mutual-styles;
    border-radius: 0 $border-radius $border-radius 0;
  }

  ;

  & .numberValue {
    display: inline-block;
    flex-grow: 1;
  }

  ;
}

;

div.numberTextInput div[class~=MuiOutlinedInput-root] {
  & fieldset[class~=MuiOutlinedInput-notchedOutline] {
    border: {
      radius: 0px;
      left: 0px;
      right: 0px;
    }

    ;
  }

  ;
}

;

div.switch {
  padding: 8;

  & span[class~=MuiSwitch-thumb] {
    transition: left 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    box-shadow: none;
    width: 20px;
    height: 20px;
    margin: 2px;
  }

  & span[class~=MuiSwitch-track] {
    background-color: lightgrey;
    border-radius: 10px;
    height: 18px;
    top: 0;
    position: relative;

    &:before,
    &:after {
      position: 'absolute';
      top: '50%';
      transform: 'translateY(-50%)';
      width: 16px;
      height: 16px;
    }

    &:before {
      left: 12px;
    }

    &:after {
      right: 12px;
    }
  }

  ;

  & span[class~=Mui-checked] {
    color: $primary-color;

    &+span[class~=MuiSwitch-track] {
      background-color: $primary-color;
    }

    ;
  }

  ;
}

;


