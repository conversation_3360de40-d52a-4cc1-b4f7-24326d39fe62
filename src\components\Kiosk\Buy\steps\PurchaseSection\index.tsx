import { useCallback, useMemo, useState } from "react";
import { useTranslation } from "next-i18next";
import dayjs, { Dayjs } from "dayjs";
import { Box, Button, Divider, Typography } from "@mui/material";
import Grid from '@mui/material/Unstable_Grid2';
import CircularProgress from "@mui/material/CircularProgress";

import DataSourceProps from "@/models/props/DataSourceProps";
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import Label from "@/components/_CommonElements/Label";
import Section from "@/components/_CommonElements/Section";

import styles from "@/components/Kiosk/InfoDisplay/event.module.scss";
import eventBuyStyles from "@/components/Kiosk/Buy/eventBuy.module.scss";

interface StepPurchaseSectionProps {
    dataSource?: DataSourceProps[];
    selectedDate: Dayjs | null | undefined;
    handleNextStep: (targetIndex: number, value?: boolean) => void;
    onSelect: (sessionId: string) => void;
}

const StepPurchaseSection = (props: StepPurchaseSectionProps) => {
    const { dataSource, selectedDate, handleNextStep, onSelect } = props;
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);
    
    const [activedButtonId, setActivedButtonId] = useState<string>("");
    const formattedSelectedDate = useMemo(() => selectedDate?.format("YYYY-MM-DD") ?? "", [selectedDate]);

    const sessionTabHeaders = useMemo(() => dataSource?.filter(d => {
        if (d.isHeader) {
            const [_, startDateTimeStr, endDateTimeStr] = d.id.match(/opt-(\d{13})_(\d{13})/i) ?? ["", null, null];
            const sessionStartDateTime = startDateTimeStr ? dayjs(Number(startDateTimeStr)) : 0;
            const sessionEndDateTime = startDateTimeStr ? dayjs(Number(endDateTimeStr)) : 0;
            if (selectedDate && dayjs.isDayjs(sessionStartDateTime) && dayjs.isDayjs(sessionEndDateTime)) {
                if (sessionStartDateTime.isSame(sessionEndDateTime, 'date')) {
                    const [_, sessionOptionDate] = d.primaryValue.match(/(\d{4}-\d{2}-\d{2}).*/i) ?? ["", null];
                    if (selectedDate.isSame(sessionOptionDate, 'date')) return d;
                } else {
                    const _selectedDate = selectedDate.set('hours', sessionStartDateTime.get('hours')).set('m', sessionStartDateTime.get('m'));
                    if (_selectedDate >= sessionStartDateTime && _selectedDate <= sessionEndDateTime) {
                        return d;
                    }
                }
            }
        }
    }, []), [dataSource, selectedDate]);

    const handleOnClick = useCallback((eventSessionId: string) => {
        setActivedButtonId(eventSessionId);
        onSelect(eventSessionId);
        handleNextStep(2);
    }, [handleNextStep, onSelect]);

    return (
        <Section
            containerSize="wide"
            className={{ root: styles.purchaseSection }}
            content={
                <>
                    <Label title={eventTranslation("detail.session")} />
                    <Typography variant="h4">{formattedSelectedDate}</Typography>
                    <Divider orientation="horizontal" flexItem />
                    {!dataSource ? 
                        <Box display={"flex"} justifyContent={"center"} alignItems={"center"}>
                            <CircularProgress />
                        </Box>
                         : 
                        <Grid container rowSpacing={2} columnSpacing={{ xs: 1, sm: 2, md: 3 }}>
                            {sessionTabHeaders?.map((header) => 
                                <Grid xs={6} key={`tab-h-${header.id}`}>
                                    <Button 
                                        fullWidth 
                                        variant="outlined" 
                                        sx={{
                                            textTransform: "none",
                                            padding: "1.3rem 0",
                                        }}
                                        className={activedButtonId === header.id ? eventBuyStyles.sessionSelected : ''}
                                        onClick={() => handleOnClick(header.id)}>
                                        <Typography variant="h5">{header.primaryValue.replaceAll(formattedSelectedDate, '')}</Typography>
                                    </Button>
                                </Grid>
                            )}
                        </Grid>
                    }
                </>
            }
        />
    );
};

export default StepPurchaseSection;