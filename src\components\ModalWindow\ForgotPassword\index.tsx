import Modal from "@/components/_CommonElements/Modal";
import ForgotPasswordForm from '@/components/User/ForgotPassword/Form';
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";

interface ModalForgotPasswordWindowProps {
  visible: boolean;
  onClose: () => void;
}

const ModalForgotPasswordWindow = (props: ModalForgotPasswordWindowProps) => {
  const {
    visible,
    onClose
  } = props;

  const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
  return (<>
    <Modal
        visible={visible}
        onCancel={onClose}
        hideCancelButton
        hideConfirmButton
        title={modalTranslation("modals.forgotPassword.title")}
    >
      <ForgotPasswordForm onClose={onClose}/>
    </Modal>
  </>);
};

export default ModalForgotPasswordWindow;