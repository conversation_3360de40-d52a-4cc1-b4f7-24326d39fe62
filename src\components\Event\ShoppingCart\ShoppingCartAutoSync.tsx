import { BA<PERSON>KE<PERSON> } from "@/constants/config";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import AuthenicationHooks from "@/hooks/Authentication";
import { useDiscountCode } from "@/hooks/Order/DiscountCode";
import { useShoppingCart } from "@/hooks/Order/ShoppingCart";
import ApplyDiscountCodeAPIResult from "@/models/api/result/order/applyDiscountCode";
import { clearCart, setEventId, setEventName, setItems, setTotalPrice, setDiscountInfo } from "@/redux/slices/cartSlice";
import { dispatch } from "@/redux/store";
import API from "@/utils/API";
import { useRouter } from "next/router";
import { enqueueSnackbar } from "notistack";
import { useEffect } from "react";

const ShoppingCartAutoSync = () => {
    const router = useRouter();
    const { fetchAsync } = useShoppingCart();
    const { getAppliedDiscountCodeAsync, applyDiscountCodeAsync } = useDiscountCode();
    const { isAuthorized } = AuthenicationHooks.useUserInfo();

    // When page changed, sync the user shopping cart again.
    useEffect(() => {
        if (!isAuthorized) {
            dispatch(clearCart());
            return;
        }
        void (async () => {
            try {
                const shoppingCart = (await fetchAsync()).data!;
                if (shoppingCart) {
                    dispatch(setEventId(shoppingCart.eventId));
                    dispatch(setEventName(shoppingCart.eventName));
                    dispatch(setItems(shoppingCart.items));                    

                    const discountCodeInfo = (await getAppliedDiscountCodeAsync()).data!;
                    if (discountCodeInfo) {
                        const { data: responseData } : ApplyDiscountCodeAPIResult = await applyDiscountCodeAsync(discountCodeInfo.discountCode);
                        const orderPreviewResult = responseData?.orderPreviewResult;
                        if (orderPreviewResult) {
                            const updatedShoppingCartItems = shoppingCart.items.map(item => {
                                const discountPrice = orderPreviewResult.items.find(_item => _item.eventSessionBundleId == item.eventSessionBundleId)?.discountPrice ?? 0;
                                const newItem = {
                                    ...item,
                                    originalPrice: item.price,
                                    price: discountPrice
                                }
                                return newItem;
                            });
                            dispatch(setItems(updatedShoppingCartItems));
                            dispatch(setTotalPrice(orderPreviewResult.orderDiscountPrice));
                        }
                        dispatch(setDiscountInfo(discountCodeInfo));
                    }
                }
            }
            catch (error) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
            }
        })();
    }, [isAuthorized, router.asPath, router.locale, fetchAsync, getAppliedDiscountCodeAsync, applyDiscountCodeAsync]);
    return null;
};
export default ShoppingCartAutoSync;