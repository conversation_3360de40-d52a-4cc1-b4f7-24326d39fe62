import { useMemo } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import dayjs from "dayjs";
import { useTranslation } from "next-i18next";
import { Card, CardActionArea, CardContent, Chip, Tooltip, Typography } from "@mui/material";

import { FRONTEND } from "@/constants/config";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import EnumEventPreviewPosition from "@/constants/enum/EventPreviewPosition";
import { EventPreviewType } from "@/constants/enum/EventPreviewType";
import Event from "@/models/api/models/Event";

import AutoSizedImage from "@/components/_CommonElements/AutoSizedImage"
import { EnumLocale } from "@/constants/enum/Locale";
import styles from "@/components/Event/Event/event.module.scss";

interface EventDisplayProps {
    event: Event
    end?: boolean
}

const EventDisplay = (props: EventDisplayProps) => {
    const { event } = props;
    const { t: commonTranslation } = useTranslation(EnumTranslationJson.Common);
    const router = useRouter();

    const { locale } = router;

    const banners = event.previewList.filter(preview => preview.position === EnumEventPreviewPosition.THUMBNAIL);
    const banner = useMemo(() => {
        return banners.length > 0 ? banners.find((banner) => banner.previewType === EventPreviewType.JPEG) : undefined;
    }, [banners]);
    const url = useMemo(() => 
        event.eventCategory?.toUpperCase() === "EXPO" ? `/event/${event.eventId}` : `/campaign/${event.eventId}`, [event.eventCategory, event.eventId]);
    const Clickable = useMemo(() => event.isListDetails || !FRONTEND.IS_PRODUCTION, [event.isListDetails]);
    const eventStartDateTime_LocalTime = useMemo(() => dayjs.utc(event.eventStartDateTime).tz(event.eventTimeZone), [event.eventStartDateTime, event.eventTimeZone]);
    const eventEndDateTime_LocalTime = useMemo(() => dayjs.utc(event.eventEndDateTime).tz(event.eventTimeZone), [event.eventEndDateTime, event.eventTimeZone]);
    const eventDateTimeDisplay = useMemo(() => {
        if (locale === EnumLocale.English) return eventStartDateTime_LocalTime.locale(EnumLocale.English).format("DD MMM, YYYY") + ' - ' + eventEndDateTime_LocalTime.locale(EnumLocale.English).format("DD MMM, YYYY");
        return eventStartDateTime_LocalTime.format("ll") + ' - ' + eventEndDateTime_LocalTime.format("ll");
    }, [eventEndDateTime_LocalTime, eventStartDateTime_LocalTime, locale]);
    const eventLocation = useMemo(() => {
        const locationName = event.eventLocation.replaceAll(' ', '').toLowerCase();
        return (/^[a-zA-Z]+(\.[a-zA-z]+)?$/).test(locationName) ? commonTranslation(`location.${locationName}`) : locationName;
    }, [commonTranslation, event.eventLocation]);

    const Content = useMemo(() => {
        return (
            <>
                <AutoSizedImage
                    src={banner?.previewContent}
                    ratioWxH="16x9"
                    alt={banner?.eventPreviewName}
                />
                <CardContent className={styles.cardContentContainer}>
                    <Chip label={eventLocation} size="small" />
                    <Typography variant="subtitle2" className={styles.eventDate}>{eventDateTimeDisplay}</Typography>
                    <div className={styles.cardContent}>{ event.eventName }</div>
                </CardContent>
            </>
        );
    }, [banner?.eventPreviewName, banner?.previewContent, commonTranslation, event.eventLocation, event.eventName, eventDateTimeDisplay, locale]);
    return (
        <Card elevation={2}>
            { Clickable ?
                <Tooltip title={event.eventName}>
                    <CardActionArea className={styles.cardActionArea} LinkComponent={Link} href={url}>
                            {Content}
                    </CardActionArea>
                </Tooltip>:
                Content
            }
        </Card>
    );
};

export default EventDisplay;