import styles from "@/components/_CommonElements/Display/DetailMore/DetailMore.module.scss";
import classNames from "classnames";
import { ReactNode } from "react";

interface DetailMoreProps {
    className?: string;
    children: ReactNode;
}

const DetailMore = (props: DetailMoreProps) => {
    const {
        className,
        children
    } = props;

    return (
        <div className={classNames(className, styles.detailExpand)}>
            <input type="checkbox" id="detailExpandController" />
            <label htmlFor="detailExpandController"></label>
            <div className={styles.detailExpandContent}>
                {children}
            </div>
        </div>
    );
};

export default DetailMore;