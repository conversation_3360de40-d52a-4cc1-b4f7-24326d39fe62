import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import { useTranslation } from "next-i18next";
import { useCallback, useEffect, useState } from "react";

import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useReservableSession } from "@/hooks/MembershipReservation/ReservableSession";
import ReserveSessionAPIResult from "@/models/api/result/reserve/reserveSession";
import API from "@/utils/API";
import { CircularProgress } from "@mui/material";
import { useSnackbar } from "notistack";
import dayjs from "dayjs";

interface ReserveConfirmDialogProps {
    pickedDate?: Date;
    pickedDateSessionId?: string;
    open: boolean;
    onClose: () => void;
}

const ReserveConfirmDialog = (props: ReserveConfirmDialogProps) => {
    const { pickedDate, pickedDateSessionId, open, onClose } = props;
    const [ isLoading, setIsloading ] = useState(false);
    const [ selectedSessionId, setSelectedSessionId ] = useState(pickedDateSessionId);
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const { postReservableSessionAsync } = useReservableSession();
    const { enqueueSnackbar } = useSnackbar();
    
    const handleClose = useCallback(() => {
        onClose();
    }, []);

    const handleAllow = useCallback(() => {
        void (async () => {
            console.log(selectedSessionId);
            console.log(pickedDateSessionId);
            setIsloading(true);
            try {
                if (pickedDateSessionId && pickedDateSessionId != "") {
                    const { data: responseData } : ReserveSessionAPIResult = await postReservableSessionAsync(pickedDateSessionId);
                    if (responseData) enqueueSnackbar(modalTranslation("modals.membershipReserveConfirm.apiResponse.success"), { variant: "success" });
                }
            }
            catch (error) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
            } finally {
                setIsloading(false);
                handleClose();
            }
        })();
    }, [pickedDateSessionId, enqueueSnackbar, handleClose]);

    useEffect(() => setSelectedSessionId(pickedDateSessionId), [pickedDateSessionId]);

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            disableEscapeKeyDown
        >
            <DialogTitle>
                {modalTranslation("modals.membershipReserveConfirm.title")}
            </DialogTitle>
            <DialogContent>
                <DialogContentText>
                    {modalTranslation("modals.membershipReserveConfirm.content", { "SELECTED_DATE": dayjs(pickedDate).format("YYYY-MM-DD") ?? "" })}
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button disabled={isLoading} onClick={handleClose} color="error">{modalTranslation("buttons.cancel")}</Button>
                <Button disabled={isLoading} onClick={handleAllow} autoFocus>
                    { isLoading ? <CircularProgress /> : modalTranslation("buttons.confirm") }
                </Button>
            </DialogActions>
        </Dialog>
    );
};

ReserveConfirmDialog.displayName = "ReserveConfirmDialog";
export default ReserveConfirmDialog;