@use "sass:math";
@use "sass:map";

@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;

$container-height: 40px;
$padding: 15px;
$letter-spacing: 2px;
$border-width: 2px;

$primary-color: map.get(theme.$color, "primary");
$secondary-color: map.get(theme.$color, "secondary");
$background-color: map.get(theme.$color, "background");
$text-color: map.get(theme.$color, "text");
$text-color-contrast: map.get(theme.$color, "contrast-text");
$text-color-disabled: map.get(theme.$color, "text-disabled");

@mixin letter-spacing() {
  letter-spacing: $letter-spacing;
  display: inline-block;
  transform: translate($letter-spacing * 0.5, 1px);
}

button.container {
  color: $text-color;
  position: relative;
  background-color: transparent;
  padding: 0 $padding;
  border-radius: $container-height * 0.5;
  transition: all animations.$fast-animation-speed;
  word-break: keep-all;
  white-space: nowrap;
  margin: 5px 5px;
  flex-shrink: 0;

  & [class~="MuiButton-startIcon"] {
    margin: 0 2px 0 0;
  }

  & [class~="MuiButton-endIcon"] {
    margin: 0 0 0 2px;
  }

  &.small {
    font-size: 0.8rem;
    height: #{$container-height * 0.875};
  }

  &.medium {
    font-size: 1rem;
    height: $container-height;
  }

  &.large {
    font-size: 1.2rem;
    height: $container-height * 1.2;
    padding: 0 ($padding * 1.1);
    border-radius: $container-height * 0.5;
  }

  &.extra {
    font-size: 20px;
    height: $container-height * 1.4;
    padding: 0 ($padding * 1.25);
    border-radius: $container-height * 0.7;
  }

  & span {
    &.letterSpacing {
        display: flex !important;
        justify-content: center;
        align-items: center;
        @include letter-spacing;
    }
  }

  &.outlined {
    background-color: white;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: {
        width: $border-width;
        style: solid;
        radius: $container-height * 0.5;
      }
    }

    &.large,
    &.extra {
      &::before {
        border-radius: 12px;
      }
      &:hover {
        border-radius: 12px;
      }
    }

    &.color-primary {
      &::before {
        border-color: $primary-color;
        // filter: drop-shadow(-2px 3px 2px $primary-color);
      }
      &:hover {
        box-shadow: 0 0 10px $primary-color;
        transition-delay: animations.$fast-animation-speed * 0.2;
        background-color: $primary-color;
        color: $text-color-contrast;
      }
    }

    &.color-secondary {
      &::before {
        border-color: $secondary-color;
        filter: drop-shadow(-2px 3px 2px $secondary-color);
      }
      &:hover {
        box-shadow: 0 0 10px $secondary-color;
        transition-delay: animations.$fast-animation-speed * 0.2;
        background-color: $secondary-color;
      }
    }

    // &[class~="Mui-disabled"] {
    //   color: $text-color-disabled;
    // }
  }

  &.contained {
    &.color-primary {
      background-color: $primary-color;
      color: $text-color-contrast;

      &:hover {
        box-shadow: 0 0 15px $primary-color;
      }
    }

    &.color-secondary {
      background-color: $secondary-color;

      &:hover {
        box-shadow: 0 0 15px $secondary-color;
      }
    }
  }

  @include viewport.within("mobile") {
    padding: 0 12px;
  }
}
