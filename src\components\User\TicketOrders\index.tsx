import styles from "@/components/User/TicketOrders/orders.module.scss";

import { useTranslation } from "next-i18next";
import { Alert } from "@mui/material";
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';

import OrderDetails from "@/components/User/TicketOrders/details";
import {OrderType} from '@/constants/enum/OrderType';
import { UserOrder } from "@/models/api/models/UserOrder";

import Pagination from "@/components/_CommonElements/Pagination";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";

interface OrdersProps {
  userOrders: UserOrder[];
  listType: string;  
  pageNum: number;
  maxPageNum: number;
  setPageNum: (pageNum: number) => void;
}

const TicketOrders = (props: OrdersProps) => {
    const { t: commonTranslation } = useTranslation(EnumTranslationJson.Common);
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
    const {
        userOrders,
        listType,
        pageNum,
        maxPageNum,
        setPageNum,
    } = props;

  let emptyMessage: string;

  switch (listType) {
    case OrderType.COMPLETED:
      emptyMessage = commonTranslation("noRecord");
      break;
    case OrderType.PROCESSING:
      emptyMessage = commonTranslation("noRecord");
      break;
    case OrderType.PAID:
      emptyMessage = commonTranslation("noRecord");
      break;
    case OrderType.PENDING:
      emptyMessage = commonTranslation("noRecord");
      break;
    case OrderType.EXPIRED:
    emptyMessage = commonTranslation("noRecord");
    break;
    default:
      return null;
  }

  return (
  <>
    <Alert severity="info">{profileTranslation("order.reminder")}</Alert>
    <List className={styles.list} sx={{ width: '100%', minWidth: 360, bgcolor: 'background.paper' }} >

      {!userOrders.length && 
        <ListItem className={styles.emptyRecord}>
          <h2>{emptyMessage}</h2>
        </ListItem>
      }

      {OrderDetails && userOrders.map((each, index:number) => {
        return <OrderDetails key={index} order={each}/>
      })}
    </List>
    {maxPageNum > 1 &&
      <div className={styles.pagination}>
        <Pagination currentPage={pageNum} maxPage={maxPageNum} onClick={setPageNum}/>
      </div>
    }
  </>);
};

export default TicketOrders;