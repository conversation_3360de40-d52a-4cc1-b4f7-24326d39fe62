import type { GetServerSideProps } from "next";
import { useMemo, useState } from "react";
import { ProfileSectionWrapper } from "@/components/User/ProfileSections";
import EventList from "@/components/User/EventList";
import { LoadingCircle } from "@/components/_CommonElements/LoadingBar";
import { EventListType } from "@/constants/enum/EventListType";
import serverSideAuth from "@/utils/serverSideAuth";
import i18n from "@/utils/i18n";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import UserEventTicketsAPIResult from "@/models/api/result/user/tickets";
import ENDPOINT from "@/models/api/endpoint";
import { EventType } from "@/constants/enum/EventType";
import { BACKEND } from "@/constants/config";
import Tabs from "@/components/_CommonElements/Tabs";
import PageContent from "@/components/_PageComponents/PageContent";

interface TabContentProps {
    type: EventType
}
const TabContent = ({ type }: TabContentProps) => {
    // Deprecated
    // const [pageNum, setPageNum] = useState(1);
    // const userUpcomingEventsResponse = BACKEND.Gateway.useQuery<UserEventTicketsAPIResult>({
    //     url: `${ENDPOINT.GetUserEventsByTicketStatus()}/purchased/${type}`,
    //     query: {
    //         page: pageNum.toString(),
    //     },
    //     params: {
    //         queryKey: `user_purchased_bounded_tickets-${type}-${pageNum.toString()}`,
    //     },
    // });
    // const data = userUpcomingEventsResponse.data?.data;
    // const maxPageNum = data?.maxPage || 0;

    // if (!data) {
    //     return <LoadingCircle />;
    // }
    // return (
    //     <EventList
    //         userEvents={data.list}
    //         listType={EventListType.PURCHASED}
    //         pageNum={pageNum}
    //         maxPageNum={maxPageNum}
    //         setPageNum={setPageNum}
    //     />
    // );
    return null;
}

const UnboundTicketsPage = () => {
    const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
    const tabs = useMemo(() => [
        {
            name: profileTranslation("event.active"),
            contents: <TabContent type={EventType.ACTIVE} />,
            active: true
        },
        {
            name: profileTranslation("event.upComing"),
            contents: <TabContent type={EventType.UPCOMING} />
        },
        {
            name: profileTranslation("event.end"),
            contents: <TabContent type={EventType.END} />
        }
    ], [profileTranslation]);
    const title = useMemo(() => seoTranslation("page.unboundTickets.title"), [seoTranslation]);
    return (
        <PageContent
            title={title}
            content={
                <ProfileSectionWrapper>
                    <Tabs tabs={tabs} />
                </ProfileSectionWrapper>
            }
        />
    );
};

export const getServerSideProps: GetServerSideProps = serverSideAuth(
    {
        permission: "userOnly",
    },
    async (context) =>
        i18n.GetServerSidePropsAsync({
            additionalFiles: [
                EnumTranslationJson.Account,
                EnumTranslationJson.Profile,
                EnumTranslationJson.Redeem,
            ],
            context,
        })
);

export default UnboundTicketsPage;
