import * as cookie from "cookie";

const createRedirectObject = (destination: string) => {
  return {
    redirect: {
      permanent: false,
      destination: destination,
    },
  };
};

enum EnumRedirectMode {
  NONE = 1,
  REDIRECT_GUEST = 2,
  REDIRECT_USER = 3,
}

interface PageAccessCheckerProps {
  cookies?: string;
  redirectMode: EnumRedirectMode;
  redirectUrl?: string;
}

class PageAccessChecker {
  private _redirectMode: EnumRedirectMode;
  private _redirectUrl: string;
  public jwt: string;
  public isLoggedIn: boolean;

  constructor(props: PageAccessCheckerProps) {

    const {
      cookies,
      redirectMode,
      redirectUrl,
    } = props;

    this._redirectMode = redirectMode;
    if (redirectUrl === undefined) {
      this._redirectUrl =
        redirectMode === EnumRedirectMode.REDIRECT_GUEST ? "/login" : "/";
    } else {
      this._redirectUrl =
        redirectMode === EnumRedirectMode.REDIRECT_GUEST
          ? `/login?returnTo=${redirectUrl}`
          : redirectUrl;
    }

    if (!cookies) {
      this.jwt = "";
      this.isLoggedIn = false;
    } else {
      const { jwt } = cookie.parse(cookies);
      this.jwt = jwt;
      this.isLoggedIn = Boolean(jwt);
    }
  }

  get redirectObject() {
    switch (this._redirectMode) {
      case EnumRedirectMode.REDIRECT_GUEST:
        if (!this.isLoggedIn) {
          return createRedirectObject(this._redirectUrl);
        }
        break;
      case EnumRedirectMode.REDIRECT_USER: {
        if (this.isLoggedIn) {
          return createRedirectObject(this._redirectUrl);
        }
      }
    }
    return undefined;
  }
}

export { EnumRedirectMode };
export default PageAccessChecker;
