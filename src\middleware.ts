import { FRONTEND } from "@/constants/config";
import { NextRequest, NextResponse } from "next/server";
import { EnumLocale } from "./constants/enum/Locale";
import { EnumCookieKey } from "./constants/enum/Cookies";

const PUBLIC_FILE = /\.(.*)$/;
const SUSPECT_LOCALE_FORMAT = /(^[a-zA-Z]{1,2}$)|(^[a-zA-Z]{2}-[a-zA-Z]{0,2}$)/;
const SECRET_KEY_PARAM_NAME = "secret";

const SHOULD_CHECK_ACCESS_KEY = FRONTEND.SHOULD_CHECK_ACCESS_KEY;

interface NextResponseWithCookiesProps {
    cookies?: { [key: string]: string };
    nextRes: NextResponse;
}

const NextResponseWithCookies = (props: NextResponseWithCookiesProps) => {
    const { cookies, nextRes } = props;
    if (!cookies) {
        return nextRes;
    }
    for (const key in cookies) {
        nextRes.cookies.set(key, cookies[key], {
            path: "/",
            maxAge: 7 * 24 * 60 * 60 * 1000,
        });
    }
    return nextRes;
};

const NextResponseWithSecretCookie = (response: NextResponse) => {
    return NextResponseWithCookies({
        nextRes: response,
        cookies: SHOULD_CHECK_ACCESS_KEY
            ? {
                [SECRET_KEY_PARAM_NAME]: FRONTEND.ACCESS_KEY!,
            }
            : undefined,
    });
};

interface CheckAccessKeyProps {
    searchParams: URLSearchParams;
    cookies: NextRequest["cookies"];
    alwaysReturnNextResponse?: boolean;
}

const isAccessKeyValid = (props: CheckAccessKeyProps) => {
    const { searchParams, cookies } = props;
    const accessKey = FRONTEND.ACCESS_KEY;
    // get secret from query
    let search;
    if (searchParams) {
        search = searchParams.get(SECRET_KEY_PARAM_NAME);
    }
    // get secret from cookie
    let cookie;
    if (cookies) {
        const cookieObj = cookies.get(SECRET_KEY_PARAM_NAME);
        if (cookieObj) {
            cookie = cookieObj.value;
        }
    }
    const inputKey: string = search || cookie || "";
    return inputKey === accessKey;
};

interface CheckFromSourceProps {
    searchParams: URLSearchParams;
    cookies: NextRequest["cookies"];
    nextRes: NextResponse;
}
const NextResponseHandleFromSource = (props: CheckFromSourceProps) => {
    const { searchParams, cookies, nextRes } = props;
    // get secret from query
    let fromSource = "";
    if (searchParams) {
        fromSource = searchParams.get("s") ?? "";
    }
    // get secret from cookie
    let cookie = "";
    if (cookies) {
        cookie = cookies.get(EnumCookieKey.TRACED_REFERRAL_CODE)?.value ?? "";
    }
    if (nextRes && cookie === "" && fromSource !== "") {
        nextRes.cookies.set(EnumCookieKey.TRACED_REFERRAL_CODE, fromSource);
    }
    return nextRes;
};

const middleware = (req: NextRequest) => {
    const { nextUrl, cookies } = req;
    const { pathname, searchParams, locale } = nextUrl;

    if (
        PUBLIC_FILE.test(pathname) ||
        pathname.startsWith("/api/") ||
        pathname.startsWith("/_next/image/")
    ) {
        return NextResponse.next();
    }
    // Execute after checking static assets, otherwise it will causes 500 error (due to path issue)
    if (SHOULD_CHECK_ACCESS_KEY) {
        if (!isAccessKeyValid({ searchParams, cookies })) {
            return NextResponse.redirect(`https://incutix.com/`);
        }
    }
    if (locale !== "_default") {
        return NextResponseWithSecretCookie(NextResponse.next());
    }

    const originalPathname = pathname;
    const originalPathnameCropped = (() => {
        const _originalPathnameSplit = originalPathname.split("/");
        _originalPathnameSplit.splice(1, 1);
        return _originalPathnameSplit.join("/");
    })();

    const shouldReplaceLocale = SUSPECT_LOCALE_FORMAT.test(
        originalPathname.split("/")[1]
    );

    const redirectPathname = shouldReplaceLocale
        ? originalPathnameCropped
        : originalPathname;

    const preferredLanguage = cookies.get(EnumCookieKey.PREFERRED_LANGUAGE)?.value || EnumLocale.English;
    const response = NextResponse.redirect(
        `${nextUrl.origin}/${preferredLanguage}${redirectPathname}${nextUrl.search}`
    );

    return NextResponseHandleFromSource({ searchParams, cookies, nextRes: NextResponseWithSecretCookie(response) });
};

export default middleware;
