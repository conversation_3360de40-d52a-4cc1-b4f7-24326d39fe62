/** @type {import('next-sitemap').IConfig} */
var nextConfig = require("./next-i18next.config");
var siteUrl = "https://incutix.com";
var additionalSitemaps = [
    "events-sitemap.xml"
];
var exludePages = [
    "500",
    "user/*",
    "verify/*",
    "purchaseResult"
].map(page => `*/${page}`).concat(additionalSitemaps.map(sitemap => `/${sitemap}`));

var includeLanguages = nextConfig.i18n.locales.filter(locale => locale !== "_default");

module.exports = {
    siteUrl,
    generateRobotsTxt: true,
    generateIndexSitemap: false,
    changefreq: "daily",
    priority: 1.0,
    exclude: exludePages,
    robotsTxtOptions: {
      policies: [{ userAgent: "*", disallow: exludePages }],
      includeNonIndexSitemaps: true,
      additionalSitemaps: additionalSitemaps.map(sitemap => `${siteUrl}/${sitemap}`)
    },
    alternateRefs: includeLanguages.map(language => ({
        href: `${siteUrl}/${language}`,
        hreflang: language
    })),
  };
  