import Navigation from "@/components/Layouts/Navigation";
import styles from "./header.module.scss";
import classNames from "classnames";

import {
    ScrollHeader,
    LowBar,
    MobileHeader,
} from "@stoneleigh/scroll-header";

import LanguageSwitcher from "@/components/_PageComponents/LanguageSwitcher";
import SiteLogo from './SiteLogo';
import { useTheme } from "@mui/material/styles";
import { useMediaQuery } from "@mui/material";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";

const Header = () => {
    const theme = useTheme();
    const matches = useMediaQuery(theme.breakpoints.down("lg"));
    const { route } = useRouter();
    const [isKioskMode, setIsKioskMode] = useState(false);

    useEffect(() => {
        if (route.includes('/kiosk')) {
            setIsKioskMode(true);
        }
    }, [route]);

    return (
        <>
            {matches == true ? (
                <MobileHeader styleName={styles.header}>
                    <LowBar>
                        <SiteLogo />
                        <div className={styles.right}>
                            <Navigation.Toggle />
                        </div>
                    </LowBar>

                    {!isKioskMode && 
                        <Navigation.Overlay>
                            <div className={styles["flexBox-1"]} />
                            <Navigation.Menu direction="vertical" />
                            <div className={styles["flexBox-2"]} />
                            { /* <Navigation.ShareBox /> */ }
                            <div className={styles["flexBox-3"]} />
                            <LanguageSwitcher.Vertical />
                        </Navigation.Overlay>
                    }
                </MobileHeader>
            ) : (
                <ScrollHeader
                    styleName={classNames(styles.header, "scroll-header")}
                >
                    <LowBar styleName={styles.bar}>
                        <SiteLogo clickable={!isKioskMode} />

                        {!isKioskMode && 
                            <div className={styles.contentbox}>
                                <Navigation.Menu direction="horizontal" />
                                <LanguageSwitcher.Horizontal />
                            </div>
                        }
                    </LowBar>
                </ScrollHeader>
            )}
        </>
    );
};

export default Header;