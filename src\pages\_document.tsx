import * as React from 'react';
import Document, { Html, Head, Main, NextScript, DocumentContext, DocumentProps } from 'next/document';
import createEmotionServer from '@emotion/server/create-instance';
import createEmotionCache from "@/styles/mui/createEmotionCache";
import { FRONTEND } from '@/constants/config';
import { DocumentInitialProps } from "next/document";

interface CustomDocumentProps {
    emotionStyleTags: React.JSX.Element[];
    locale?: string;
}
const MyDocument = (props: CustomDocumentProps & DocumentProps) => {
    return (
        <Html lang="en">
            <Head>
                {/* Tell the browser to never restore the scroll position on load */}
                <script
                    dangerouslySetInnerHTML={{
                    __html: `history.scrollRestoration = "manual"`,
                    }}
                />
                <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
                <link rel="dns-prefetch" href="https://www.googletagmanager.com" />
                <link rel="dns-prefetch" href="https://connect.facebook.net" />
                <link rel="preconnect" href="https://fonts.gstatic.com" />
                <link rel="dns-prefetch" href="https://site-assets.fontawesome.com" />

                {/* Inject MUI styles first to match with the prepend: true configuration. */}
                <meta name="emotion-insertion-point" content="" />
                {props.emotionStyleTags}

                <script
                    async
                    src={`https://www.googletagmanager.com/gtag/js?id=${FRONTEND.GOOGLE_ANALYTICS_KEY}`}
                />

                <script
                    dangerouslySetInnerHTML={{
                        __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${FRONTEND.GOOGLE_ANALYTICS_KEY}', {
              page_path: window.location.pathname,
            });
          `,
                    }}
                />
            </Head>
            <body>
                <Main />
                <NextScript />
            </body>
        </Html>
    );
};


MyDocument.getInitialProps = async (context: DocumentContext): Promise<DocumentInitialProps & CustomDocumentProps> => {
    const { locale } = context;
    const originalRenderPage = context.renderPage;

    const cache = createEmotionCache();
    // eslint-disable-next-line @typescript-eslint/unbound-method
    const { extractCriticalToChunks } = createEmotionServer(cache);
  
    context.renderPage = () => (
        originalRenderPage({
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            enhanceApp: (App: any) => (
                function EnhanceApp(props) {
                    return <App emotionCache={cache} {...props} />;
                }
            )
        })
    );

    const initialProps = await Document.getInitialProps(context);
    // This is important. It prevents emotion to render invalid HTML.
    // See https://github.com/mui-org/material-ui/issues/26561#issuecomment-855286153
    const emotionStyles = extractCriticalToChunks(initialProps.html);
    const emotionStyleTags = emotionStyles.styles.map((style) => (
        <style
            data-emotion={`${style.key} ${style.ids.join(" ")}`}
            key={style.key}
            // eslint-disable-next-line react/no-danger
            dangerouslySetInnerHTML={{ __html: style.css }}
        />
    ));

    return {
        ...initialProps,
        emotionStyleTags,
        locale,
    };
};
export default MyDocument;