buttons:
  submit: 提交
  confirm: 確定
  cancel: 取消
  close: 關閉
resendAfter: "{{SEC}} 秒"
obtainCode: "獲取"
modals:
  login:
    content:
      welcomeBackMessage: 歡迎回來, {{USERNAME}} !
  emailVerification:
    title: 電郵驗證
    content:
      codeLabel: 驗證碼
      codeEmailSent: 驗證碼正發送至您的電郵信箱
      reminder: |
        請點擊「獲取」以獲取驗證碼，然後在您註冊的電子郵件地址中尋找驗證電子郵件。您需要輸入6位數字的代碼 及按「確定」。或在「帳戶資料」下的「驗證電郵」中進行電郵驗證。
        (注意，若沒收到驗證碼電郵，請檢查垃圾郵件。)
  forgotPassword:
    title: 忘記密碼
  changePassword:
    title: 更改密碼
    content:
      oldPasswordLabel: 舊密碼
      newPasswordLabel: 新密碼
      confirmNewPasswordLabel: 確認新密碼
  admissionQrcode:
    title: 入場
    content:
      reminder: 請向職員展示二維碼
  clearShoppingCart_userrequest:
    title: 注意！您正在清空購物車中的商品
    content: 此後，購物車中的所有商品將被移除，您將無法恢復它們。
  clearShoppingCart_systemrequest:
    title: 注意！每張訂單只能購買相同活動中的商品
    content: 若你加入此活動商品，關於上一個活動的商品將會從購物車移除。
  promoCode:
    title: 使用推廣碼
    content:
      promoCode: 推廣碼
      helper: (如適用) 請填寫
      promoCodeApply: 套用
      validation:
        missingCode: 請在套用前填入推廣碼
  kioskPaymentMethod:
    title: 請選擇付款方法
    methods:
      creditCard: 信用卡（Visa/Mastercard/銀聯）
      alipay: 支付寶
      wechatPay: 微信支付
      fps: 轉數快
  membershipReserveConfirm:
    title: 確認預約日期
    content: 您已選擇預約 {{SELECTED_DATE}}
    apiResponse:
      success: 恭喜! 您已成功預約。
  membershipReserveCancel:
    title: 確認取消預約
    apiResponse:
      success: 您已取消預約。