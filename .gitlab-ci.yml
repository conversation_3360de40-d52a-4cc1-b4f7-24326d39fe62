include:
  - local: functions.yml
  - local: variables.yml
  
variables:
  CI_NAME: CI/CD Runner
  CI_EMAIL: <EMAIL>
  CI_USER: oelasolutions.gitlab.deployment
  CI_USER_ACCESS_TOKEN: **************************
  CI_GIT_URI: **************:${CI_PROJECT_PATH}.git
  DEPLOYMENT_SSH_PRIVATE_KEY: $DEPLOYMENT_SSH_PRIVATE_KEY

stages:
  - test
  - bump
  - prepare
  - build
  - deploy
  - backup

#
#unit-test: missing
#
bump-job:
  stage: bump
  image: node:lts-alpine
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  before_script:
    - !reference [.setup_yarn, script]
    - !reference [.setup_git, script]
  script:
    - git checkout main
    - yarn dlx standard-version --release-as patch --quiet --skip.tag
    - git push --set-upstream origin main --push-option=ci.skip

prepare-job:
  stage: prepare
  image: node:lts-alpine3.19
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^releases\//'
  before_script:
    - !reference [.install_aws, script]
    - !reference [.aws_add_ecr_profile, script]
  script:
    - BUILD_VERSION=$(node -p "require('./package.json').version")
    - echo "Login to ECR Region [${ECR_AWS_REGION}]..."
    - ECR_PASSWORD=$(aws ecr get-login-password --region $ECR_AWS_REGION --profile $ECR_AWS_PROFILE)
    - MULTISTAGE_ENVIRONMENT_LOWERCASE=${CI_COMMIT_BRANCH#releases/}
    - MULTISTAGE_ENVIRONMENT_UPPERCASE=$(echo $MULTISTAGE_ENVIRONMENT_LOWERCASE | tr "[:lower:]" "[:upper:]")
    - ECR_FULL_PATH="${ECR_DOMAIN}/${ECR_REPO_NAME}-${MULTISTAGE_ENVIRONMENT_LOWERCASE}:v${BUILD_VERSION}"
    - echo "BUILD_VERSION=$BUILD_VERSION" >> build.env
    - echo "ECR_PASSWORD=$ECR_PASSWORD" >> build.env
    - echo "MULTISTAGE_ENVIRONMENT_LOWERCASE=$MULTISTAGE_ENVIRONMENT_LOWERCASE" >> build.env
    - echo "MULTISTAGE_ENVIRONMENT_UPPERCASE=$MULTISTAGE_ENVIRONMENT_UPPERCASE" >> build.env
    - echo "ECR_FULL_PATH=$ECR_FULL_PATH" >> build.env
  artifacts:
    reports:
      dotenv: build.env

build-job:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^releases\//'
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - echo ${ECR_PASSWORD} | docker login --username AWS --password-stdin ${ECR_DOMAIN} 
  script:
    - echo "Building and Pushing Image ${ECR_FULL_PATH}..."
    - docker context create tls-environment
    - docker buildx create --name multiarch-builder --driver docker-container --use tls-environment
    - docker buildx build
        --push
        --platform linux/amd64
        --build-arg EASYLIVE_CODEARTIFACT_AWS_ACCESS_KEY_ID="$DOCKER_AWS_ACCESS_KEY_ID"
        --build-arg EASYLIVE_CODEARTIFACT_AWS_SECRET_ACCESS_KEY="$DOCKER_AWS_SECRET_ACCESS_KEY"
        --build-arg PROJECT_ENV="$MULTISTAGE_ENVIRONMENT_UPPERCASE"
        -t $ECR_FULL_PATH .
    - echo "Build and Push image ${ECR_FULL_PATH} successful !"
  needs: [prepare-job]

deployment-job:
  stage: deploy
  image: alpine/k8s:1.22.13
  variables:
    K8S_WORKLOAD_NAME: "${JOB}-${MULTISTAGE_ENVIRONMENT_LOWERCASE}"
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^releases\//'
  parallel:
    matrix:
      - JOB: "enduserweb"
  before_script:
    - !reference [.install_aws, script]
    - !reference [.aws_login_k8s_cluster, script]
    - |
      if [[ $MULTISTAGE_ENVIRONMENT_LOWERCASE =~ ^(dev|sit|uat)$ ]]; then
        K8S_NAMESPACE_NAME="${K8S_BASIC_NAMESPACE_NAME}-private"
      fi
      if [[ $MULTISTAGE_ENVIRONMENT_LOWERCASE =~ ^(preprd|prd)$ ]]; then
        K8S_NAMESPACE_NAME="${K8S_BASIC_NAMESPACE_NAME}-public"
      fi
  script:
    - echo Deploy Deployment [${K8S_WORKLOAD_NAME}] with Image [$ECR_FULL_PATH] in [${K8S_NAMESPACE_NAME}]
    - kubectl set image deployment/${K8S_WORKLOAD_NAME} ${K8S_WORKLOAD_NAME}=${ECR_FULL_PATH} -n ${K8S_NAMESPACE_NAME}
  needs: [prepare-job, build-job]

backup-job:
  stage: backup
  image: node:lts-alpine
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^releases\//'
  variables:
    GIT_TARGET_BRANCH: $CI_COMMIT_BRANCH
    GIT_TAG_PREFIX: $MULTISTAGE_ENVIRONMENT_LOWERCASE
  before_script:
    - !reference [.setup_yarn, script]
    - !reference [.setup_git, script]
  script:
    - !reference [.push_git_tag, script]
  needs: [prepare-job, deployment-job]