@use "@/styles/utils/viewport.module.scss" as viewport;

.button {
    background-color: #FF7800;
    width: 60px;
    height: 60px;
    border-radius: 50% !important;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 25px;
    box-shadow: 0 0 1rem rgba(0, 0, 0, 0.3);
    &.mobile {
        @include viewport.within("mobile") {
            display: none;
        }
    }
    &:hover {
        background: #FF7800 !important;
        box-shadow: 0 0 1rem #ff9231;
    }
    &.contained {
        background: #FF7800;
        color: white;
    }
    &.outline {
        border: 1px solid #FF7800;
        background: white;
        color: black;
    }
}