import RouteMap from "@/constants/config/RouteMap";
import Link from "next/link";
import styles from "./siteLogo.module.scss";

interface SiteLogoProps {
    clickable?: boolean;
}

const SiteLogo = (props: SiteLogoProps) => {
    const {
        clickable = true
    } = props;
    return (
        <figure className={styles.headerLogo}>
            {clickable ? 
                <Link aria-label="Home Page" href={RouteMap.Main}>
                    <img alt="Website Logo" className={styles.logo} src="https://assets.incutix.com/logo.png" />
                </Link>
                :
                <img alt="Website Logo" className={styles.logo} src="https://assets.incutix.com/logo.png" />
            }
            <div className={styles.poweredClaim}>
                <img alt="powered by EASYLiVE" src="https://assets.incutix.com/easylive_claim_Logo-01.png" />
            </div>
        </figure>
    );
};
export default SiteLogo;