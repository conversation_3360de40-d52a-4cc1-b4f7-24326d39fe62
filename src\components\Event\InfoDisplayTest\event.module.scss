@use "sass:map";
@use "sass:math";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;
@use "@/styles/utils/viewport.module.scss" as viewport;
@import "rfs/scss";

$primary-color: map.get(theme.$color, "primary");
$secondary-color: map.get(theme.$color, "secondary");
$text-color: map.get(theme.$color, "text");
$text-color-contrast: map.get(theme.$color, "contrast-text");
$border-radius: map.get(theme.$border, "radius");

.container {
    margin: 0 auto;
    width: 95%;
    max-width: 900px;
    color: $text-color;
    display: flex;
    flex-direction: column;
    gap: 30px;
    position: relative;
}

.bannerBackground {
    background-color: transparent;
    user-select: none;
}

.banner {
    display: block;
    max-height: 500px;
    margin: 0 auto;
    width: 100%;
    object-fit: contain;
}

.videoPlayerWrapper {
    display: flex;
    justify-content: center;
    justify-items: center;
    width: 100%;
    min-height: 360px;
}


.field {
    &.border {
        border-radius: map.get(theme.$border, "radius");
        border: map.get(theme.$presets, "light-border");
        padding: 15px;
    }
}

.promo {
    position: relative;
    width: 100%;

    &:before {
        content: "";
        width: 100%;
        padding-top: 56.25%;
        display: block;
    }

    & iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}

.eventTitle {
    text-align: center;
    align-items: center;
    justify-content: center;
}

.basicInfoTable {
    display: flex;
    flex-direction: column;
    gap: 10px;

    & .record {
        display: inline-flex;
        align-items: center;
    }

    & .priceBadge {
        border-radius: 0.6rem;
        color: #000000;
        padding: 0.2rem 0.5rem;
        border: 1px solid #ff7802;
        font-weight: 500;
    }

    & .rateCard {
        color: #000000;
        padding: 0 0.5rem;
        font-weight: 500;
        background-color: inherit;
        height: fit-content;
    }

    & .field {
        &.border {
            border-bottom: 0 none;
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }
    }
}

div.help {
    display: flex;
    justify-content: space-evenly;
    margin: 10px 0;

    &>div {
        flex-grow: 1;
        width: 50%;
        padding: 0 20px;

        @include viewport.within("mobile") {
            width: 100%;
        }
    }

    @include viewport.within("mobile") {
        flex-direction: column;
    }
}

button.back {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 40px 20px 40px 5px;
    color: $text-color;
    font-size: 20px;
    border: 0;
    background-color: transparent;
    cursor: pointer;
    transition: color animations.$fast-animation-speed;

    &:hover {
        color: $primary-color;
    }
}

a.link {
    color: $primary-color;
    display: inline-block;
    cursor: pointer;

    &:hover {
        text-decoration: underline;
        transition: all 0.2s;
    }
}

.purchaseArea {
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .stepContainer {
        max-width: 700px;
        width: 80vw;

        @include viewport.within("tablet") {
            width: 100%;
            max-width: initial;
        }

        .step {
            text-align: start;
            padding: 0 0 0 10px;
            margin-bottom: 18px;
            font-weight: bold;
            border-left: 6px solid rgb(1, 175, 199);
        }
    }
}

.calendar {
    & [class~="MuiPickersDay-root"] {
        &:hover {
            background-color: #ffead8 !important;
        }
        &:focus {
            color: white;
            background-color: #ff7802 !important;
        }
        &[class~="Mui-selected"] {
            color: white;
            background-color: #ff7802 !important;
        }
    }
    @include viewport.beyond("tablet") {
        width: 480px !important;
        max-height: 525px !important;
        & [class~="MuiDayCalendar-weekDayLabel"] {
            width: 54px;
            height: 54px;
        }
        & [class~="MuiPickersDay-root"] {
            width: 54px;
            height: 54px;
        }
        & [class~="MuiDayCalendar-slideTransition"] {
            min-height: 360px;
        }
    }
}
.dateAndQtyContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.selectBox {
    max-width: 650px;
    width: 75vw;
    @include viewport.within("tablet") {
        width: 90vw;
    }
}

.selectOption {
    white-space: pre-wrap !important;   
    &:hover {
        background-color: rgba(0, 0, 0, 0.1) !important;
    }
}

.buyBtn {
    margin: 0 auto;
    padding: 0 30px;

    & > button {
        border-top-right-radius: 0;
        border-top-left-radius: 0;
        border: 2px solid rgba(220, 220, 220, 0.5);
        border-top: 0;
    }
}

.stepLabel {
    $height: 32px;
    $letter-spacing: 2px;

    min-width: 64px;
    width: max-content;
    display: flex;
    align-items: center;
    height: $height;
    color: $text-color-contrast;
    font-weight: bold;
    padding: 3px #{15px - $letter-spacing} 3px 15px;
    border-radius: $height * 0.5;
    background-color: $primary-color;
    letter-spacing: $letter-spacing;
    margin: 10px 10px 10px 0;
    box-shadow: 0 0 5px $primary-color;

    &>span {
        display: block;
        transform: scale(1.1) translateY(1px);
        filter: drop-shadow(0px 0px 0.2px $text-color-contrast);
    }
}
.buyContainer {
    display: flex;
    justify-content: center;
    align-items: center;
}
.purcahseSectionContainer {
    position: relative;
    .disabledArea {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        pointer-events: all;
        cursor: not-allowed;
    }
    .purchaseSection {
        padding: 0 50px;
        @include viewport.within("tablet") {
            padding: 0;
        }
        &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        .purchaseBox {
            display: flex;
            flex-direction: column;
            gap: 10px;
            justify-content: center;
            margin: 0 auto;
            @include viewport.within("mobile") {
                width: 100%;
            };
            & .row {
                display: flex;
                flex-direction: row;
                gap: 10px;
                min-width: 100px;
                align-items: center;
                @include viewport.within("tablet") {
                    flex-direction: column;
                    align-items: flex-start;
                };
            }
        }
    }
}

.toBuy{
    width: 100%;
    text-align: center;

}
.floatBuybtn {
    position: fixed;
    right: 20px;
    bottom: 20px;
    background-color: #FF7800;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 25px;
    cursor: pointer;
    z-index: 999;
}