import React, { FunctionComponent, useCallback, useMemo } from "react";
import classNames from "classnames";
import styles from "@/components/_CommonElements/Button/MenuButton/menu.module.scss";
import TextButton from "@/components/_CommonElements/Button/TextButton";
import { ClickAwayListener, Menu, MenuItem, MenuItemProps } from "@mui/material";

interface MenuButtonProps {
  label?: string | React.ReactNode;
  size?: "small" | "medium" | "large";
  className?: string;
  dataSource: MenuItemProps[]
  variant?: "outlined" | "contained" | "text";
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

const MenuButton: FunctionComponent<MenuButtonProps> = (props) => {
  const {
    label,
    size,
    className,
    dataSource,
    variant = "contained",
    startIcon,
    endIcon,
  } = props;

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (anchorEl === null) {
        setAnchorEl(event.currentTarget);
    }
    else {
        setAnchorEl(null);
    }
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = useMemo(() => Boolean(anchorEl), [anchorEl]);
  const onItemClick = useCallback((e: React.MouseEvent<HTMLLIElement, MouseEvent>, item: MenuItemProps) => {
    if (item.onClick) {
        item.onClick(e);
        handleClose();
    }
  }, []);
  return (
    <div className={classNames(className, styles.wrapper)}>
        <ClickAwayListener onClickAway={handleClose}>
            <div>
                <TextButton
                    id="basic-button"
                    aria-controls={open ? 'basic-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={open ? 'true' : undefined}
                    onClick={handleClick}
                    label={label}
                    size={size}
                    variant={variant}
                    startIcon={startIcon}
                    endIcon={endIcon}
                />
                <Menu
                    anchorEl={anchorEl}
                    elevation={1}
                    open={open}
                    onClose={handleClose}
                    MenuListProps={{
                        'aria-labelledby': 'basic-button',
                    }}
                >
                    {dataSource.map((item, i) => {
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        const { onClick, ...otherItemProps } = item
                        return (
                            <MenuItem
                                key={i}
                                {...otherItemProps}
                                onClick={e => onItemClick(e, item)}
                            />
                        );
                    })}
                </Menu>
            </div>
        </ClickAwayListener>
    </div>
  );
};

export default MenuButton;
