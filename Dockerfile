FROM node:lts-alpine3.19 as builder

WORKDIR /app

ARG EASYLIVE_CODEARTIFACT_AWS_ACCESS_KEY_ID
ARG EASYLIVE_CODEARTIFACT_AWS_SECRET_ACCESS_KEY
ARG PROJECT_ENV
ENV PROJECT_ENV=$PROJECT_ENV
ENV PROJECT_FRONTEND_ENV=$PROJECT_ENV
ENV PROJECT_BACKEND_ENV=$PROJECT_ENV
RUN apk add aws-cli

# aws profile setup
RUN aws configure set aws_access_key_id $EASYLIVE_CODEARTIFACT_AWS_ACCESS_KEY_ID --profile easylive
RUN aws configure set aws_secret_access_key $EASYLIVE_CODEARTIFACT_AWS_SECRET_ACCESS_KEY --profile easylive

COPY package.json ./
COPY .yarn/ ./.yarn
#RUN touch ./build.log
COPY . ./
RUN yarn install --immutable --immutable-cache
RUN yarn build

FROM node:lts-alpine as runner
WORKDIR /app
ENV NODE_ENV=production
ENV PORT=3000
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/next-i18next.config.js ./next-i18next.config.js
COPY --from=builder /app/next.config.js ./next.config.js

COPY --from=builder /app/.yarn ./.yarn
COPY --from=builder /app/.yarnrc.yml ./.yarnrc.yml
COPY --from=builder /app/.yarn-plugin-aws-codeartifact.yml ./.yarn-plugin-aws-codeartifact.yml
COPY --from=builder /app/.pnp.cjs ./.pnp.cjs
COPY --from=builder /app/.pnp.loader.mjs ./.pnp.loader.mjs
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/yarn.lock ./yarn.lock

# Note yarn rebuild again - this is to let yarn rebuild binaries in the "runner" stage of the Dockerfile
# We also have to remove unplugged, so that rebuilding happens and replaces the old binaries
RUN rm -rf /app/.yarn/unplugged && yarn rebuild


USER nextjs
EXPOSE ${PORT}
CMD ["yarn", "start"]