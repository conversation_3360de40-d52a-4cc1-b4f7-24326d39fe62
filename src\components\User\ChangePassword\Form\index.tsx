import React, { use<PERSON><PERSON><PERSON>, useI<PERSON><PERSON><PERSON><PERSON><PERSON>, useMemo, useState } from "react";
import { useTranslation } from "next-i18next";
import Form from "@/components/_CommonElements/Form";
import { FieldContainer, TextInput } from "@/components/_CommonElements/Input";
import { TextButton } from "@/components/_CommonElements/Button";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { BACKEND, FRONTEND } from "@/constants/config";
import ENDPOINT from "@/models/api/endpoint";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import API from "@/utils/API";
import ChangePasswordAPIResult from "@/models/api/result/user/changePassword";
import { useSnackbar } from "notistack";
import LoadingButton from "@/components/_CommonElements/Button/LoadingButton";
import { clearAll } from "@/redux/slices/identitySlice";
import { clearUserInfo } from "@/redux/slices/userInfoSlice";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import { deleteCookie } from "cookies-next";
import { useRouter } from "next/router";
import LogoutAPIResult from "@/models/api/result/user/logout";
import { dispatch } from "@/redux/store";
import InputValidation from "@/utils/regex/InputValidation";

const IsInvalidPasswordInput = (password: string | undefined) => {
    if (password === undefined) {
        return false;
    }
    return !InputValidation.validatePassword(password) || password.length < FRONTEND.PasswordMinLength;
};
const UseLogout = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async () => {
        return await fetchAsync<LogoutAPIResult>(
            ENDPOINT.Logout(),
            {
                method: "POST"
            }
        );
    };
    return requestAsync;
}
export type ChangePasswordFormRef = {
    onSubmit: () => void;
};
const useChangePassword = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async (oldPassword: string, newPassword: string, confirmNewPassword: string) => {
        const formData = API.ToFormData({
            OldPassword: oldPassword,
            NewPassword: newPassword,
            ConfirmNewPassword: confirmNewPassword
        });
        return await fetchAsync<ChangePasswordAPIResult>(
            ENDPOINT.changePassword(),
            {
                method: "PUT",
                data: formData
            }
        );
    };
    return requestAsync;
}

interface Props {
    onCompleted: () => void;
}
const ChangePasswordForm = React.forwardRef<ChangePasswordFormRef, Props>((props, ref) => {
    const { onCompleted } = props;
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { t: validationTranslation } = useTranslation(EnumTranslationJson.Validation);
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const { enqueueSnackbar } = useSnackbar();
    const [ isLoading, setIsLoading] = useState<boolean>(false);
    const [oldPassword, setOldPassword] = useState<string>();
    const [newPassword, setNewPassword] = useState<string>();
    const [confirmNewPassword, setConfirmNewPassword] = useState<string>();
    const router = useRouter();
    const invalidOldPassword = useMemo(() => IsInvalidPasswordInput(oldPassword), [oldPassword]);
    const invalidNewPassword = useMemo(() => IsInvalidPasswordInput(newPassword), [newPassword]);
    const invalidConfirmNewPassword = useMemo(() => newPassword !== confirmNewPassword, [newPassword, confirmNewPassword]);
    const logoutAsync = UseLogout();
    const disabledSubmit = useMemo(() => {
        if (oldPassword === undefined || newPassword === undefined || confirmNewPassword === undefined) {
            return true;
        }
        if (invalidOldPassword || invalidNewPassword || invalidConfirmNewPassword) {
            return true;
        }
        return false;
    }, [oldPassword, newPassword, confirmNewPassword, invalidOldPassword, invalidNewPassword, invalidConfirmNewPassword]);

    const changePasswordAsync = useChangePassword();
    const onSubmit = useCallback(async () => {
        if (disabledSubmit) {
            return;
        }
        setIsLoading(true);
        try {
            await changePasswordAsync(oldPassword!, newPassword!, confirmNewPassword!);
            enqueueSnackbar(snackbarTranslation("messages.changePassword.success"), { variant: "success" });
            // Always logout user even the APi is failed
            // Otherwise we would probably exposing user's secret when they dont know they failed to logout.
            dispatch(clearAll());
            dispatch(clearUserInfo());
            Object.values(EnumCookieKey).forEach((values) => {
                deleteCookie(values);
            });
            router.reload();

            try {
                await logoutAsync();
            } catch {

            }
            onCompleted();
        } catch (error) {
            const errorMessage = API.GetErrorMessage(error);
            enqueueSnackbar(errorMessage, { variant: "error" });
        }
        setIsLoading(false);
    }, [logoutAsync, router, snackbarTranslation, disabledSubmit, oldPassword, newPassword, confirmNewPassword, changePasswordAsync, enqueueSnackbar, onCompleted]);

    const onChangeOldPassword = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setOldPassword(e.target.value);
    }, []);

    const onChangeNewPassword = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setNewPassword(e.target.value);
    }, []);
    const onChangeConfirmNewPassword = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setConfirmNewPassword(e.target.value);
    }, []);

    useImperativeHandle(ref, () => ({
        onSubmit
    }));
    return (
        <>
            <Form.Container size="small">
                <Form.Body rowSpacing={2} columnSpacing={2}>
                    <FieldContainer>
                        <TextInput
                            name="password"
                            label={modalTranslation("modals.changePassword.content.oldPasswordLabel")}
                            type="password"
                            value={oldPassword}
                            error={invalidOldPassword}
                            helperText={invalidOldPassword ? validationTranslation("password.errorMessage.format") : ""}
                            onChange={onChangeOldPassword}
                        />
                    </FieldContainer>

                    <FieldContainer>
                        <TextInput
                            name="password"
                            label={modalTranslation("modals.changePassword.content.newPasswordLabel")}
                            type="password"
                            value={newPassword}
                            error={invalidNewPassword}
                            helperText={invalidNewPassword ? validationTranslation("password.errorMessage.format") : ""}
                            onChange={onChangeNewPassword}
                        />
                    </FieldContainer>

                    <FieldContainer>
                        <TextInput
                            name="password"
                            label={modalTranslation("modals.changePassword.content.confirmNewPasswordLabel")}
                            type="password"
                            error={invalidConfirmNewPassword}
                            helperText={invalidConfirmNewPassword ? validationTranslation("confirmPassword.errorMessage.format") : ""}
                            value={confirmNewPassword}
                            onChange={onChangeConfirmNewPassword}
                        />
                    </FieldContainer>
                    <FieldContainer justifyContent="center">
                        { isLoading ? <LoadingButton /> :
                            <TextButton
                                disabled={disabledSubmit}
                                label={modalTranslation("buttons.confirm")}
                                onClick={onSubmit}
                            />
                        }
                    </FieldContainer>
                </Form.Body>
            </Form.Container>
        </>
    );
});
ChangePasswordForm.displayName = "ChangePasswordForm";
export default ChangePasswordForm;
