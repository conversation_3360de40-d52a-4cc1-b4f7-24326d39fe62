import { TextInput } from "@/components/_CommonElements/Input";
import { Grid } from "@mui/material";
import { TextButton } from "@/components/_CommonElements/Button";
import { useDispatch, useSelector } from "@/redux/store";
import { useState, useMemo, useCallback } from "react";
import { ModalActions } from '@/redux/slices/uiSlice';
import styles from "@/components/Event/Redeem/redeem.module.scss";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import dynamic from "next/dynamic";
import AuthenicationHooks from "@/hooks/Authentication";

const TicketRedemptionConfirmation = dynamic(() => import("@/components/ModalWindow/TicketRedemptionConfirmation"));
interface TicketRedemptionInputProps {
    defaultValue?: string;
}

const TicketRedemptionInput = (props: TicketRedemptionInputProps) => {
    const {
        defaultValue = "",
    } = props;
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const { t: redeemTranslation } = useTranslation(EnumTranslationJson.Redeem);
    const { isAuthorized } = AuthenicationHooks.useUserInfo();
    const userVerified = useSelector((state) => state.user.userVerified);
    const [ ticketToken, setTicketToken ] = useState<string>(defaultValue);
    const [ confirmModalVisible, setConfirmModalVisible ] = useState<boolean>(false);

    const dispatch = useDispatch();
    const isValidInput = useMemo(() => {
        if (!isAuthorized) {
            dispatch(ModalActions.openLoginModal());
            return false;
        }
        if (!userVerified) {
            dispatch(ModalActions.openAccountVerificationModal());
            return false;
        }
        if (ticketToken.length == 0) {
            return false;
        }
        return true;
    }, [ticketToken, isAuthorized, userVerified, dispatch]);

    const onChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setTicketToken(e.target.value);
    }, []);

    const onSuccess = useCallback(() => {
        setTicketToken("");
    }, []);
    const showModal = useCallback(() => {
        setConfirmModalVisible(true);
    }, []);
    const hideModal = useCallback(() => {
        setConfirmModalVisible(false);
    }, []);

    const helperText = useMemo(() => isValidInput ? "" : redeemTranslation("form.helper"), [isValidInput, redeemTranslation]);
    return (
        <>
            <Grid container justifyContent="center" className={styles.redeemInputContainer}>
                <Grid item xs={10} sx={{ maxWidth: "400px" }} className={styles.items}>
                    <TextInput
                        label={redeemTranslation("form.label")}
                        error={!isValidInput}
                        helperText={helperText}
                        value={ticketToken}
                        onChange={onChange}
                    />
                </Grid>
                <Grid container justifyContent="center" className={styles.items}>
                    <TextButton
                        disabled={!isValidInput}
                        label={modalTranslation("buttons.submit")}
                        onClick={showModal}
                    />
                </Grid>
            </Grid>
            <TicketRedemptionConfirmation
                visible={confirmModalVisible}
                onCancel={hideModal}
                onSuccess={onSuccess}
                ticketToken={ticketToken}
            />
        </>
    );
};
export default TicketRedemptionInput;