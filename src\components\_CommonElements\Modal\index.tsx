import styles from "@/components/_CommonElements/Modal/modal.module.scss";

import { useRouter } from "next/router";
import ModalContainer from "./Container";
import { TextButton } from "@/components/_CommonElements/Button";

import Grid from '@mui/material/Grid';
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import React, { ReactNode, useCallback } from "react";
import classNames from "classnames";

interface ModalProps {
    classes?: {
        paperContainer: string;
        modalContainer?: string,
        scrollContainer?: string;
        content?: string;
    };
    title?: string,
    children?: ReactNode;
    visible: boolean;
    onConfirm?: () => void;
    onCancel: () => void;
    onConfirmRedirectTo?: string;
    hideConfirmButton?: boolean;
    hideCancelButton?: boolean;
    hideCloseButton?: boolean;
    confirmButtonLabel?: string;
    cancelButtonLabel?: string;
    onBackdropClick?: () => void;
    contentWidth?: string;
    contentPadding?: string;
}
const Modal = React.memo((props: ModalProps) => {
    const {
        title,
        classes,
        children,
        visible,
        onConfirm, // override onClickRedirectTo if exists
        onCancel,
        onConfirmRedirectTo,
        hideConfirmButton = false,
        hideCancelButton = false,
        hideCloseButton = false,
        confirmButtonLabel,
        cancelButtonLabel,
        onBackdropClick,
        contentWidth,
        contentPadding
    } = props;

    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const router = useRouter();

    const redirectTo = useCallback(() => {
        if (!onConfirmRedirectTo) {
            onCancel();
            return;
        }
        void router.push(onConfirmRedirectTo)
    }, [onConfirmRedirectTo, onCancel, router]);

    return (<>
        <ModalContainer
            classes={classes}
            open={visible}
            onBackdropClick={onBackdropClick}
            title={title}
            hideCloseButton={hideCloseButton}
            onClickClose={onCancel}
        >
            <div className={classNames(styles.contents, classes?.content)} style={{ width: contentWidth, padding: contentPadding }}>
                {children}
            </div>

            <Grid container justifyContent="center">
                {!hideConfirmButton &&
                    <Grid container item justifyContent="center" xs={4}>
                        <TextButton
                            label={confirmButtonLabel || modalTranslation("buttons.confirm")}
                            onClick={onConfirm ?? redirectTo}
                        />
                    </Grid>
                }

                {!hideCancelButton &&
                    <Grid container item justifyContent="center" xs={4}>
                        <TextButton
                            label={cancelButtonLabel || modalTranslation("buttons.cancel")}
                            onClick={onCancel ?? redirectTo}
                        />
                    </Grid>
                }
            </Grid>

        </ModalContainer>
    </>);
});
Modal.displayName = "Modal";
export default Modal;