import styles from "@/components/_CommonElements/Modal/modal.module.scss";
import classNames from "classnames";

import Backdrop from '@mui/material/Backdrop';

interface ModalBackgroundProps {
  backgroundColor?: string;
  className?: string;
  color?: string;
  fadeIn?: boolean;
  onClick?: () => void;
  opaque?: boolean;
  open?: boolean;
  inlineStyle?: { [key: string]: any};
  invisible?: boolean;
}

const ModalBackground = (props: ModalBackgroundProps) => {
  const {
    backgroundColor,
    className,
    onClick,
    open = true,
    inlineStyle,
    invisible,
  } = props;

  return (<>    
    <Backdrop
      className={classNames(className, styles.background, styles.visible)}
      open={open}
      invisible={invisible}
      onClick={onClick}
      sx={{
        ...inlineStyle,
        backgroundColor: inlineStyle?.backgroundColor ?? backgroundColor}}
    >
      
    </Backdrop>
  </>);
};

export default ModalBackground;