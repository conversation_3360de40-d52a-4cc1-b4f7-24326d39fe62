@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

.sitemap {

  & .item {
    margin: 10px 0;
    user-select: none;
    cursor: pointer;
    font-weight: 500;
    padding: 2px 4px;

    @include viewport.within("mobile") {
      padding: 2px 0;
    };

    &.active, &:hover {
      color: map.get(theme.$color, "primary");
    };
  };
};