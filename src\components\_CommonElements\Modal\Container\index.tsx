import styles from "@/components/_CommonElements/Modal/modal.module.scss";
import classNames from "classnames";
import Tabs from "@/components/_CommonElements/Tabs";
import Dialog from '@mui/material/Dialog';
import IconButton from "@/components/_CommonElements/Button/IconButton";
import CancelRoundedIcon from '@mui/icons-material/CancelRounded';
import React, { ReactNode } from "react";

interface ModalTabObject {
    name: string;
    contents: React.ReactNode;
    active?: boolean;
    action?: () => void;
}

interface ModalContainerProps {
    title?: string;
    classes?: {
        paperContainer?: string;
        modalContainer?: string,
        scrollContainer?: string;
    }
    children?: ReactNode;
    disableEscapeKeyDown?: boolean;
    disableBackdropClickClose?: boolean;
    fullScreen?: boolean;
    hideCloseButton?: boolean;
    onBackdropClick?: () => void;
    onClickClose?: () => void;
    open?: boolean;
    modalTabs?: ModalTabObject[];
    margin?: string;
    padding?: string;
    boxShadow?: string;
}

const ModalContainer = React.memo((props: ModalContainerProps) => {
    const {
        title,
        classes,
        children,
        hideCloseButton,
        disableEscapeKeyDown,
        disableBackdropClickClose,
        fullScreen,
        onBackdropClick,
        onClickClose,
        open = true,
        modalTabs
    } = props;
    const handleOnClose = (event: object, reason: string) => {
        if (reason === "backdropClick" && disableBackdropClickClose) return null;
        return onClickClose && onClickClose();
    };
    return (<>
        <Dialog
            disableEscapeKeyDown={disableEscapeKeyDown}
            open={open}
            onBackdropClick={onBackdropClick}
            onClose={handleOnClose}
            fullScreen={fullScreen}
            classes={{ paper: classNames(styles.container, classes?.paperContainer, {[styles.withTitle]: title !== undefined }) }}

        >
            {title &&
                <div className={styles.title}>
                    {title}
                </div>
            }

            <div className={classNames(styles.modal, classes?.modalContainer)}>
                <div className={classNames(styles.scrollContainer, classes?.scrollContainer)}>
                    {modalTabs &&
                        <Tabs tabs={modalTabs} />
                    }
                    {children}
                </div>
            </div>

            {!hideCloseButton &&
                <div className={classNames(styles.closeButton)}>
                    <IconButton onClick={onClickClose}>
                        <CancelRoundedIcon fontSize="large" />
                    </IconButton>
                </div>
            }
        </Dialog>
    </>);
});
ModalContainer.displayName = "ModalContainer";


export default ModalContainer;