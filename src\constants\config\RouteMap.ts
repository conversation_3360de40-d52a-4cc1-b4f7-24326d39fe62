import { FRONTEND } from ".";

class RouteMap {
    static readonly AboutUs = "/about";
    static readonly ContactUs = "/contact";
    static readonly Faq = "/faq";
    static readonly guide = "/guide";
    static readonly TermsAndConditions = "/terms";
    static readonly PrivacyPolicy = "/privacy";
    static readonly TicketPurchase = "/event/purchase";
    static readonly TicketPurchaseLink = (eventId: string) => `${FRONTEND.URL}/event/purchase?eventId=${eventId}`;
    static readonly Event = "/event";
    static readonly BoundTickets = "/user/boundTickets";
    static readonly UnboundTickets = "/user/unboundTickets";
    static readonly ticketOrders = "/user/ticketOrders";
    static readonly productOrders = "/user/productOrders";
    static readonly UserProfile ="/user/profile";
    static readonly UserRedeem ="/user/redeem";
    static readonly MembershipSubscription ="/user/membershipSubscription";
    static readonly MembershipReservation = "/user/reservation";
    static readonly EventPurchasedResult = (orderId: string) => `${FRONTEND.URL}/event/purchasedResult?order_id=${orderId}`;
    static readonly Login = "/login";
    static readonly SignUp = "/signUp";
    static readonly ImportTicketTokenEvents = "/event/redeem";
    static readonly UserImportTicketTokenEvents = "/user/redeem";
    static readonly LiveVideo = "/video";
    static readonly ShareTicketTokenUrl = (token: string, url?: string|undefined):string => `${url?url:FRONTEND.URL}${RouteMap.ImportTicketTokenEvents}?code=${token}`;
    static readonly Main = "/";  
    static readonly ShareEvent = (eventId: string) => `${FRONTEND.URL}/event/${eventId}`;
    static readonly Checkout = "/checkout";
}
export default RouteMap;