import ReservableSite from "./ReservableSite";

interface UserReservedSession {
    reserveId: string;
    reserveCreatedDateTime: string;
    status: string;
    reserveStatusCreatedDateTime: string;
    generalReservableSessionId: string;
    sessionStartDate: string;
    sessionEndDate: string;
    sessionStartTime: string;
    sessionEndTime: string;
    site: ReservableSite;
}

export default UserReservedSession;