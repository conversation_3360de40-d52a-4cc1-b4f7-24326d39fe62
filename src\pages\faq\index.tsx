import type { GetServerSideProps, NextPage } from 'next';
import Faq from '@/components/Faq';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import i18n from '@/utils/i18n';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import PageContent from '@/components/_PageComponents/PageContent';

const FAQPage: NextPage = () => {
    const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
    const title = useMemo(() => seoTranslation("page.faq.title"), [seoTranslation]);
    return (
        <PageContent
            title={title}
            content={<Faq />}
        />
    );
};
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [
        EnumTranslationJson.FAQ
    ],
    context
});
export default FAQPage;