import React, { useMemo, useState, useCallback, Fragment } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { Box, CircularProgress, Divider, Icon, Stack, Typography, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useSnackbar } from "notistack";
import Markdown from "markdown-to-jsx";

import API from "@/utils/API";
import i18n from "@/utils/i18n";
import { chunk } from "@/utils/Array";
import AuthenicationHooks from "@/hooks/Authentication";
import { dispatch } from "@/redux/store";
import { ModalActions } from "@/redux/slices/uiSlice";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import RouteMap from "@/constants/config/RouteMap";
import { BACKEND } from "@/constants/config";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import ENDPOINT from "@/models/api/endpoint";
import CreateOrderAPIResult from "@/models/api/result/order/create";
import CampaignShoppingCart from "@/models/api/models/CampaignShoppingCart";
import ProductDetails from "@/models/api/models/ProductDetails";
import Label from "@/components/_CommonElements/Label";
import Section from "@/components/_CommonElements/Section";
import { Checkbox } from "@/components/_CommonElements/Input";
import { TextButton } from "@/components/_CommonElements/Button";
import ProductDisplay from "@/components/_PageComponents/Campaign/ProductList/Product";

import styles from "../campaign.module.scss";

const AgreementLabel = React.memo(() => {
    const { t: purchaseTranslation } = useTranslation(EnumTranslationJson.Purchase);
    const Router = useRouter();

    return (
        <span className={styles.tnc}>
            <Markdown
                options={{
                    wrapper: Fragment,
                    forceWrapper: false
                }}
            >
                {purchaseTranslation("modal.content.agreementClaim", {
                    TNC_URL: `/${Router.locale}${RouteMap.TermsAndConditions}`,
                    POLICY_URL: `/${Router.locale}${RouteMap.PrivacyPolicy}`
                })}
            </Markdown>
        </span>
    );
});
AgreementLabel.displayName = "AgreementLabel";

const useCheckout = () => {
    const requestAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION, EnumRequestHeader.LANGUAGE] });
    const checkoutAsync = async ({campaignId, cartItemIds, paymentMethod} : 
        {
            campaignId: string,
            cartItemIds: string[],
            paymentMethod: { id: string, gateway: string };
        } ) => {
        const formData = new FormData();
        formData.append("PaymentGatewayId", paymentMethod.gateway);
        formData.append("PaymentType", paymentMethod.id);
        for (let i = 0; i < cartItemIds.length; i++) {
            const productId = cartItemIds[i];
            if (productId) {
                formData.append(`ProductIds[${i}]`, productId);
            }
        }
        return await requestAsync<CreateOrderAPIResult>(
            ENDPOINT.CampaignCheckout(campaignId),
            {
                method: RequestMethod.POST,
                data: formData
            }
        );
    };
    return { checkoutAsync };
};

interface CampaignProductListProps {
    products: ProductDetails[];
}

const CampaignProductList = (props: CampaignProductListProps) => {
    const { products } = props;

    const theme = useTheme();
    const screenBreakpointBetweenDetect = useMediaQuery(theme?.breakpoints?.between('md', 'lg'));
    const screenBreakpointDownDetect = useMediaQuery(theme?.breakpoints?.down('md'));
    const router = useRouter();
    const { checkoutAsync } = useCheckout();
    const { enqueueSnackbar } = useSnackbar();
    const { isAuthorized } = AuthenicationHooks.useUserInfo();
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { t: campaignTranslation } = useTranslation(EnumTranslationJson.Campaign);

    const [ isUpdating, setIsUpdating ] = useState(false);
    const [ isDeleting, setIsDeleting ] = useState(false);
    const [ isCreating, setIsCreating ] = useState(false);
    const [isAcceptedAgreement, setIsAcceptedAgreement] = useState<boolean>(false);

    const campaignId = router.query?.id as string;
    const isLoading = useMemo(() => isUpdating || isDeleting, [isUpdating, isDeleting]);
    const [cartItems, setCartItems] = useState<CampaignShoppingCart[]>(() => []);

    const chunkedProducts = useMemo(() => 
        chunk<ProductDetails>(products, screenBreakpointDownDetect ? 1 : screenBreakpointBetweenDetect ? 3 : 4), 
        [products, screenBreakpointBetweenDetect, screenBreakpointDownDetect]);

    const totalPrice = useMemo(() => cartItems.reduce((sum, item) => {
        sum += item.price * item.quantity;
        return sum;
    }, 0.0), [cartItems]);
    const cartItemIds = useMemo(() => cartItems.map(p => p.productId), [cartItems]);

    const onChangeIsAcceptedAgreement = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setIsAcceptedAgreement(e.target.checked);
    }, []);

    const checkIsAcceptedAgreement = useCallback(() => isAcceptedAgreement, [isAcceptedAgreement]);

    const removeItemInCart = useCallback((productId: string) => {
        setIsDeleting(true);
        try {
            setCartItems((prev) => prev.reduce((final: CampaignShoppingCart[], p) => {
                if (p.productId != productId) {
                    final.push(p);
                }
                return final;
            }, []));
            enqueueSnackbar(snackbarTranslation("messages.cart.remove.success"), { variant: "success" });
        }
        catch (error) {
            console.log(error);
        }
        setIsDeleting(false);
    }, [enqueueSnackbar, snackbarTranslation]);

    const decreaseItemByOne = useCallback((productId: string) => {
        const targetItem = cartItems.find(i => i.productId === productId);
        if (targetItem) {
            // Dont decrease any more, keep the last one quantity for this item.
            if (targetItem.quantity > 1) {
                setIsUpdating(true);
                try {
                    targetItem.quantity--;
                    setCartItems((prev) => [...prev, targetItem]);
                }
                catch (error: unknown) {
                    console.log(error);
                }
                setIsUpdating(false);
            } else if (targetItem.quantity == 1) {
                removeItemInCart(productId);
            }
        }
    }, [cartItems, removeItemInCart]);

    const increaseItemByOne = useCallback((productId: string) => {
        setIsUpdating(true);
        try {
            const targetItem = cartItems.find(i => i.productId === productId);
            if (targetItem) {
                targetItem.quantity++;
                setCartItems((prev) => [...prev, targetItem]);
            } else {
                const newItem = products.find(p => p.productId === productId);
                if (newItem) {
                    setCartItems((prev) => [...prev, {
                        productId: productId,
                        price: newItem.price,
                        quantity: 1
                    }]);
                }
            }
        }
        catch (error: unknown) {
            console.log(error);
        }
        setIsUpdating(false);
    }, [cartItems, products]);

    const submitOrder = useCallback(async (
        isAcceptedAgreement: boolean, 
        paymentMethod: { id: string, gateway: string }) => {
        try {
            setIsCreating(true);
            if (!isAuthorized) {
                dispatch(ModalActions.openLoginModal());
                throw new Error("IS_AUTHORIZED: False");
            }
            if (!isAcceptedAgreement) {
                enqueueSnackbar(snackbarTranslation("messages.purchase.error.acceptAgreement"), { variant: "error" })
                throw new Error("IsAcceptedAgreement: False");
            }
            if (!cartItems) {
                enqueueSnackbar(snackbarTranslation("messages.purchase.error.unknowItem"), { variant: "error" })
                setCartItems(() => []);
                throw new Error("EMPTY_CART_ITEMS");
            }
            try {
                const res = (await checkoutAsync({campaignId: campaignId, cartItemIds: cartItemIds, paymentMethod: paymentMethod})).data!;
                void router.push(res.paymentUrl!.toString());
            }
            catch (error) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
                throw error;
            }
        } catch {
            setIsCreating(false);
        }
    }, [isAuthorized, cartItems, enqueueSnackbar, snackbarTranslation, checkoutAsync, campaignId, cartItemIds, router]);

    const onSubmitOrder = useCallback(() => {
        void submitOrder(checkIsAcceptedAgreement(), { id: "card", gateway: "LABPAY_AIRWALLETX" })
    }, [checkIsAcceptedAgreement, submitOrder]);

    return (
        <Section
            containerSize="wide"
            className={{ root: styles.purchaseSection, label: styles.label }}
            label={campaignTranslation("section.purchase.title")}
            labelSize="xlarge"
            content={
                <>
                    <Stack direction="column" spacing={2}>
                        {/* <Stack direction="row">
                            <Label title={campaignTranslation("detail.productName")} className={styles.ticketType} />
                            <Label title={campaignTranslation("detail.qty")} />
                            <div style={{ width: '7rem' }}>&nbsp;</div>
                        </Stack> */}
                        {chunkedProducts ? 
                            chunkedProducts.map((rowItems, rowIndex) =>
                                <Stack key={`display-products-row-${rowIndex}`} direction="row" spacing={2}>
                                    {rowItems?.map((product, colIndex) => 
                                        <Stack key={`display-product-clo-${colIndex}`} direction="column" spacing={0} className={styles.colStackFull}>
                                            <ProductDisplay 
                                                key={`product-${product.productId}`} 
                                                product={product} 
                                                cartItems={cartItems}
                                                functions={[
                                                    decreaseItemByOne,
                                                    increaseItemByOne,
                                                    removeItemInCart
                                                ]} 
                                            />
                                        </Stack>
                                    )}
                                </Stack>
                            )
                             : null
                        }
                    </Stack>
                    <Divider orientation="horizontal" flexItem />
                    <Box display='flex' justifyContent='flex-end'>
                        <Typography variant="h2" component="h2" className={styles.total}>
                            {isLoading ? <CircularProgress /> : campaignTranslation('modal.total')} {i18n.GetCurrency('HKD', totalPrice, router.locale)}
                        </Typography>
                    </Box>
                    <Checkbox
                        label={<AgreementLabel />}
                        checked={isAcceptedAgreement}
                        onChange={onChangeIsAcceptedAgreement}
                        className={styles.tnc}
                    />
                    <div className={styles.toBuy}>
                        {isCreating ? <CircularProgress /> : 
                            <TextButton
                                size="extra"
                                onClick={onSubmitOrder}
                                variant="contained"
                            >
                                <Icon baseClassName="fa" className="fa-ticket-simple" />
                                &nbsp; {campaignTranslation("detail.buy")}
                            </TextButton>
                        }
                    </div>
                </>
            }
        />
    );
};

export default CampaignProductList;