import type { GetServerSideProps, NextPage } from 'next';
import i18n from '@/utils/i18n';
import PrivacyPolicy from '@/components/PrivacyPolicy';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import PageContent from '@/components/_PageComponents/PageContent';

const PrivacyPage: NextPage = () => {
    const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
    const title = useMemo(() => seoTranslation("page.privacyPolicy.title"), [seoTranslation]);
    return (
        <PageContent
            title={title}
            content={<PrivacyPolicy />}
        />
    );
};
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    context
});
export default PrivacyPage;