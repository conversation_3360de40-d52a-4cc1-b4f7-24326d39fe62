@use "@/styles/utils/theme.module.scss"as theme;
@use "@/styles/utils/viewport.module.scss"as viewport;
@use "sass:selector";

@mixin unify-parent($child) {
  @at-root #{selector.unify(&, $child)} {
    @content;
  }
}

.menu {
    &.horizontal {
        @include viewport.within("tablet") {
          display: none;
        }
      
        font-weight: 800;
        & > ul {
            align-items: center;
        }
      }
}