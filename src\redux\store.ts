import { configureStore } from "@reduxjs/toolkit";
import {
  useDispatch as useAppDispatch,
  useSelector as useAppSelector,
  TypedUseSelectorHook,
} from "react-redux";
import { persistStore } from "redux-persist";
import RootReducer from "./RootReducer";

// ----------------------------------------------------------------------


const store = configureStore({
  reducer: RootReducer ,
  middleware: (getDefaultMiddleware) => // https://redux-toolkit.js.org/api/getDefaultMiddleware
    getDefaultMiddleware({
      serializableCheck: false,
      immutableCheck: false,
    })
    // .concat(logger),
});

const persistor = persistStore(store);

export type AppDispatch = typeof store.dispatch;

const { dispatch } = store;
export type RootState = ReturnType<typeof store.getState>;

const useDispatch = () => useAppDispatch<AppDispatch>();
const useSelector: TypedUseSelectorHook<RootState> = useAppSelector;

export { store, persistor, dispatch, useSelector, useDispatch };
