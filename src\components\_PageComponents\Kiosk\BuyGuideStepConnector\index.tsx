import { StepLabel } from '@mui/material';

const BuyGuideStepConnector = () => (
    <StepLabel
        sx={{
            "& .MuiStepLabel-label.Mui-disabled": {
                color: "#757e8d",
                width: "fit-content",
                height: "fit-content",
                fontSize: "3rem",
            },
            "& .MuiStepLabel-label.Mui-active": {
                color: "blue",
                width: "fit-content",
                height: "fit-content",
                fontSize: "3rem",
            }
        }}
        icon={<span />}>
        {/* <KeyboardDoubleArrowDownIcon sx={{ fontSize: "3rem" }} /> */}
    </StepLabel>
);

export default BuyGuideStepConnector;