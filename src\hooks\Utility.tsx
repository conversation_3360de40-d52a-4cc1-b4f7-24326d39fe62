import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import dayjs from "dayjs";
import { useTranslation } from "next-i18next";
import { useEffect, useMemo, useState } from "react";

class UtilityHooks {
    public static useCountDown = (start: number) => {
        const [counter, setCounter] = useState<number>(start);
        useEffect(() => {
            if (counter <= 0) {
                return;
            }
            setTimeout(() => {
                setCounter(counter - 1);
            }, 1000);
        }, [counter]);
        return { counter, setCounter };
    };
    public static useBrowserLocalTime = (unixDateTime: number) => {
        const { t: hooksTranslation } = useTranslation(EnumTranslationJson.Hooks);
        const [ localTime, setLocalTime ] = useState<string>();
        
        const browserTimeZone = useMemo(() => {
            return typeof window !== 'undefined' ? Intl.DateTimeFormat().resolvedOptions().timeZone || "UTC" : undefined;
        }, []);

        useEffect(() => {
            // Use Browser Timezone instead of NextJS Timezone
            const createDateTime_Unix = dayjs.utc(unixDateTime);
            const createDateTime_LocalTime = createDateTime_Unix.tz(browserTimeZone);
            setLocalTime(createDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss (Z)"));    
        }, [unixDateTime, browserTimeZone]);

        return { localTime: localTime || hooksTranslation("pendingBrowserLocalTime"), browserTimeZone };
    }
}

export default UtilityHooks;