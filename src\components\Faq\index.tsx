import styles from "@/components/Faq/faq.module.scss";

import { ChipLabel } from "@/components/_CommonElements/Display";
import {
  SecondaryNavigation,
  NavItem,
} from "@/components/_CommonElements/SecondaryNavigation";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import Markdown from "markdown-to-jsx";
import { Fragment } from "react";
import Accordion from "@/components/_CommonElements/Accordion";

interface MarkdownProps {
  content: string;
}

const MarkdownContainer = (props: MarkdownProps) => {
  const { content } = props;
  return (
    <Markdown
      options={{
        wrapper: Fragment,
        forceWrapper: false,
        overrides: {
          Accordion: {
            component: Accordion,
          },
        },
      }}
    >
      {content}
    </Markdown>
  );
};

const Faq = () => {
  const { t: faqTranslation } = useTranslation(EnumTranslationJson.FAQ);
  return (
    <>
      <div className={styles.container}>
        <SecondaryNavigation>
          <NavItem link={`#account`} title={faqTranslation("account.title")} />
          <NavItem link={`#ticket`} title={faqTranslation("ticket.title")} />
          <NavItem link={`#merchandise`} title={faqTranslation("merchandise.title")} />
        </SecondaryNavigation>
        <section id={"account"} className={styles.faqSection}>
          <ChipLabel className={styles.chipLabel}>
            {faqTranslation("account.title")}
          </ChipLabel>
          <MarkdownContainer content={faqTranslation("account.content")} />
        </section>
        <section id={"ticket"} className={styles.faqSection}>
          <ChipLabel className={styles.chipLabel}>
            {faqTranslation("ticket.title")}
          </ChipLabel>
          <MarkdownContainer content={faqTranslation("ticket.content")} />
        </section>
        <section id={"merchandise"} className={styles.faqSection}>
          <ChipLabel className={styles.chipLabel}>
            {faqTranslation("merchandise.title")}
          </ChipLabel>
          <MarkdownContainer content={faqTranslation("merchandise.content")} />
        </section>
        <div className={styles.help}>
            <Markdown>
                {faqTranslation("report-issue", {
                    REPORT_ISSUE_URL: "/report-issue"
                })}
            </Markdown>
        </div>

      </div>
    </>
  );
};

export default Faq;
