import LoadingButton from "@/components/_CommonElements/Button/LoadingButton";
import Section from "@/components/_CommonElements/Section";
import BuyGuideStepConnector from "@/components/_PageComponents/Kiosk/BuyGuideStepConnector";
import BuyGuideStepIcon from "@/components/_PageComponents/Kiosk/BuyGuideStepIcon";
import eventBuyStyles from "@/components/Kiosk/Buy/eventBuy.module.scss";
import styles from "@/components/Kiosk/InfoDisplay/event.module.scss";
import { BACKEND } from "@/constants/config";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import ENDPOINT from "@/models/api/endpoint";
import EventSessionBundles from "@/models/api/models/ApplicationEventSessionBundles";
import ApplicationEventSessionBundles from "@/models/api/models/ApplicationEventSessionBundles";
import EventDetails from "@/models/api/models/EventDetails";
import EventSessionBundlesByDate from "@/models/api/models/EventSessionBundlesByDate";
import ApplicationEventSessionBundlesAPIResult from "@/models/api/result/application/eventSessionBundles";
import { EventSessionBundleAPIResult } from "@/models/api/result/events/session";
import { ShoppingCartSummarizedItem } from "@/models/api/result/user/shoppingCart";
import { clearCart } from "@/redux/slices/cartSlice";
import { setEmail } from "@/redux/slices/kioskSlice";
import { ModalActions } from "@/redux/slices/uiSlice";
import { dispatch, useSelector } from "@/redux/store";
import i18n from "@/utils/i18n";
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import DeleteIcon from '@mui/icons-material/Delete';
import EmailIcon from '@mui/icons-material/Email';
import { Box, Button, Checkbox, Divider, FormControlLabel, FormGroup, InputAdornment, Stack, Step, StepLabel, Stepper, TextField, Typography } from "@mui/material";
import classNames from "classnames";
import dayjs, { Dayjs } from "dayjs";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import { useSnackbar } from "notistack";
import { ChangeEvent, ReactNode, useCallback, useEffect, useMemo, useState } from "react";

import StepPurchaseEventSessionBundle from "./steps/PurchaseEventSessionBundle";
import StepPurchaseSection from "./steps/PurchaseSection";

const DateCalendar = dynamic(async () => (await import("@mui/x-date-pickers")).DateCalendar, { ssr: false });
interface EventBuyContainerProps {
    eventDetails: EventDetails;
    counterMessage?: ReactNode | string;
}
const EventBuyContainer = (props: EventBuyContainerProps) => {
    const { eventDetails, counterMessage } = props;
    const cartItems = useSelector((state) => state.cart.items);
    const { t: kioskTranslation } = useTranslation(EnumTranslationJson.Kiosk);
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { enqueueSnackbar } = useSnackbar();
    const router = useRouter();

    const [selectedDate, setSelectedDate] = useState<Dayjs | null>();
    const [selectedDateAvailableEventSessionBundlesOption, setSelectedDateAvailableEventSessionBundlesOption] = useState<ApplicationEventSessionBundles | undefined>();
    const [selectedSession, setSelectedSession] = useState<string>("");
    const [quantity, setQuantity] = useState<number>(0);
    const [ isLoadingSessionList, setIsLoadingSessionList ] = useState<boolean>(false);
    
    const [activeStep, setActiveStep] = useState(0);
    const [isReadTnc, setIsReadTnc] = useState(false);
    const [customerEmailAddr, setCustomerEmailAddr] = useState('');
    const [disableNextStep, setDisableNextStep] = useState([isReadTnc == false, selectedDate == null, true]);
    const purchareSteps: string[] = useMemo(() => ([
        kioskTranslation('purchaseProcess.labels.stepOne'),
        kioskTranslation('purchaseProcess.labels.stepTwo'),
        // kioskTranslation('purchaseProcess.labels.stepThree'),
        kioskTranslation('purchaseProcess.labels.stepFour'),
    ]), [kioskTranslation]);
    const cartIsEmpty = useMemo(() => cartItems.length <= 0, [cartItems]);

    const availableDates = useMemo(() => (eventDetails.availableSessionDateList.map((dateString) => new Date(dateString)).map((date) => dayjs(date.getTime()))), [eventDetails.availableSessionDateList]);

    const handleNextStep = useCallback((targetIndex: number, value: boolean = false) => {
        if (disableNextStep[targetIndex] !== value) {
            const nextDisableNextStep = disableNextStep.map((step, index) => {
                if (index === targetIndex) {
                    return value;
                } else {
                    return step;
                }
            });
            setDisableNextStep(nextDisableNextStep);
        }
    }, [disableNextStep]);

    const defaultDateValue = useMemo(() => {
        const todayDateTime = dayjs();
        const defaultValue = todayDateTime.diff(availableDates[0], 'day') >= 0 ? todayDateTime : availableDates[0];
        if (!selectedDate) setSelectedDate(defaultValue);
        handleNextStep(1);
        return defaultValue;
    }, [availableDates, handleNextStep, selectedDate]);

    /*
    const selectedDateAvailableEventSessionBundlesOptionDataSource = useMemo(() => {
        if (!selectedDateAvailableEventSessionBundlesOption) {
            return undefined;
        }
        const dataSource = [];

        for (const session of Object.keys(selectedDateAvailableEventSessionBundlesOption.sessionGroup)) {
            const sessionGroup = selectedDateAvailableEventSessionBundlesOption.sessionGroup[session];
            const sessionStartDateTime_LocalTime = dayjs.utc(sessionGroup.sessionStartDateTime).tz(eventDetails.eventTimeZone);
            const sessionEndDateTime_LocalTime = dayjs.utc(sessionGroup.sessionEndDateTime).tz(eventDetails.eventTimeZone);
            const optionDisplayHeader = `${sessionStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss")} ～ ${sessionEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss")}`;
            dataSource.push({
                id: `opt-${session}`,
                value: '',
                primaryValue: optionDisplayHeader,
                disabled: false,
                isHeader: true
            });

            sessionGroup.items?.forEach(bundle => {
                dataSource.push({
                    id: bundle.eventSessionBundleId,
                    value: bundle.eventSessionBundleId,
                    primaryValue: bundle.eventBundleName,
                    secondaryValue: i18n.GetCurrency(bundle.currency, bundle.price, router.locale),
                    disabled: bundle.soldOut,
                    outOfStock: bundle.soldOut,
                    isHeader: false
                }); 
            });
        }

        return dataSource;
    }, [router.locale, eventDetails.eventTimeZone, selectedDateAvailableEventSessionBundlesOption]);

    const selectedDateAvailableEventSessionBundleFlattenedDataSource = useMemo(() => {
        if (!selectedDateAvailableEventSessionBundlesOption) {
            return undefined;
        }

        const dataSource: ShoppingCartSummarizedItem[] = [];

        for (const session of Object.keys(selectedDateAvailableEventSessionBundlesOption.sessionGroup)) {
            const sessionGroup = selectedDateAvailableEventSessionBundlesOption.sessionGroup[session];

            sessionGroup.items?.forEach(bundle => {
                dataSource.push(bundle); 
            });
        }

        return dataSource;
    }, [selectedDateAvailableEventSessionBundlesOption]);
    */

    const selectedDateAvailableEventSessionBundleFlattenedDataSourceBySession = useMemo(() => {
        if (!selectedDateAvailableEventSessionBundlesOption) {
            return undefined;
        }

        const dataSource: ShoppingCartSummarizedItem[] = [];

        /*
        for (const session of Object.keys(selectedDateAvailableEventSessionBundlesOption.sessionGroup)) {
            const sessionGroup = selectedDateAvailableEventSessionBundlesOption.sessionGroup[session];
            if (`opt-${session}` === selectedSession) {
                sessionGroup.items?.forEach(bundle => {
                    dataSource.push(bundle); 
                });
            }
        }
        */
        for (const bundle of selectedDateAvailableEventSessionBundlesOption.eventBundleList) {
            const now = dayjs.utc();
            const sessionStartDateTime_LocalTime = now.tz(eventDetails.eventTimeZone);
            const sessionEndDateTime_LocalTime = now.tz(eventDetails.eventTimeZone);
            const _bundle: ShoppingCartSummarizedItem = {
                ...bundle,
                sessionStartDateTime: sessionStartDateTime_LocalTime.unix(),
                sessionEndDateTime: sessionEndDateTime_LocalTime.unix(),
                quantity: 0,
                eventTimeZone: eventDetails.eventTimeZone
            };
            dataSource.push(_bundle);
        }

        return dataSource;
    }, [selectedDateAvailableEventSessionBundlesOption, selectedSession]);

    const requestFetchSessionBundleList = useCallback(async (showLoading = true) => {
        if (!selectedDate) {
            return null;
        }
        if (showLoading) {
            setIsLoadingSessionList(true);
        }
        try {
            const date = selectedDate.format("YYYY-MM-DD");
            const res = (await BACKEND.Gateway.fetchQuery<ApplicationEventSessionBundlesAPIResult>({
                url: ENDPOINT.GetApplicationAvailableBundles(eventDetails.eventId),
                params: {
                  queryKey: `session-bundles-${eventDetails.eventId}-OTA-${date}`
                },
                headers: {
                  "X-Ix-Application-Key": BACKEND.APP_SECRET ?? "",
                },
                refetchInterval: false,
                cacheTime: 0,
                staleTime: 0
            })).data!;
            console.log(res);
            
            setSelectedDateAvailableEventSessionBundlesOption(res);
        } catch {
            enqueueSnackbar(snackbarTranslation("messages.fetchEventSession.error"));
        }
        if (!showLoading) {
            setIsLoadingSessionList(false);
        }
    }, [selectedDate, eventDetails.eventId, enqueueSnackbar, snackbarTranslation]);

    const onSelectedDateChanged = useCallback((date: unknown) => {
        setSelectedDate(date as Dayjs | null);
        setSelectedDateAvailableEventSessionBundlesOption(undefined);
        setQuantity(0);
        handleNextStep(1);
        if (disableNextStep[2] == false) handleNextStep(2, true);
    }, [disableNextStep, handleNextStep]);

    useEffect(() => {
        if (!isLoadingSessionList) {
            void requestFetchSessionBundleList(false);
        }
    }, [router.locale, requestFetchSessionBundleList, isLoadingSessionList]);

    const isDateUnavailable = useCallback((date: unknown): boolean => {
        const formattedDate = dayjs(date as Dayjs).format("YYYY-MM-DD");
        // Check if the formattedDate is included in availableDates
        return !availableDates.some((availableDate) =>
            availableDate.isSame(formattedDate, "day")
        );
    }, [availableDates]);

    const [flutterjsLogger, setFlutterjsLogger] = useState("");

    const handleClearAll = useCallback(() => dispatch(clearCart()), []);

    const handlePaymentMethod = useCallback(() => {
        dispatch(ModalActions.openKioskPaymentMethodModal());
    }, []);

    const currentDateTime = dayjs();
    const defaultCalendarMonth = useMemo(() => {
        return currentDateTime.isAfter(availableDates[0]) ? currentDateTime : availableDates[0];
    }, [currentDateTime, availableDates]);

    const handleCustomerEmailAddrOnChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        const isTncAndEmailCompleted = (newValue === '' || isReadTnc == false);
        setCustomerEmailAddr(newValue);
        dispatch(setEmail(newValue));
        handleNextStep(0, isTncAndEmailCompleted);
    }, [handleNextStep, isReadTnc]);

    const handleAcceptTnc = useCallback(() => {
        const newValue = !isReadTnc;
        const isTncAndEmailCompleted = (customerEmailAddr === '' || newValue == false);
        setIsReadTnc(newValue);
        handleNextStep(0, isTncAndEmailCompleted);
    }, [customerEmailAddr, handleNextStep, isReadTnc]);

    const onHandleSelectSession = useCallback((sessionId: string) => {
        setSelectedSession(sessionId);
    }, []);

    const handlePurchaseStepNext = useCallback(() => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }, []);
    const handlePurchaseStepBack = useCallback(() => setActiveStep((prevActiveStep) => prevActiveStep - 1), []);
    const handleCancelResetPurchaseSteps = useCallback(() => setActiveStep(0), []);

    return (
        <>
            <Stack 
                direction={"row"} 
                justifyContent="space-between"
                alignItems="flex-start"
                spacing={1}>
                <Box pl={2} pr={2} className={eventBuyStyles.kisokSectionBox} flex={1}>
                    <Stack direction={"column"} gap={1}>
                        <Section
                            containerSize="wide"
                            content={<Box sx={{pt: 1}}>{counterMessage}</Box>}
                        />
                        <Divider />
                        <Stepper activeStep={activeStep} orientation="vertical" connector={<BuyGuideStepConnector />}>
                            {purchareSteps.map((label: string) => (
                                <Step key={label}>
                                    <StepLabel StepIconComponent={BuyGuideStepIcon}><Typography fontSize={"1.5rem"}>{label}</Typography></StepLabel>
                                </Step>
                            ))}
                        </Stepper>
                    </Stack>
                </Box>
                <Box pl={2} pr={2} pt={2} className={eventBuyStyles.kisokSectionBox} flex={3}>
                    <Stack direction={"column"} gap={1}>
                        {activeStep === purchareSteps.length ? (
                            <>
                                <Typography sx={{ mt: 2, mb: 1 }}>
                                    Purchase finished.
                                </Typography>
                                <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
                                    <Box sx={{ flex: '1 1 auto' }} />
                                    <Button onClick={handleCancelResetPurchaseSteps}>Reset</Button>
                                </Box>
                            </>
                        ) : (
                            <>
                                <div className={styles.purcahseSectionContainer}>
                                    {activeStep === 0 ? (
                                        <>
                                            <FormGroup>
                                                <Typography variant="h5" pb={5}>
                                                    {kioskTranslation('purchaseProcess.tnc')}
                                                </Typography>
                                                <br/>
                                                <TextField 
                                                    fullWidth 
                                                    required
                                                    label="Email" 
                                                    id="optinEmail" 
                                                    variant="standard" 
                                                    size="medium"
                                                    type="email"
                                                    placeholder="Place enter your email address"
                                                    InputProps={{
                                                        startAdornment: (
                                                        <InputAdornment position="start">
                                                            <EmailIcon />
                                                        </InputAdornment>
                                                        ),
                                                    }}
                                                    inputProps={{
                                                        pattern: "^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,4}$"
                                                    }}
                                                    onChange={handleCustomerEmailAddrOnChange}
                                                    autoFocus
                                                />
                                                <br/>
                                                <FormControlLabel 
                                                    required 
                                                    control={<Checkbox size="medium" onChange={handleAcceptTnc} />} 
                                                    label={<Typography fontSize={"1.2rem"}>{kioskTranslation('purchaseProcess.acceptTnc')}</Typography>} 
                                                    checked={isReadTnc}
                                                />
                                            </FormGroup>
                                        </>
                                    ) : null}
                                    {activeStep === 1 ? (
                                        <>
                                            <Typography variant="h3" pb={3} alignContent={"center"} textAlign={"center"} justifyContent={"center"}>
                                                {kioskTranslation('purchaseProcess.ticketsOnTheDay')}
                                            </Typography>
                                            <DateCalendar
                                                classes={{ root: styles.calendar }}
                                                disablePast
                                                defaultCalendarMonth={defaultCalendarMonth}
                                                defaultValue={defaultDateValue}
                                                value={selectedDate}
                                                onChange={onSelectedDateChanged}
                                                shouldDisableDate={isDateUnavailable}
                                                maxDate={selectedDate}
                                            />
                                        </>
                                    ) : null}
                                    {/* {activeStep === 99 ? (
                                        <Section
                                            className={{ root: styles.purchaseSection }}
                                            content={
                                                <>
                                                { isLoadingSessionList ? <LoadingButton />: 
                                                    <StepPurchaseSection 
                                                        selectedDate={selectedDate}
                                                        dataSource={selectedDateAvailableEventSessionBundlesOptionDataSource}
                                                        handleNextStep={handleNextStep}
                                                        onSelect={onHandleSelectSession}
                                                    />
                                                }
                                                </>
                                            }
                                        />
                                    ) : null} */}
                                    {activeStep === 2 ? (
                                        <Section
                                            className={{ root: styles.purchaseSection }}
                                            content={
                                                <>
                                                { isLoadingSessionList ? <LoadingButton />: 
                                                    <StepPurchaseEventSessionBundle 
                                                        selectedDate={selectedDate}
                                                        flattenedDataSource={selectedDateAvailableEventSessionBundleFlattenedDataSourceBySession}
                                                    />
                                                }
                                                </>
                                            }
                                        />
                                    ) : null}
                                </div>
                                <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
                                    {activeStep > 0 && 
                                        <Button
                                            variant="contained"
                                            color="inherit"
                                            disabled={activeStep === 0}
                                            onClick={handlePurchaseStepBack}
                                            sx={{ mr: 1 }}
                                            className={eventBuyStyles.stepperButton}
                                            classes={{
                                                disabled: eventBuyStyles.stepperButtonDisabled
                                            }}
                                        >
                                            {kioskTranslation('purchaseProcess.stepControlButtons.back')}
                                        </Button>
                                    }
                                    <Box sx={{ flex: '1 1 auto' }} />
                                    {activeStep === purchareSteps.length - 1 ? 
                                        <Stack direction='row' spacing={1}>
                                            <Button
                                                disabled={cartIsEmpty}
                                                startIcon={<DeleteIcon fontSize="medium" />}
                                                variant="contained"
                                                onClick={handleClearAll}
                                                className={classNames(eventBuyStyles.stepperButton, eventBuyStyles.clearAll)}
                                                classes={{
                                                    disabled: eventBuyStyles.stepperButtonDisabled
                                                }}
                                            >
                                                {kioskTranslation("purchaseProcess.stepControlButtons.clearAll")}
                                            </Button>
                                            <Button
                                                disabled={cartIsEmpty}
                                                startIcon={<AddShoppingCartIcon fontSize="medium" />}
                                                variant="contained"
                                                onClick={handlePaymentMethod}
                                                className={eventBuyStyles.stepperButton}
                                                classes={{
                                                    disabled: eventBuyStyles.stepperButtonDisabled
                                                }}
                                            >
                                            {kioskTranslation("purchaseProcess.stepControlButtons.complete")}
                                            </Button>
                                        </Stack>
                                        : 
                                        <Button 
                                            variant="contained"
                                            onClick={handlePurchaseStepNext} 
                                            disabled={disableNextStep[activeStep]}
                                            className={eventBuyStyles.stepperButton}
                                            classes={{
                                                disabled: eventBuyStyles.stepperButtonDisabled
                                            }}
                                        >
                                            {kioskTranslation('purchaseProcess.stepControlButtons.next')}
                                        </Button>
                                    }
                                    {/* <Button
                                        onClick={handleTestPrinter}
                                    >Debug Printing</Button> */}
                                </Box>
                                {flutterjsLogger}
                            </>
                        )}
                    </Stack>
                </Box>
            </Stack>
        </>
    )
};

export default EventBuyContainer;