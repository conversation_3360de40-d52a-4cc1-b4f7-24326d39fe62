import { getServerSideSitemapLegacy, ISitemapField } from 'next-sitemap'
import { GetServerSideProps } from 'next'
import ENDPOINT from '@/models/api/endpoint'
import EnumRequestHeader from '@/constants/enum/RequestHeader'
import EventAPIResult from '@/models/api/result/events/events'
import { GenericAPI } from '@stoneleigh/api-lib'

export const getServerSideProps: GetServerSideProps = async (context) => {
    const { locale } = context;
    const [ Events ] = await Promise.all([GenericAPI.requestAsync<EventAPIResult>(
        ENDPOINT.EventByPage(1),
        {
            method: "GET",
            headers: {
                [EnumRequestHeader.LANGUAGE]: locale || "en-US"
            }
        }
    ) as Promise<EventAPIResult> ]);

    let siteUrls: ISitemapField[] = [];
    if (Events.data) {
        siteUrls = siteUrls.concat(Events.data.list.map(event => ({
            loc: `https://incutix.com/event/${event.eventId}`,
            lastmod: new Date().toISOString()
        })));
    }
    return getServerSideSitemapLegacy(context, siteUrls);
}
// Default export to prevent next.js errors
export default function Sitemap() {}