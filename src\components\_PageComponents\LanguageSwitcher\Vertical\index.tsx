import styles from "@/components/_PageComponents/LanguageSwitcher/Vertical/vertical.module.scss";
import { useRouter } from "next/router";

import LanguageList from "@/constants/languages";

import { dispatch } from "@/redux/store";
import { useCallback } from "react";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import { setCookie } from "cookies-next";
import { OverlayActions } from "@/redux/slices/uiSlice";

const VerticalLanguageSwitcher = () => {
  const router = useRouter();
  const ChangeLocale = useCallback((locale: string) => {
    const { pathname, query, asPath } = router;
    dispatch(OverlayActions.closeNavigationMenu());
    void router.replace({ pathname, query }, asPath, {
        locale,
        scroll: false,
    });
    setCookie(EnumCookieKey.PREFERRED_LANGUAGE, locale);
  }, [router]);

  return (
    <div className={styles.vertical}>
      {LanguageList.items.map(item => {
        return (
          <button
            key={item.locale}
            onClick={() => ChangeLocale(item.locale)}
          >
            {item.displayName}
          </button>
        );
      })}
    </div>
  );
};

export default VerticalLanguageSwitcher;