import styles from "@/components/_CommonElements/SecondaryNavigation/secondaryNavigation.module.scss";
import classNames from "classnames";
import Link from "next/link";
import { ReactNode } from "react";

interface SecondaryNavigationProps {
    className?: string;
    children: ReactNode;
}

interface NavItemProps {
    className?: string;
    link: string;
    title: string;
}

export const NavItem = (props: NavItemProps) => {
    const {
        className,
        link,
        title
    } = props;
    return (
        <li className={classNames(className)}><Link href={link}>{title}</Link></li>
    )
}

export const SecondaryNavigation = (props: SecondaryNavigationProps) => {
    const {
        className,
        children
    } = props;

    return (
        <ul className={classNames(className, styles.navigation)}>
            {children}
        </ul>
    );
};