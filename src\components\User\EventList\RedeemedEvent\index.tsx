import styles from "@/components/User/EventList/eventList.module.scss";
import Link from "next/link";
import TicketWithEventDetail from "@/models/api/models/TicketWithEventDetail";
import { useRouter } from "next/router";
import Cookies from 'universal-cookie';
import RouteMap from "@/constants/config/RouteMap";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import dayjs from "dayjs";
import { useCallback, useMemo, useState } from "react";
import Card from "@mui/material/Card";
import CardMedia from "@mui/material/CardMedia";
import CardContent from "@mui/material/CardContent";
import CardActionArea from '@mui/material/CardActions';
import Box from "@mui/material/Box";
import { TextButton } from "@/components/_CommonElements/Button";
import QrCodeScannerIcon from '@mui/icons-material/QrCodeScanner';
import AdmissionQrCode from "@/components/ModalWindow/AdmissionQrCode";

export interface RedeemedEventProps {
    event: TicketWithEventDetail;
}
const RedeemedEvent = (props: RedeemedEventProps) => {
    const { t: navigationTranslation } = useTranslation(EnumTranslationJson.Navigation);
    
    const {
        event,
    } = props;

    const router = useRouter();
    const cookies = new Cookies();

    const [admissionQrCodeModalVisible, setAdmissionQrCodeModalVisible] = useState<boolean>(false);

    const sessionStartDateTime_LocalTime = useMemo(() => dayjs.utc(event.sessionStartDateTime).tz(event.eventTimeZone), [event.eventTimeZone, event.sessionStartDateTime]);
    const sessionEndDateTime_LocalTime = useMemo(() => dayjs.utc(event.sessionEndDateTime).tz(event.eventTimeZone), [event.eventTimeZone, event.sessionEndDateTime]);
    //const isSameDay = useMemo(() => sessionStartDateTime_LocalTime.isSame(sessionEndDateTime_LocalTime, 'day'), [sessionStartDateTime_LocalTime, sessionEndDateTime_LocalTime]);

    const hideAdmissionQrCodeModal = useCallback(() => {
        setAdmissionQrCodeModalVisible(false);
    }, []);

    return (
        <>
            <Card sx={{ maxWidth: "60rem", position: "relative" }} className={styles.boundTickets}>
                <div className={styles.imageHeader}>
                    <Link target="_blank" href={`${RouteMap.Event}/${event.eventId}`}>
                        <CardMedia
                            component="img"
                            image={event.eventPreviewContent}
                            alt={event.eventName}
                            className={styles.eventBannerImage}
                        />
                    </Link>
                    <div className={styles.eventBannerImageBlurredBackground} style={{ backgroundImage: `url(${event.eventPreviewContent})` }}></div>
                </div>
                <CardContent>
                    <Link target="_blank" href={`${RouteMap.Event}/${event.eventId}`}>
                        <h2 className={styles.name}>{event.eventName}</h2>
                    </Link>
                    <p>{event.eventBundleName}</p>
                    <p className={styles.dateTime}>
                        { sessionStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss (Z)") }
                        { event.sessionEndDateTime && <> ~ </>}
                        { sessionEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss (Z)") }
                    </p>
                </CardContent>
                <CardActionArea className={styles.actionBar} onClick={() => { setAdmissionQrCodeModalVisible(!admissionQrCodeModalVisible) }}>
                    <Box 
                        component="div"
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        width={"100%"}
                    >
                        <TextButton className={styles.actionButton} fullWidth variant="text" startIcon={<QrCodeScannerIcon />}>
                            {navigationTranslation("main.showTicket")}
                        </TextButton>
                    </Box>
                </CardActionArea>
            </Card>
            <AdmissionQrCode
                visible={admissionQrCodeModalVisible}
                qrCodeToken={event.qrCodeToken}
                onCancel={hideAdmissionQrCodeModal}
            />
        </>
    );
};

export default RedeemedEvent;