const queryFilter = (_query: { [key: string]: string | string[] | undefined }) => {
  const query: { [key: string]: string } = {};

  for (const [key, value] of Object.entries(_query)) {
    if (!value) continue;
    Array.isArray(value) ? query[key] = value[0] : query[key] = value;
  }

  return query;
};

const convertToQueryString = (query: { [key: string]: string | string[] | undefined }, extendQuery = false) => {
  if (!query) return "";
  let queryString: string = ``;

  const concatQueryParam = (key: string, string: string) => {
    queryString += Boolean(queryString) || extendQuery ? "&" : "?";
    queryString += `${key}=${encodeURIComponent(string)}`
  };

  for (const [key, value] of Object.entries(query)) {
    if (!value) continue;
    if (Array.isArray(value)) {
      value.forEach(each => {
        concatQueryParam(key, each);
      });
    } else {
      concatQueryParam(key, value);
    }
  }

  return queryString;
};

export { queryFilter, convertToQueryString };