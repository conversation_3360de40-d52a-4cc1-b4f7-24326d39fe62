import { useEffect } from "react";
import styles from "./overlay.module.scss";
import { AnimatePresence } from "framer-motion";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import { dispatch, useSelector } from "@/redux/store";
import { OverlayActions } from "@/redux/slices/uiSlice";
import FadeInOutBox from "@/components/_CommonElements/Motion/FadeInOutBox";

interface OverlayProps {
    children?: React.ReactNode;
}

const Overlay = (props: OverlayProps) => {
    const { children } = props;
    const visible = useSelector((state) => state.ui.overlay.navigation.visible);

    useEffect(() => {
        visible
            ? document.body.setAttribute("in-menu-overlay", "")
            : document.body.removeAttribute("in-menu-overlay");
    }, [visible]);

    return (
        <AnimatePresence>
            {visible && (
                <ClickAwayListener
                    onClickAway={(e: any) => {
                        if (e.target?.classList?.contains("header")) return;
                        dispatch(OverlayActions.closeNavigationMenu());
                    }}
                >
                    <div className={styles.divRef}>
                        {" "}
                        {/* keep this div for ClickAwayListener to function properly */}
                        <FadeInOutBox className={styles.overlay}>{children}</FadeInOutBox>
                    </div>
                </ClickAwayListener>
            )}
        </AnimatePresence>
    );
}

export default Overlay;
