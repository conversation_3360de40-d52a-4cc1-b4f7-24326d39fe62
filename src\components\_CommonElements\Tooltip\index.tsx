import { useState, FunctionComponent, ReactNode } from "react";
import M<PERSON><PERSON>ooltip from '@mui/material/Tooltip';
import ClickAwayListener from '@mui/material/ClickAwayListener';

interface TooltipProps {
  label: string;
  children: ReactNode;
  className?: string;
  arrow?: boolean;
  onClick?(): void;
  onClose?(): void;
  disableFocusListener?: boolean;
  disableHoverListener?: boolean;
  enterDelay?: number;
  followCursor?: boolean;
  placement?:	'bottom-end' | 'bottom-start' | 'bottom' | 'left-end' | 'left-start' | 'left' | 'right-end' | 'right-start' | 'right' | 'top-end' | 'top-start' | 'top';
  onlyClickToShow?: boolean;
}

const Tooltip: FunctionComponent<TooltipProps> = (props: TooltipProps) => {
  const {
    label,
    children,
    className,
    arrow,
    disableFocusListener,
    disableHoverListener,
    enterDelay,
    followCursor,
    placement = "top",
    onlyClickToShow,
  } = props;

  const [open, setOpen] = useState(false);

  const CustomTooltip = () => {
    return (<>
      <MUITooltip
        arrow={arrow}
        title={label}
        open={onlyClickToShow ? open : undefined}
        disableFocusListener={onlyClickToShow || disableFocusListener}
        disableHoverListener={onlyClickToShow || disableHoverListener}
        enterDelay={enterDelay}
        followCursor={followCursor}
        placement={placement}
      >
        <div style={{ display: "inline-block" }}>
          {children}
        </div>
      </MUITooltip>
    </>)
  };

  if (onlyClickToShow) {
    return (
      <ClickAwayListener onClickAway={() => setOpen(false)}>
        <div onClick={() => setOpen(true)}>
          <CustomTooltip/>
        </div>
      </ClickAwayListener>
    );
  }

  return <CustomTooltip/>;
};

export default Tooltip;