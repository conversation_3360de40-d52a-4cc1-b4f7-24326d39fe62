import type { GetServerSideProps, NextPage } from "next";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import ENDPOINT from "@/models/api/endpoint";
import i18n from "@/utils/i18n";
import Section from "@/components/_CommonElements/Section";
import EventHighLights from "@/components/EventHighLights";
import TrendingEventAPIResult from "@/models/api/result/events/trending";
import { GenericAPI } from "@stoneleigh/api-lib";
import Loading from "@/components/_CommonElements/Loading";
import dynamic from "next/dynamic";
import styles from "./index.module.scss";

const EventFeed = dynamic(() => import("@/components/Event/Feed"), { 
    loading: () => <Loading />,
    ssr: true
});

interface Props {
    fallback: {
        TrendingEvent: TrendingEventAPIResult,
    }
}

const Home: NextPage<Props> = (props) => {
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);
    const { fallback } = props;

    return (
        <>
            {fallback.TrendingEvent.data && fallback.TrendingEvent.data.list.length > 0 ? <EventHighLights events={fallback.TrendingEvent.data.list} /> : null}
            <div className={styles.container}>
                    <Section
                        labelSize="large"
                        label={eventTranslation("trendingEvents")}
                        content={
                            <EventFeed eventCategory={"EXPO"} isTrending />
                    }
                    />
                    <Section
                        labelSize="large"
                        label={eventTranslation("trendingMerchandiseEvents")}
                        content={
                            <EventFeed eventCategory={"MERCHANDISE"} isTrending />
                        }
                    /> 
                    <Section
                        labelSize="large"
                        label={eventTranslation("pastEvents")}
                        content={
                            <EventFeed eventCategory={"EXPO"} />
                        }
                    />
                    
            </div>
        </>
    );
};

export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [
        EnumTranslationJson.Event
    ],
    context,
    getServerSideProps: async context => {
        const { locale } = context;
        const [TrendingEvent] = await Promise.all([
            GenericAPI.requestAsync<TrendingEventAPIResult>(
                ENDPOINT.TrendingEventByPage(1),
                {
                    method: "GET",
                    headers: {
                        [EnumRequestHeader.LANGUAGE]: locale || "en-US"
                    }
                }
            ),
        ]);
        return {
            props: {
                fallback: {
                    TrendingEvent,
                }
            }
        }
    }
});
export default Home;
