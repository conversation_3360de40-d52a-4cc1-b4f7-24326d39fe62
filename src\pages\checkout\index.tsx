import type { NextPage } from "next";
import { GetServerSideProps } from "next";

import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import i18n from '@/utils/i18n';
import serverSideAuth from "@/utils/serverSideAuth";
import Section from "@/components/_CommonElements/Section";
import Checkout from "@/components/Checkout";

const CheckoutPage: NextPage = () => {
    return (
        <>
            <Section
                containerSize="wide"
                labelSize="xlarge"
                content={<Checkout />}
            />
        </>
    );
};
export const getServerSideProps: GetServerSideProps = serverSideAuth(
    {
        permission: "userOnly",
    },
    async (context) => i18n.GetServerSidePropsAsync({
        additionalFiles: [
            EnumTranslationJson.Checkout, 
            EnumTranslationJson.ShoppingCart, 
            EnumTranslationJson.Purchase,
            EnumTranslationJson.SnackBar
        ],
        context,
    })
);
export default CheckoutPage;
