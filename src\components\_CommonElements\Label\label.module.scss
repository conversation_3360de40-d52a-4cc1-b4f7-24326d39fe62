@use "sass:map";
@use "@/styles/utils/theme.module.scss" as theme;

$primary-color: map.get(theme.$color, "primary");
$text-color-contrast: map.get(theme.$color, "contrast-text");

.labelContainer {
    display: flex;
    .label {
        $height: 32px;
        $letter-spacing: 2px;
        word-break: normal;
        width: fit-content;
        padding: 3px 15px;
        height: $height;
        letter-spacing: $letter-spacing;
        border-radius: $height * 0.5;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $text-color-contrast;
        font-weight: bold;
        background-color: $primary-color;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    }
}