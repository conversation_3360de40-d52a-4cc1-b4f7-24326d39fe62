import React from "react";
import dayjs from "dayjs";
import { Chip, Stack, List, ListItem, Box } from "@mui/material";
import shoppingCartMenuStyles from "@/components/Event/ShoppingCart/ShoppingCartMenu/shoppingCartMenu.module.scss";
import ShoppingCartItemDisplay from "./ShoppingCartItemDisplay";
import { ShoppingCartSummarizedItem } from "@/models/api/models/ShoppingCart";
import OneTimePaymentOrderItem from "@/models/props/OneTimePaymentOrderItem";

interface ShoppingCartDataSource { [char: string]: ShoppingCartSummarizedItem[] }

interface ShoppingCartItemsProps {
    dataSource: ShoppingCartDataSource | OneTimePaymentOrderItem[];
    isCheckingOut?: boolean;
}

const ShoppingCartItems = (props: ShoppingCartItemsProps) => {
    const { dataSource, isCheckingOut = false } = props;
    const dataSourceKeys = Object.keys(dataSource);

    if (dataSourceKeys.length <= 0) {
        return null;
    }
    return (
        <Box className={shoppingCartMenuStyles.cartItemList}>
            <List>
                {('length' in dataSource) ? 
                    <ListItem>
                        <Stack 
                            spacing={1} 
                            justifyContent='flex-start' 
                            direction={'column'} 
                            flex={1}
                            className={shoppingCartMenuStyles.cartListItem}
                        >
                            {(dataSource as OneTimePaymentOrderItem[]).map(item =>
                                <ShoppingCartItemDisplay
                                    key={item.itemId}
                                    item={item}
                                    isCheckingOut={isCheckingOut}
                                />
                            )}
                        </Stack>
                    </ListItem>
                    :
                    Object.keys(dataSource as ShoppingCartDataSource).map(groupName => {
                        const items = dataSource[groupName];
                        if (items.length <= 0) {
                            return null;
                        }
                        const sessionStartDateTime_LocalTime = dayjs.utc(items[0].sessionStartDateTime).tz(items[0].eventTimeZone);
                        const sessionEndDateTime_LocalTime = dayjs.utc(items[0].sessionEndDateTime).tz(items[0].eventTimeZone);
                        const displayGroupName = `${sessionStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm")} ～ ${sessionEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm")}`;
                        return (
                            <ListItem key={`group_${displayGroupName}`}>
                                <Stack 
                                    spacing={1} 
                                    justifyContent='flex-start' 
                                    direction={'column'} 
                                    flex={1}
                                    className={shoppingCartMenuStyles.cartListItem}
                                >
                                    <Chip label={displayGroupName} />
                                    {items.map(item =>
                                        <ShoppingCartItemDisplay
                                            key={item.eventSessionBundleId}
                                            item={item}
                                            isCheckingOut={isCheckingOut}
                                        />
                                    )}
                                </Stack>
                            </ListItem>
                        );
                    })
                }
            </List>
        </Box>
    );
};
ShoppingCartItems.displayName = "ShoppingCartItems";
export default ShoppingCartItems;