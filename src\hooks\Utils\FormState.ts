import { useEffect, useMemo, useState } from "react";
import { buffer, debounceTime, distinct, map, Subject } from "rxjs";

type IFormField = {
  value: any;
  isRequired?: boolean;
  isValid?: boolean;
  isInteracted?: boolean;
  validator?: (value: any) => boolean;
  errorType?: "format" | "empty";
  errorMessage?: { [key in NonNullable<IFormField["errorType"]>]?: string };
};

type IForm<D> = {
  [key in keyof D]: IFormField;
};

type IFormStateOptions = {
  validationDelayInSeconds?: number;
};

const useFormState = <D,>(initialState: IForm<D>, options: IFormStateOptions = {}) => {
  type IInputFieldName = keyof D;
  type IInputParams = { field: IInputFieldName; value: IForm<D>[IInputFieldName]["value"] };

  const { validationDelayInSeconds = 0.5 } = options;

  const [data, _setData] = useState<IForm<D>>(initialState);

  // --------------------------------------------------------------------

  const inputSubject = useMemo(() => new Subject<IInputParams>(), []);
  const debounceInput = useMemo(() => inputSubject.pipe(debounceTime(validationDelayInSeconds * 1000)), []);
  const bufferInput = useMemo(
    () =>
      inputSubject.pipe(
        map((input) => input.field),
        distinct(undefined, debounceInput),
        buffer(debounceInput)
      ),
    []
  );

  useEffect(() => {
    const inputSubscriber = bufferInput.subscribe((fields) => {
      validateFields(fields);
    });

    return () => {
      inputSubscriber.unsubscribe();
    };
  }, []);

  const set = (input: IInputParams) => {
    inputSubject.next(input);
    setData(input);
  };

  // --------------------------------------------------------------------

  const setData = (input: IInputParams) => {
    const { field, value } = input;

    const newData = { ...data };

    newData[field].isInteracted = true;
    newData[field].value = value;

    _setData(newData);
  };

  // --------------------------------------------------------------------

  const changeValidator = ({ field, validator }: { field: IInputFieldName; validator: IFormField["validator"] }) => {
    const newData = { ...data };

    newData[field].validator = validator;

    _setData(newData);
  };

  // --------------------------------------------------------------------

  const validate = (data: IForm<D>, field: IInputFieldName) => {
    switch (true) {
      case data[field].isRequired && !data[field].value:
        data[field].isValid = false;
        data[field].errorType = "empty";
        break;
      case Boolean(data[field].validator):
        let isValid = data[field].validator!(data[field].value);
        data[field].isValid = isValid;
        data[field].errorType = isValid ? undefined : "format";
        break;
      default:
        data[field].isValid = true;
        data[field].errorType = undefined;
    }
  };

  // --------------------------------------------------------------------

  const validateFields = (fields: Array<IInputFieldName>) => {
    const newData = { ...data };

    fields.forEach((field) => {
      validate(newData, field);
    });

    _setData(newData);
  };

  // --------------------------------------------------------------------

  const checkIfAllValid = () => {
    validateFields(Object.keys(data) as Array<IInputFieldName>);

    for (let field in data) {
      if (!data[field].isValid) return false;
    }

    return true;
  };

  // --------------------------------------------------------------------

  const extractStateValueToObject = () => {
    let _data: { [key in IInputFieldName]: any } = {} as { [key in IInputFieldName]: any };
    for (const key in data) {
      _data[key] = data[key].value;
    }
    return _data;
  };

  return { data, set, changeValidator, validateFields, checkIfAllValid, extractStateValueToObject };
};

export { useFormState };
export type { IFormField };
