@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;


$button-size: 25px;
$button-size-small: 36px;

$font-size: 18px;
$font-size-small: 15px;

.container {
  margin: 10px 0;
  user-select: none;
  display: flex;
  flex-wrap: wrap;
    gap: 5px;
  & .pageButton {
    cursor: pointer;
    font: {
      size: $font-size;
      weight: bold;
    };
    padding: 1px;
    word-break: keep-all;
    color: map.get(theme.$color, "text");
    width: $button-size;
    height: $button-size;
    background-color: map.get(theme.$color, "background");

    &:hover {
      background-color: map.get(theme.$color, "background-hover");
    };

    &.active {
      cursor: context-menu;
      transform: scale(1.1);
      background-color: map.get(theme.$color, "background-hover");
      text-decoration: underline;
    };
  };

  &.small .pageButton {
    font-size: $font-size-small;
    width: $button-size-small;
    height: $button-size-small;
  };

  @include viewport.within("mobile") {
    & .pageButton {
      font-size: $font-size-small;
      width: $button-size-small;
      height: $button-size-small;
    };
  };
};