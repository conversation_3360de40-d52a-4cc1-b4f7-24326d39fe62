import type { GetServerSideProps, NextPage } from "next";
import { useCallback, useMemo, useState } from "react";
import AccountInfo from "@/components/User/AccountInfo";
import { LoadingCircle } from "@/components/_CommonElements/LoadingBar";
import { ModalActions } from "@/redux/slices/uiSlice";
import i18n from "@/utils/i18n";
import ENDPOINT from "@/models/api/endpoint";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import serverSideAuth from "@/utils/serverSideAuth";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import { BACKEND } from "@/constants/config";
import UserInfoAPIResult from "@/models/api/result/user/info";
import { dispatch } from "@/redux/store";
import PageContent from "@/components/_PageComponents/PageContent";

const ProfileSectionWrapper = dynamic(async () => (await import("@/components/User/ProfileSections")).ProfileSectionWrapper, { ssr: false });
const ModalChangePasswordWindow = dynamic(() => import("@/components/ModalWindow/ChangePassword"), { ssr: false });

const useUserInfo = () => {
    return BACKEND.Gateway.useQuery<UserInfoAPIResult>({
        url: ENDPOINT.GetUserInfo(),
        params: {
            queryKey: "profile",
        },
    });
};

const ProfilePage: NextPage = () => {
    const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
    const [openChangePasswordWindow, setChangePasswordWindow] = useState(false);
    const { data: res } = useUserInfo();
    const AccountInfoBox = useMemo(() => {
        if (!res) {
            return <LoadingCircle />
        }
        return (
            <AccountInfo
                user={res.data!}
                onClickValidatePhone={() => {
                    dispatch(ModalActions.openAccountVerificationModal());
                }}
                onClickChangePassword={() => {
                    setChangePasswordWindow(true);
                }}
            />
        );
    }, [res]);

    const hideChangePasswordModel = useCallback(() => {
        setChangePasswordWindow(false);
    }, []);

    const title = useMemo(() => seoTranslation("page.myProfile.title"), [seoTranslation]);
    return (
        <PageContent
            title={title}
            content={
                <>
                    <ProfileSectionWrapper>
                        { AccountInfoBox }
                    </ProfileSectionWrapper>
                    <ModalChangePasswordWindow
                        visible={openChangePasswordWindow}
                        onClose={hideChangePasswordModel}
                    />
                </>
            }
        />
    );
};
export const getServerSideProps: GetServerSideProps = serverSideAuth(
    {
        permission: "userOnly",
    },
    async (context) =>
        i18n.GetServerSidePropsAsync({
            additionalFiles: [EnumTranslationJson.Account, EnumTranslationJson.Profile],
            context,
        })
);

export default ProfilePage;
