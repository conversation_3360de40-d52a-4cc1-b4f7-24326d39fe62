import Head from 'next/head';
import { FRONTEND } from '@/constants/config';

interface PagaMetaProps {
  title?: string;
  robotIndex?: "index" | "noindex";
  description?: string;
  keywords?: string;
  websiteUrl?: string;
  imagePath?: string;
  imageType?: string;  
  imageHeight?: string;
  imageWidth?: string;
}

const PageMeta = (props: PagaMetaProps) => {
  const {
    robotIndex = "index",
    description = "INCUTix",
    keywords = "INCUTix",
    websiteUrl = `${FRONTEND.URL}${props.websiteUrl || ""}`,
    imagePath,
    imageType,
    imageHeight,
    imageWidth,
  } = props;

  const title = props.title ? `${props.title} - INCUTix`: "INCUTix";
  
  return (<>
    <Head>
      <meta name="robots" content={robotIndex} />

      {title && <>
        <title>{title}</title>
        <meta property="og:title" content={title} key="og:title" />
        <meta property="twitter:title" content={title} key="twitter:title" />
      </>}

      <meta property="og:type" content="website" key="og:type" />
      <meta property="og:url" content={websiteUrl} key="og:url" />

      <meta property="twitter:card" content="summary" key="twitter:card" />
      <meta property="twitter:url" content={websiteUrl} key="twitter:url" />

      {description && <>
        <meta name="description" content={description} key="description" />
        <meta property="og:description" content={description} key="og:description" />
        <meta property="twitter:description" content={description} key="twitter:description" />
      </>}

      {keywords &&
        <meta name="keywords" content={keywords} key="keywords" />
      }

      {imagePath && <>
        <meta property="og:image" content={imagePath} key="og:image" />
        <meta property="twitter:image" content={imagePath} key="twitter:image" />
      </>}

      {imageType &&
        <meta property="og:image:type" content={imageType} key="og:image:type" />}
      
      {imageWidth &&
        <meta property="og:image:width" content={imageWidth} key="og:image:width" />}
          
      {imageHeight &&
        <meta property="og:image:height" content={imageHeight} key="og:image:height" />}

    </Head>
  </>);
};

export default PageMeta;