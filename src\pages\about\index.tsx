import type { GetServerSideProps, NextPage } from 'next';

import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import { useTranslation } from 'next-i18next';
import i18n from '@/utils/i18n';
import Section from '@/components/_CommonElements/Section';
import styles from "./about.module.scss";

const AboutUs: NextPage = () => {
    const { t: aboutUsTranslation } = useTranslation(EnumTranslationJson.AboutUs);
    const title = aboutUsTranslation("title");
    const description = aboutUsTranslation("description", {
        foundedByContainerClassname: styles.foundedBy,
        foundedLabelClassname: styles.label,
        foundedLogoClassname: styles.logo,
    });
    return (<>
        <Section
            labelSize="xlarge"
            label={title}
            content={description}
        />
    </>);
}
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [EnumTranslationJson.AboutUs],
    context
});

export default AboutUs;