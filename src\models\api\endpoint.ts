import { <PERSON><PERSON><PERSON><PERSON> } from "@/constants/config";
import { EventType } from "@/constants/enum/EventType";

class ENDPOINT {
    static version = 'v1';
    static baseUrl: string = `${BACKEND.INTERNAL_API_ENDPOINT}/${this.version}`;
    public static readonly Login = () => `${ENDPOINT.baseUrl}/user/login`;
    public static readonly Logout = () => `${ENDPOINT.baseUrl}/user/logout`;
    public static readonly Register = () => `${ENDPOINT.baseUrl}/user/register`;
    public static readonly EventByPage = (page: number) => `${ENDPOINT.baseUrl}/events/${page}`;
    public static readonly TrendingEventByPage = (page: number, category: string = 'EXPO') => `${ENDPOINT.baseUrl}/events/trending/${category}/${page}`;
    public static readonly PastEventByPage = (page: number, category: string = 'EXPO') => `${ENDPOINT.baseUrl}/events/past/${category}/${page}`;
    public static readonly RecentEventByPage = (page: number, category: string = 'EXPO') => `${ENDPOINT.baseUrl}/events/recent/${category}/${page}`;
    public static readonly EventDetilsById = (eventId: string) => `${ENDPOINT.baseUrl}/events/${eventId}`;
    public static readonly EventBundlesById = (eventId: string) => `${ENDPOINT.baseUrl}/events/${eventId}/bundles`;
    public static readonly SessionDetail = (eventSessionId: string) => `${ENDPOINT.baseUrl}/events/sessions/${eventSessionId}`;
    public static readonly SessionByEventIdDate = (eventId: string,date:string) => `${ENDPOINT.baseUrl}/events/${eventId}/sessions?date=${date}`;
    public static readonly SessionBundlesByEventIdDate = (eventId: string,date:string) => `${ENDPOINT.baseUrl}/events/${eventId}/sessions/bundles?date=${date}`;
    public static readonly GetEventSupportPaymentGatwayMethods = (eventId: string) => `${ENDPOINT.baseUrl}/events/${eventId}/paymentmethods`;
    public static readonly OrderPayment = () => `${ENDPOINT.baseUrl}/order`;
    public static readonly OrderApplyDiscountCode = () => `${ENDPOINT.baseUrl}/order/discountCode`;
    public static readonly OrderRemoveDiscountCode = () => `${ENDPOINT.baseUrl}/order/discountCode`;
    public static readonly QueryPaymentOrderState = (id: string, gatewayName: string) => `${ENDPOINT.baseUrl}/order/${id}/paymentgateway/${gatewayName}`;
    public static readonly requestEmailVerificationCode = () => `${ENDPOINT.baseUrl}/user/email/verify/request`;
    public static readonly confirmEmailVerificationCode = () => `${ENDPOINT.baseUrl}/user/email/verify/confirm`;
    public static readonly forgotPasswordCode = () => `${ENDPOINT.baseUrl}/user/password/forgot/request`;
    public static readonly forgotPassword = () => `${ENDPOINT.baseUrl}/user/password/forgot/confirm`;
    public static readonly changePassword = () => `${ENDPOINT.baseUrl}/user/password/change`;
    public static readonly GetUserInfo = () => `${ENDPOINT.baseUrl}/user/info`;
    public static readonly GetUserQrCode = () => `${ENDPOINT.baseUrl}/user/qrcode`;
    public static readonly redeemTicket = () => `${ENDPOINT.baseUrl}/user/events/redeem`;
    public static readonly GetUserEventsByTicketStatus = (type: EventType) => `${ENDPOINT.baseUrl}/user/events/owned/${type}`;
    public static readonly GetUserOrder = () => `${ENDPOINT.baseUrl}/user/orders/paid`;
    public static readonly GetUserTicketOrdersByStatus = () => `${ENDPOINT.baseUrl}/user/orders`;
    public static readonly GetUserAppliedDiscountCode = () => `${ENDPOINT.baseUrl}/user/discounts/applied`;
    public static readonly GetUserMembershipSubscription = () => `${ENDPOINT.baseUrl}/user/membership`;
    public static readonly GetUserMembershipRequiredFields = () => `${ENDPOINT.baseUrl}/user/membership/fields`;
    public static readonly GetShoppingCart = () => `${ENDPOINT.baseUrl}/user/shoppingCart`;
    public static readonly UpdateShoppingCartItem = () => `${ENDPOINT.baseUrl}/user/shoppingCart`;
    public static readonly DeleteShoppingCartItem = (eventSessionBundleId: string) => `${ENDPOINT.baseUrl}/user/shoppingCart/${eventSessionBundleId}`;
    public static readonly DeleteAllShoppingCartItem = () => `${ENDPOINT.baseUrl}/user/shoppingCart`;
    public static readonly UploadUserPortrait = () => `${ENDPOINT.baseUrl}/user/portrait`;
    public static readonly GetOrderById = (id: string) => `${ENDPOINT.baseUrl}/order/${id}`;
    public static readonly onShow = () => `${ENDPOINT.baseUrl}/myEvent/onshow`;
    private static readonly _GetSaleReport = `${ENDPOINT.baseUrl}/admin/reports/sales`;

    public static readonly GetSaleReport = () => this._GetSaleReport;

    public static readonly CampaignProducts = (campaignId: string) => `${ENDPOINT.baseUrl}/adhoc/campaigns/${campaignId}/products`;
    public static readonly CampaignCheckout = (campaignId: string) => `${ENDPOINT.baseUrl}/adhoc/campaigns/${campaignId}/order`;
    public static readonly GetUserProductOrdersByStatus = () => `${ENDPOINT.baseUrl}/user/orders/product`;

    public static readonly GetMembershipTypes = () => `${ENDPOINT.baseUrl}/membership/types`;
    public static readonly MembershipSubscribeOrderPayment = () => `${ENDPOINT.baseUrl}/membership/subscribe`;
    public static readonly GetReservableSites = () => `${ENDPOINT.baseUrl}/sites`;
    public static readonly GetReservableSessions = (siteId: string) => `${ENDPOINT.baseUrl}/reserve/${siteId}/reservableSession`;
    public static readonly ApplyReserveSession = () => `${ENDPOINT.baseUrl}/reserve`;
    public static readonly CancelReservedSession = (reserveId: string) => `${ENDPOINT.baseUrl}/reserve/${reserveId}`;
    public static readonly GetUserReservedSession = (status: string) => `${ENDPOINT.baseUrl}/user/reserve/${status}`;

    // Kiosk
    public static readonly GetApplicationAvailableBundles = (eventId: string) => `${ENDPOINT.baseUrl}/application/events/${eventId}/tickets/type`;
}
export default ENDPOINT;
