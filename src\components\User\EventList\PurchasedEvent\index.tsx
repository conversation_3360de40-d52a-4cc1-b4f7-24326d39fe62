import styles from "@/components/User/EventList/eventList.module.scss";
import Link from "next/link";
import TicketWithEventDetail from "@/models/api/models/TicketWithEventDetail";
import { useCallback, useMemo } from 'react';
import { useState } from 'react';
import TicketRedemptionConfirmation from '@/components/ModalWindow/TicketRedemptionConfirmation';
import { TextButton } from "@/components/_CommonElements/Button";
import ShareDialog from '@/components/ModalWindow/Share';
import RouteMap from "@/constants/config/RouteMap";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import dayjs from "dayjs";
import { useSnackbar } from "notistack";
import Card from "@mui/material/Card";
import CardMedia from "@mui/material/CardMedia";
import CardContent from "@mui/material/CardContent";
import CardActions from '@mui/material/CardActions';

export interface PurchasedEventProps {
    event: TicketWithEventDetail;
    defaultShowTickets?: boolean;
    queryImportTicketToken?: string;
    withRedemptionReminder?: boolean;
}

const PurchasedEvent = (props: PurchasedEventProps) => {
    const {
        event,
        defaultShowTickets,
        withRedemptionReminder = true,
    } = props;

    const { enqueueSnackbar } = useSnackbar();
    const [targetTicketToken, setTargetTicketToken] = useState<string>(event.ticketToken);
    const [confirmModalVisible, setConfirmModalVisible] = useState<boolean>(false);
    const [shareModalVisible, setShareModalVisible] = useState<boolean>(false);
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { t: shareTranslation } = useTranslation(EnumTranslationJson.Share);

    const shareUrl = useMemo(() => {
        if (!targetTicketToken) {
            return undefined;
        }
        return RouteMap.ShareTicketTokenUrl(targetTicketToken, window.location.origin)
    }, [targetTicketToken]);

    const shareTicket = useCallback((ticketToken: string) => {
        setTargetTicketToken(ticketToken);
        setShareModalVisible(true);
    }, []);
    const hideShareModal = useCallback(() => {
        setShareModalVisible(false);
    }, []);
    const showConfirmModal = useCallback((ticketToken: string) => {
        setTargetTicketToken(ticketToken);
        setConfirmModalVisible(true);
    }, []);
    const hideConfirmModal = useCallback(() => {
        setConfirmModalVisible(false);
    }, []);
    const onRedeemSuccess = useCallback(() => {
        enqueueSnackbar(snackbarTranslation("messages.redeem.success"), { variant: "success" });
        setConfirmModalVisible(false);
    }, [snackbarTranslation, enqueueSnackbar]);

    const startDateTime_Unix = useMemo(() => dayjs.utc(event.sessionStartDateTime), [event.sessionStartDateTime]);
    const endDateTime_Unix = useMemo(() => dayjs.utc(event.sessionEndDateTime), [event.sessionEndDateTime]);
    const sessionStartDateTime_LocalTime = useMemo(() => startDateTime_Unix.tz(event.eventTimeZone), [event.eventTimeZone, startDateTime_Unix]);
    const sessionEndDateTime_LocalTime = useMemo(() => endDateTime_Unix.tz(event.eventTimeZone), [endDateTime_Unix, event.eventTimeZone]);
    //const isSameDay = useMemo(() => startDateTime_LocalTime.isSame(endDateTime_LocalTime, 'day'), [startDateTime_LocalTime, endDateTime_LocalTime]);

    return (
    <>
        <Card sx={{ maxWidth: "60rem", position: "relative" }} className={styles.purchasedTickets}>
            <div className={styles.imageHeader}>
                <Link target="_blank" href={`${RouteMap.Event}/${event.eventId}`}>
                    <CardMedia
                        component="img"
                        image={event.eventPreviewContent}
                        alt={event.eventName}
                        className={styles.eventBannerImage}
                    />
                </Link>
                <div className={styles.eventBannerImageBlurredBackground} style={{ backgroundImage: `url(${event.eventPreviewContent})` }}></div>
            </div>
            <CardContent>
                <Link target="_blank" href={`${RouteMap.Event}/${event.eventId}`}>
                    <h2 className={styles.name}>{event.eventName}</h2>
                </Link>
                <p>{event.eventBundleName}</p>
                <p className={styles.dateTime}>
                    { sessionStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss (Z)") }
                    { event.sessionEndDateTime && <> ~ </>}
                    { sessionEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss (Z)") }
                </p>
            </CardContent>
            <CardActions>
                <TextButton
                    letterSpacing={false}
                    label={profileTranslation("event.shareTicket.button")}
                    onClick={() => setShareModalVisible(!shareModalVisible)}
                />
                <TextButton
                    letterSpacing={false}
                    label={profileTranslation("event.redeemTicket.button")}
                    onClick={() => setConfirmModalVisible(!confirmModalVisible)}
                />
            </CardActions>
        </Card>
        <TicketRedemptionConfirmation
            visible={confirmModalVisible}
            onCancel={hideConfirmModal}
            onSuccess={onRedeemSuccess}
            ticketToken={targetTicketToken || ""}
        />
        <ShareDialog
            shareTitle={shareTranslation("title")}
            visible={shareModalVisible}
            onClose={hideShareModal}
            shareUrl={shareUrl || ""}
            shareContent={shareTranslation("content", { name: event.eventName, date: sessionStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss (Z)"), sharelink: shareUrl })}
            ticketToken={targetTicketToken}
            eventName={event.eventName}
            eventSession={event.sessionStartDateTime}
        />
    </>);
};

export default PurchasedEvent;