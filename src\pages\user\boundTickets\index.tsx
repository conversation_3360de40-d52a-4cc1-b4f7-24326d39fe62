import EventList from "@/components/User/EventList";
import eventListStyles from "@/components/User/EventList/eventList.module.scss";
import { ProfileSectionWrapper } from "@/components/User/ProfileSections";
import Tabs from "@/components/_CommonElements/Tabs";
import PageContent from "@/components/_PageComponents/PageContent";
import { EventListType } from "@/constants/enum/EventListType";
import { EventType } from "@/constants/enum/EventType";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTicket } from "@/hooks/Ticket/Ticket";
import UserEventTicketsAPIResult from "@/models/api/result/user/tickets";
import i18n from "@/utils/i18n";
import serverSideAuth from "@/utils/serverSideAuth";
import { Card, CardActions, CardContent, Skeleton } from "@mui/material";
import type { GetServerSideProps } from "next";
import { useTranslation } from "next-i18next";
import { useCallback, useEffect, useMemo, useState } from "react";

interface TabContentProps {
    type: EventType
}
const TabContent = ({ type }: TabContentProps) => {
    const [pageNum, setPageNum] = useState(1);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [ticketData, setTicketData] = useState<UserEventTicketsAPIResult>();
    const { getTicketsAsync } = useTicket();

    const scrollToTop = useCallback(() => {
        window.scrollTo({
            top: 0,
            behavior: "smooth"
        });
    }, []);

    const userUpcomingEventsResponse = useCallback(async (page: number) => {
        setIsLoading(true);
        const res = await getTicketsAsync(type, page);
        setTicketData(res);
        setIsLoading(false);
    }, []);

    const goToPage = useCallback((page: number) => {
        if (page != pageNum) {
            scrollToTop();
            userUpcomingEventsResponse(page);
            setPageNum(page);
        }
    }, [pageNum]);

    useEffect(() => {
        if (!ticketData) {
            void userUpcomingEventsResponse(pageNum);
        }
    }, [pageNum]);
    
    const tickets = ticketData?.data?.list ?? [];
    const maxPageNum = useMemo(() => {
        return ticketData?.data?.maxPage || 0;
    }, [ticketData]);

    return (
        <>
            {isLoading ?
                <Card sx={{ maxWidth: "60rem", position: "relative" }} className={eventListStyles.purchasedTickets}>
                    <Skeleton variant="rounded" width={"100%"} height={"15rem"} />
                    <CardContent>
                        <Skeleton variant="text" sx={{ fontSize: '1.5rem', width: '40%' }} />
                        <Skeleton variant="text" sx={{ fontSize: '1rem', width: '15%' }} />
                        <Skeleton variant="text" sx={{ fontSize: '1rem', width: '20%' }} />
                    </CardContent>
                    <CardActions>
                        
                    </CardActions>
                </Card>
                : 
                <EventList
                    userEvents={tickets}
                    listType={EventListType.REDEEMED}
                    pageNum={pageNum}
                    maxPageNum={maxPageNum}
                    setPageNum={goToPage}
                />
            }
        </>
    );
}
const BoundTicketsPage = () => {
    const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
    const tabs = useMemo(() => [
        {
            name: profileTranslation("event.active"),
            contents: <TabContent type={EventType.ACTIVE} />,
            active: true
        },
        {
            name: profileTranslation("event.upComing"),
            contents: <TabContent type={EventType.UPCOMING} />
        },
        {
            name: profileTranslation("event.end"),
            contents: <TabContent type={EventType.END} />
        }
    ], [profileTranslation]);
    const title = useMemo(() => seoTranslation("page.boundTickets.title"), [seoTranslation]);
    return (
        <PageContent
            title={title}
            content={
                <ProfileSectionWrapper>
                    <Tabs tabs={tabs} />
                </ProfileSectionWrapper>      
            }
        />
    );
};

export const getServerSideProps: GetServerSideProps = serverSideAuth(
    {
        permission: "userOnly",
    },
    async (context) =>
        i18n.GetServerSidePropsAsync({
            additionalFiles: [
                EnumTranslationJson.Account,
                EnumTranslationJson.Profile,
            ],
            context,
        })
);

export default BoundTicketsPage;
