import { createSlice, PayloadAction } from "@reduxjs/toolkit";
export interface KioskSliceState {
    email: string;
}

const initialState: KioskSliceState = {
    email: '',
};

export const kioskSlice = createSlice({
    name: "userInfo",
    initialState,
    reducers: {
        setEmail:(state, action: PayloadAction<string>) => {
            state.email = action.payload;
        },
        clearEmail:(state) => {
            state.email = '';
        },
    }
});
export const { setEmail, clearEmail } = kioskSlice.actions;

export default kioskSlice.reducer;