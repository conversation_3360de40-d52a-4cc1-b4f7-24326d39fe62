import { ShoppingCartItem } from "./ShoppingCart";

export interface DiscountCodeInfoForDisplay {
    discountValue: number;
    discountValueType: string;
    method: string;
}

type PropsShoppingCartItemOmitted = "eventId" | "eventTimeZone" | "sessionStartDateTime" | "sessionEndDateTime" | "eventBundleName" | "price";
export interface CartItemInfo extends Omit<ShoppingCartItem, PropsShoppingCartItemOmitted> {
    discountPrice: number;
}

export interface OrderInfoPreview {
    orderPrice: number;
    orderDiscountPrice: number;
    items: CartItemInfo[];
}

export interface DiscountApplyStatusAndEventBundlePriceInfo {
    discountCodeInfo: DiscountCodeInfoForDisplay;
    orderPreviewResult: OrderInfoPreview;
}