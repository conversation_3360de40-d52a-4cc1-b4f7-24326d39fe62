import { <PERSON>, Card, CardActions, Card<PERSON>ontent, CardHeader, CircularProgress, Container, Dialog, DialogActions, DialogContent, DialogTitle, Stack, Typography } from "@mui/material";
import Grid from '@mui/material/Unstable_Grid2';
import UndoIcon from '@mui/icons-material/Undo';
import classNames from "classnames";
import { useTranslation } from "next-i18next";
import { useCallback, useMemo, useRef, useState } from "react";

import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import MembershipSubscription from '@/models/api/models/MembershipSubscription';

import styles from "@/components/User/MembershipSubscriptionSection/membership.module.scss";
import { IconButton, TextButton } from "@/components/_CommonElements/Button";
import { BACKEND } from "@/constants/config";
import ENDPOINT from "@/models/api/endpoint";
import MembershipType from "@/models/api/models/MembershipType";
import MembershipTypesAPIResult from "@/models/api/result/membership/membershipTypes";
import dayjs, { Dayjs } from "dayjs";
import MembershipTypeDetail from "./MembershipTypeDetail";
import MembershipTypePurchaseDialogContent from "./MembershipTypePurchaseDialog";

import RequestUserQrCodeAPIResult from "@/models/api/result/user/qrcode";
import { addItem } from "@/redux/slices/oneTimePaymentSlice";
import { dispatch } from "@/redux/store";
import { useRouter } from "next/router";
import UserMembershipRequiredFieldsAPIResult from "@/models/api/result/user/membershipRequiredFields";
import UserInfoAPIResult from "@/models/api/result/user/info";
import UserAccount from "@/models/api/models/UserAccount";
import RouteMap from "@/constants/config/RouteMap";

interface CardFlipIconButtonProps {
  onClick?: () => void;
}

const CardFlipIconButton = (props: CardFlipIconButtonProps) => {
  const { onClick } = props;

  return (
    <>
      <div className={styles.cardFlipIcon} onClick={onClick}>
        <div className={styles.cardFlipIconFront}>
          <div></div>
        </div>
        <div className={styles.cardFlipIconBack}>
          <div className={styles.cardFlipIconQRCodeContainer}>
            <div className={styles.qrCodeDot}></div>
            <div className={styles.qrCodeDot}></div>
            <div className={styles.qrCodeDot}></div>
            <div className={styles.qrCodeDot}></div>
            <div className={styles.qrCodeDot}></div>
          </div>
        </div>
      </div>
    </>
  );
};

const useMembershipTypes = () => {
  return BACKEND.Gateway.useQuery<MembershipTypesAPIResult>({
      url: ENDPOINT.GetMembershipTypes(),
      params: {
          queryKey: "membershipTypes",
      },
  });
};
const useUserRequiredFields = () => {
  return BACKEND.Gateway.useQuery<UserMembershipRequiredFieldsAPIResult>({
    url: ENDPOINT.GetUserMembershipRequiredFields(),
    params: {
        queryKey: "membershipRequiredFields",
    },
  });
};
const useUserInfo = () => {
  return BACKEND.Gateway.useQuery<UserInfoAPIResult>({
      url: ENDPOINT.GetUserInfo(),
      params: {
          queryKey: "profile",
      },
      refetchInterval: false,
      cacheTime: 0,
      staleTime: 0
  });
};

interface MembershipSubscriptionProps {
    userMemberhsip: MembershipSubscription;
}

const MembershipSubscription = (props: MembershipSubscriptionProps) => {
  const {
    userMemberhsip
  } = props;

  const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
  const [ isAPILoading, setIsAPILoading ] = useState<boolean>(false);
  const [ selectedMembershipType, setSelectedMembershipType ] = useState<MembershipType>();
  const [ isOpenPurchaseDialog, setIsOpenPurchaseDialog ] = useState<boolean>(false);
  const [ isOpenNoticeCompleteUserInfoDialog, setIsOpenNoticeCompleteUserInfoDialog ] = useState<boolean>(false);
  const [ userQrCode, setUserQrCode ] = useState<string>("");
  const [ userQrCodeExpiryDateTime, setUserQrCodeExpiryDateTime ] = useState<Dayjs | null>(null);

  const { data: userInfo } = useUserInfo();
  const { data: membershipTypes, isLoading: fetchingMembershipTypes } = useMembershipTypes();
  const { data: requiredFields } = useUserRequiredFields();
  const router = useRouter();

  const flipCardButton = useRef<HTMLDivElement>(null);

  const isSubscribed = useMemo(() => {
    if (Object.keys(userMemberhsip).length > 0) {
      return userMemberhsip?.membershipSubscriptionId != "";
    }
    return false;
  }, []);
  const isApproved = useMemo(() => {
    if (isSubscribed) {
      return userMemberhsip?.state.toUpperCase() === "VERIFIED";
    }
    return false;
  }, [isSubscribed, userMemberhsip]);
  const subscribedMembershipTypeName = useMemo(() => {
    if (Object.keys(userMemberhsip).length > 0) {
      return userMemberhsip?.memberShipTypeName ?? "";
    }
  }, []);
  const startDateTime = useMemo(() => {
    if (Object.keys(userMemberhsip).length > 0) {
      if (userMemberhsip?.startDateTime > 0) {
        const timezone = userMemberhsip.region?.regionTimeZone ?? 'Asia/Hong_Kong';
        const startDateTime_LocalTime = dayjs.utc(userMemberhsip.startDateTime).tz(timezone);
        return startDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss");
      }
    }
    return '____-__-__ 00:00:00';
  }, []);
  const endDateTime = useMemo(() => {
    if (Object.keys(userMemberhsip).length > 0) {
      if (userMemberhsip?.startDateTime > 0) {
        const timezone = userMemberhsip.region?.regionTimeZone ?? 'Asia/Hong_Kong';
        const endDateTime_LocalTime = dayjs.utc(userMemberhsip?.endDateTime).tz(timezone);
        return endDateTime_LocalTime.format("YYYY-MM-DD HH:mm:ss");
      }
    }
    return '____-__-__ 00:00:00';
  }, []);
  const displayExpiryDateTime = useMemo(() => {
    if (userQrCodeExpiryDateTime) {
      const expInHKT = dayjs.utc(userQrCodeExpiryDateTime).tz("Asia/Hong_Kong");
      return expInHKT.format("YYYY-MM-DD HH:mm");
    } else {
      return "";
    }
  }, [userQrCodeExpiryDateTime]);

  const handleOpenPurchaseDialog = useCallback((memberShipType: MembershipType) => {
    let passedRequired = true;
    if (requiredFields?.data) {
      if (userInfo?.data) {
        const user = userInfo.data;
        requiredFields.data.forEach((fieldName) => {
          if (fieldName in user) {
            if (user[fieldName as keyof UserAccount] === "") passedRequired = false;
          }
        }); 
      }
    }

    if (passedRequired) {
      setIsOpenPurchaseDialog(true);
      setSelectedMembershipType(memberShipType);
    } else {
      setIsOpenNoticeCompleteUserInfoDialog(true);
    }
  }, [requiredFields, userInfo]);

  const handleClosePurchaseDialog = useCallback(() => {
    setIsOpenPurchaseDialog(false);
  }, []);

  const handleCloseNoticeCompleteUserInfoDialog = useCallback(() => {
    setIsOpenNoticeCompleteUserInfoDialog(false);
  }, []);

  const handleConfirmPurchaseDialog = useCallback(() => {
    console.log(selectedMembershipType);
    if (selectedMembershipType) {
      dispatch(addItem({
        itemId: selectedMembershipType.memberShipTypeId,
        itemName: selectedMembershipType.membershipTypeName,
        metadata: "",
        currency: "HKD",
        itemQuantity: 1,
        itemFee: selectedMembershipType.price
      }));
      void router.push('/checkout');
    }
  }, [selectedMembershipType]);

  const handleGotoFinishUserInfo = useCallback(() => {
    void router.push(RouteMap.UserProfile);
    setIsOpenNoticeCompleteUserInfoDialog(false);
  }, []);

  const handleRequestUserQrCodeFlipButton = useCallback(async () => {
    setIsAPILoading(true);
    flipCardButton.current?.classList.add(styles.flipped);
    const res = await BACKEND.Gateway.fetchQuery<RequestUserQrCodeAPIResult>({
      url: ENDPOINT.GetUserQrCode(),
      params: {
          queryKey: "userQrCOde",
      },
    });
    if (res) {
      if (res.data) {
        const qrcodeImage = "data:image/png;base64,"+res.data.code;
        setUserQrCode(qrcodeImage);
        setUserQrCodeExpiryDateTime(dayjs(res.data.expiryDateTime));
        setIsAPILoading(false);
      }
    }
  }, []);

  const handleFlipTheCardBack = useCallback(() => {
    flipCardButton.current?.classList.remove(styles.flipped);
    setUserQrCode("");
  }, []);

  return (
    <>
      <Container className={styles.membershipSubscriptionSectionContainer}>
        <Stack direction={"column"} rowGap={10} className={styles.membershipCardContainer}>
          <Box className={styles.membershipCard}>
            <Box className={styles.membershipCardFlippable} ref={flipCardButton}>
              <Card elevation={0} className={classNames(styles.currentSubscribed, styles["currentSubscribed--face"])}>
                <CardContent className={styles.membershipCardContent}>
                  <Stack direction={"column"}>
                    <Typography variant="h4">{subscribedMembershipTypeName}</Typography>
                    <Typography>{profileTranslation("membershipSubscription.subscribedDate", { "START_DATE_TIME": startDateTime })}</Typography>
                    <Typography>{profileTranslation("membershipSubscription.validTill", { "VALID_TILL": endDateTime })}</Typography>
                  </Stack>
                </CardContent>
                {!isSubscribed ? 
                  <Box className={styles.subscriptionMask}>
                    <Typography>{profileTranslation("membershipSubscription.hasNoSubscribed")}</Typography>
                  </Box> : 
                  <CardActions className={styles.membershipCardActions}>
                    <CardFlipIconButton onClick={handleRequestUserQrCodeFlipButton} />
                    <TextButton size="small" className={classNames(styles.cardActionButton, styles.cardActionButtonCancel)} disabled={!isApproved}>{profileTranslation("membershipSubscription.buttons.cancel")}</TextButton>
                  </CardActions>
                }
              </Card>
              <Card elevation={0} className={classNames(styles.currentSubscribed, styles["currentSubscribed--back"])}>
                <CardContent className={styles.membershipCardContent}>
                  <Stack direction={"column"} sx={{ alignItems: "center" }}>
                    <Typography>QR CODE</Typography>
                    <Typography>{profileTranslation("membershipSubscription.expiryDateTime", { "EXP_DATETIME": displayExpiryDateTime })}</Typography>
                    <Box className={styles.membershipQRCodeContainer}>
                      {userQrCode != "" ? 
                        <img alt={"qrcode"} src={userQrCode} /> : 
                        <CircularProgress />
                      }
                    </Box>
                  </Stack>
                </CardContent>
                <CardActions className={styles.membershipCardActions}>
                  <IconButton buttonSize="medium" inlineStyle={{ marginRight: "auto", color: "white" }} onClick={handleFlipTheCardBack}><UndoIcon /></IconButton>
                </CardActions>
              </Card>
            </Box>
          </Box>
          <Grid container spacing={2} columns={{ xs: 1, sm: 1, md: 1 }}>
            {fetchingMembershipTypes || isSubscribed ? null : 
              membershipTypes ? 
                membershipTypes.data!.map(mt => 
                  <Grid xs={1} md={1} key={`membership-type-${mt.memberShipTypeId}`}>
                    <Card>
                      <CardHeader title={mt.membershipTypeName} />
                      <CardContent className={styles.membershipTypeContent}>
                        <MembershipTypeDetail description={mt.membershipTypeDescription} />
                        <Box mt={1}>
                          <Typography>{profileTranslation("membershipSubscription.duration", { "DURATION": mt.duration })}</Typography>
                        </Box>
                      </CardContent>
                      <CardActions className={styles.membershipTypeActions}>
                        <TextButton
                          onClick={() => handleOpenPurchaseDialog(mt)}
                        >{profileTranslation("membershipSubscription.buttons.join")}</TextButton>
                      </CardActions>
                    </Card>
                  </Grid>
                )
                : null
            }
          </Grid>
        </Stack>
        <Dialog
          fullWidth={true}
          maxWidth={"lg"}
          open={isOpenPurchaseDialog}
          onClose={handleClosePurchaseDialog}
          scroll={"paper"}
        >
          <DialogTitle id={"membership-type-purchase-title"}>
            {profileTranslation("membershipSubscription.dialog.purchase.title", { "MEMBERSHIP_TYPE_NAME": selectedMembershipType?.membershipTypeName })}
          </DialogTitle>
          <MembershipTypePurchaseDialogContent membershipType={selectedMembershipType} onClose={handleClosePurchaseDialog} onConfirm={handleConfirmPurchaseDialog} />
        </Dialog>
        <Dialog
          open={isOpenNoticeCompleteUserInfoDialog}
          onClose={handleCloseNoticeCompleteUserInfoDialog}
        >
          <DialogTitle id={"notice-to-complete-user-info-title"}>
            {profileTranslation("membershipSubscription.dialog.requiredFields.title")}
          </DialogTitle>
          <DialogContent>{profileTranslation("membershipSubscription.dialog.requiredFields.body")}</DialogContent>
          <DialogActions>
            <TextButton onClick={handleCloseNoticeCompleteUserInfoDialog}>{profileTranslation("membershipSubscription.dialog.common.buttons.cancel")}</TextButton>
            <TextButton onClick={handleGotoFinishUserInfo}>{profileTranslation("membershipSubscription.dialog.requiredFields.buttons.goToComplete")}</TextButton>
          </DialogActions>
        </Dialog>
      </Container>
    </>
  );
};

export default MembershipSubscription;