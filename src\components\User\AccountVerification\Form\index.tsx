import React, { useImper<PERSON><PERSON><PERSON><PERSON> } from "react";
import { TextButton } from "@/components/_CommonElements/Button";
import { Alert, Grid, InputBase, Paper } from "@mui/material";
import { useState, useEffect, useCallback } from 'react';
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import ENDPOINT from "@/models/api/endpoint";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useSnackbar } from "notistack";
import API from "@/utils/API";
import RequestEmailVerificationAPIResult from "@/models/api/result/user/requestEmailVerification";
import ConfirmEmailVerificatonAPIResult from "@/models/api/result/user/confirmEmailVerification";
import { dispatch } from "@/redux/store";
import { setUserVerified } from "@/redux/slices/userInfoSlice";
import { BACKEND } from "@/constants/config";
import KeyIcon from '@mui/icons-material/Key';

export type AccountVerificationFormRef = {
    onSubmit: () => void;
}

const useConfirmVerificationCode = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async (code: string) => {
        const formData = new FormData();
        formData.append("ValidateCode", code);
        return await fetchAsync<ConfirmEmailVerificatonAPIResult>(
            ENDPOINT.confirmEmailVerificationCode(),
            {
                method: "POST",
                data: formData
            }
        );
    };
    return requestAsync;
};

const useRequestVerificationCode = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async () => {
        return await fetchAsync<RequestEmailVerificationAPIResult>(
            ENDPOINT.requestEmailVerificationCode(),
            {
                method: "POST"
            }
        );
    };
    return requestAsync;
};
interface Props {
    onClose: () => void;
}
const AccountVerificationForm = React.forwardRef<AccountVerificationFormRef, Props>((props, ref) => {
    const { onClose } = props;
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { t: commonTranslation } = useTranslation(EnumTranslationJson.Common);
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const [resendCounter, setResendCounter] = useState(0);
    const [codePrefix, setCodePrefix] = useState<string>("");
    const [code, setCode] = useState<string>("");
    const requestCodeAsync = useRequestVerificationCode();
    const confirmCodeAsync = useConfirmVerificationCode();

    const { enqueueSnackbar } = useSnackbar();
    const onCodeChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const code = e.target.value;
        setCode(code);
    }, []);

    useEffect(() => {
        const timer = setInterval(() => {
            setResendCounter((prevCounter) => (prevCounter > 0 ? prevCounter - 1 : 0));
        }, 1000);

        return () => {
            clearInterval(timer);
        };
    }, [resendCounter]);

    const onRequestButtonClick = useCallback(() => {
        void (async () => {
            try {
                const codeResponse = await requestCodeAsync();
                enqueueSnackbar(modalTranslation("modals.emailVerification.content.codeEmailSent"), { variant: "success" });
                setResendCounter(60);
                const codePrefix = codeResponse.data!.codePrefix;
                setCodePrefix(codePrefix);
            } catch (error: unknown) {
                const errorMessage = API.GetErrorMessage(error) || commonTranslation("tryAgain");
                enqueueSnackbar(errorMessage, { variant: "error" });
            }
        })();
    }, [commonTranslation, enqueueSnackbar, modalTranslation, requestCodeAsync]);

    const onSubmit = useCallback(async () => {
        try {
            await confirmCodeAsync(code);
            enqueueSnackbar(snackbarTranslation("messages.emailVerification.success"), { variant: "success" });
            dispatch(setUserVerified(true));
            onClose();
        } catch (error) {
            const errorMessage = API.GetErrorMessage(error) || commonTranslation("tryAgain");
            enqueueSnackbar(errorMessage, { variant: "error" });
        }
    }, [code, commonTranslation, confirmCodeAsync, enqueueSnackbar, snackbarTranslation, onClose]);

    useImperativeHandle(ref, () => ({
        onSubmit
    }));

    return (
        <>
            <Grid container sx={{paddingLeft: 2, paddingRight: 2, gap: 2}}>
                <Paper
                    elevation={4}
                    component="form"
                    sx={{ gap: 1, padding: 1, display: 'flex', alignItems: 'center', width: "100%" }}
                >
                    <KeyIcon />
                    { codePrefix &&
                        <>
                            <span>{codePrefix}</span>
                            <span> - </span>
                        </>
                    }
                    <InputBase
                        sx={{width: "100%"}}
                        placeholder={modalTranslation("modals.emailVerification.content.codeLabel")}
                        name="verificationCode"
                        value={code}
                        onChange={onCodeChanged}
                    />
                    <TextButton
                        label={resendCounter ? modalTranslation("resendAfter", { SEC: resendCounter }) : modalTranslation("obtainCode")}
                        size="small"
                        disabled={resendCounter > 0}
                        onClick={onRequestButtonClick}
                    />
                </Paper>
                <Alert icon={false} sx={{fontSize: "0.7rem"}} severity="info">
                    {modalTranslation("modals.emailVerification.content.reminder")}
                </Alert>
            </Grid>
        </>
    );
});
AccountVerificationForm.displayName = "AccountVerificationForm";

export default AccountVerificationForm;