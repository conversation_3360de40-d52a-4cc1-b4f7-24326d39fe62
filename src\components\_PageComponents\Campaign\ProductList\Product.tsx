import { Dispatch, SetStateAction, useMemo, useState, useCallback } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { Box, Button, ButtonGroup, Card, CardContent, CircularProgress, Stack, Typography } from "@mui/material";
import { useSnackbar } from "notistack";

import { IconButton } from "@/components/_CommonElements/Button";
import DeleteIcon from '@mui/icons-material/Delete';

import i18n from "@/utils/i18n";
import ProductDetails from "@/models/api/models/ProductDetails";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import CampaignShoppingCart from "@/models/api/models/CampaignShoppingCart";

import styles from "../campaign.module.scss";

interface ProductDisplayProps {
    product: ProductDetails;
    cartItems: CampaignShoppingCart[];
    functions: [
        decreaseItemByOne: (productId: string) => void,
        increaseItemByOne: (productId: string) => void,
        removeItemInCart: (productId: string) => void,
    ];
}

const ProductDisplay = (props: ProductDisplayProps) => {
    const { product, cartItems, functions } = props;
    const [decreaseItemByOne, increaseItemByOne, removeItemInCart] = functions;

    const [ isUpdating, setIsUpdating ] = useState(false);
    const [ isDeleting, setIsDeleting ] = useState(false);
    const router = useRouter();
    const { t: campaignTranslation } = useTranslation(EnumTranslationJson.Campaign);

    const isLoading = useMemo(() => isUpdating || isDeleting, [isUpdating, isDeleting]);
    const quantity = useMemo(() => cartItems.find(ci => ci.productId === product.productId)?.quantity ?? 0, [cartItems, product.productId]);
    const outOfStock = useMemo(() => product.inventoryQuantity <= 0, [product]);

    return (
        <Card key={`product-${product.productId}`} elevation={1} className={styles.itemCardContainer}>
            <Stack direction="column" className={styles.itemCardContent}>
                <Box className={styles.itemNameContainer}>
                    <CardContent>
                        <Stack>
                            <Typography>{product.productName} {outOfStock ? `(${campaignTranslation("detail.outOfStock")})` : null}</Typography>
                            <Typography sx={{ fontSize: "0.8rem" }}>{ i18n.GetCurrency('HKD', product.price * quantity, router.locale) } (@ { i18n.GetCurrency('HKD', product.price, router.locale) })</Typography>
                        </Stack>
                    </CardContent>
                </Box>
                <Box className={styles.itemQuantityContainer}>
                    {outOfStock ? null : 
                        <CardContent sx={{display: "flex", gap: 1}}>
                            <ButtonGroup size="small" aria-label="small outlined button group">
                                <Button disabled={isLoading || outOfStock} onClick={() => { decreaseItemByOne(product.productId) }}>-</Button>
                                <Button className={styles.quantity} disabled>{isUpdating ? <CircularProgress size="1rem" /> : quantity}</Button>
                                <Button disabled={isLoading || outOfStock} onClick={() => { increaseItemByOne(product.productId) }}>+</Button>
                            </ButtonGroup>
                            <IconButton disabled={isLoading || outOfStock} onClick={() => { removeItemInCart(product.productId) }}>
                                { isDeleting ? <CircularProgress size="1rem" /> : <DeleteIcon fontSize="medium" /> }
                            </IconButton>
                        </CardContent>
                    }
                </Box>
            </Stack>
        </Card>
    );
};

export default ProductDisplay;