import styles from "@/components/_CommonElements/Display/display.module.scss";
import classNames from "classnames";

interface LegendFieldSetProps {
  legend?: string;
  children: any;
  className?: string;
  fullWidth?: boolean;
  inlineStyle?: { [key: string]: any };
};

const LegendFieldSet = (props: LegendFieldSetProps) => {
  const {
    children,
    className,
    fullWidth = true,
    inlineStyle,
    legend,
  } = props;

  return (
  <fieldset 
    className={classNames(
      className,
      styles.fieldSet,
      fullWidth && styles.fullWidth
    )}
    style={inlineStyle}
  >
    <legend className={styles.legend}>
      <span>{legend}</span>
    </legend>
      
    {children}

  </fieldset>
  );
};

export default LegendFieldSet;