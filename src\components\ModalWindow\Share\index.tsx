import styles from "@/components/ModalWindow/Share/share.module.scss";
import Modal from "@/components/_CommonElements/Modal";
import { FieldContainer } from "@/components/_CommonElements/Input";
import Tooltip from '@/components/_CommonElements/Tooltip';
import Switch from "@/components/_CommonElements/Input/Switch";
import TextInput from "@/components/_CommonElements/Input/TextInput";
import { IconButton } from "@/components/_CommonElements/Button";
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { Grid } from "@mui/material";
import {
  EmailIcon,
  EmailShareButton,
  //FacebookMessengerIcon,
  //FacebookMessengerShareButton,
  LineIcon,
  LineShareButton,
  TelegramIcon,
  TelegramShareButton,
  WhatsappIcon,
  WhatsappShareButton
} from "react-share";
import { useState } from "react";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";

interface ShareDialogProps {
  visible: boolean;
  shareUrl: string;
  shareTitle: string;
  shareContent: string;
  eventName: string;
  eventSession: any;
  ticketToken?:string;
  onClose(): void;
};


const ShareDialog = (props: ShareDialogProps) => {
  const { 
    visible,
    shareUrl,
    shareTitle,
    shareContent,
    eventName,
    eventSession,
    ticketToken,
    onClose,
  } = props;

  const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
  const { t: shareTranslation } = useTranslation(EnumTranslationJson.Share);

  const text = shareContent;
  const textWithLink = `${text}`;

  const [urlOnly, seturlOnly] = useState(false);
  const [shareText, setShareText] = useState(textWithLink);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { checked } = event.target;
    seturlOnly(checked);
    setShareText(checked?shareUrl:textWithLink);
  };

  const emailTitle = shareTranslation("email.subject");
  const emailBody = shareTranslation("email.content");

  return (<>  
    <Modal
      visible={visible}
      hideCancelButton
      confirmButtonLabel={modalTranslation("buttons.close")}
      title={shareTitle}
      onConfirm={onClose}
      onBackdropClick={onClose}
      onCancel={onClose}
    >
      <Grid container item justifyContent="center" sx={{marginTop: "20px", marginBottom: "10px"}}>
        <div style={{width: "clamp(280px, 80vw, 400px)", margin: "0"}}>
          <TextInput
            readonly
            multiline
            minRows={6}
            maxRows={6}
            value={shareText}
            onClick={(e) => {e.target.select && e.target.select()}}
          />
        </div>
      </Grid>

      <Grid container justifyContent="center" alignItems="center" sx={{marginBottom: "10px"}}>
        <Switch 
          checked={urlOnly}
          onChange={handleChange}
        />
        {shareTranslation("onlyLink")}
      </Grid>

      <Grid container item justifyContent="center" columnSpacing={1}>
        <Grid item>
        <Tooltip label={shareTranslation("function.copy.actionSuccess")} onlyClickToShow placement="bottom">
            <Tooltip label={shareTranslation("function.copy.toolTip")} arrow>
              <IconButton
                className={styles.iconButton}
                buttonSize="small"
                onClick={() => {navigator.clipboard.writeText(shareText)}}
              >
                <ContentCopyIcon />
              </IconButton>
            </Tooltip>
          </Tooltip>
        </Grid>
        
        <Grid item>
          <Tooltip label={shareTranslation("platform.email")} arrow>
            <EmailShareButton
              url={''}
              subject={emailTitle}
              body={emailBody}
            >
              <EmailIcon size={32} round />
            </EmailShareButton>
          </Tooltip>
        </Grid>
        {/*
        <Grid item>
          <Tooltip label="Facebook" arrow>
            <FacebookMessengerShareButton
              url={shareUrl}
              appId="622130692166104"
            >
              <FacebookMessengerIcon size={32} round />
            </FacebookMessengerShareButton>
          </Tooltip>
        </Grid>
        <Grid item>
          <Tooltip label="Telegram" arrow>
            <TelegramShareButton
              url={shareText}
            >
              <TelegramIcon size={32} round />
            </TelegramShareButton>
          </Tooltip>
        </Grid>

        <Grid item>
          <Tooltip label="WhatsApp" arrow>
            <WhatsappShareButton
              url={shareText}
            >
              <WhatsappIcon size={32} round />
            </WhatsappShareButton>
          </Tooltip>
        </Grid>

        <Grid item>          
          <Tooltip label="LINE" arrow>
            <LineShareButton
              url={shareUrl}
              title={urlOnly?'':text}
            >
              <LineIcon size={32} round />
            </LineShareButton>
          </Tooltip>
        </Grid> */}
      </Grid>

    </Modal>
  </>);
};

export default ShareDialog;