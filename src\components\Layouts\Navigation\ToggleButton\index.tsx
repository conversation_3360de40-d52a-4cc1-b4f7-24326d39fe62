import { useCallback } from "react";
import styles from "./toggle.module.scss";

import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import { dispatch, useSelector } from "@/redux/store";
import { OverlayActions } from "@/redux/slices/uiSlice";

const ToggleButton = () => {
  const visible = useSelector((state) => state.ui.overlay.navigation.visible);
  const toggle = useCallback(() => {
    dispatch(visible ? OverlayActions.closeNavigationMenu() : OverlayActions.openNavigationMenu());
  }, [visible]);
  return (
    <button
      onClick={toggle}
      className={styles.button}
    >
      {visible 
        ? <CloseIcon className={styles.icon}/> 
        : <MenuIcon className={styles.icon}/>
      }
    </button>
  );
};

export default ToggleButton;
