import { FRONTEND } from "@/constants/config";
import AppConfig, { EnumConfigEnv } from "@stoneleigh/appconfig-lib";

/**
 * Get the production environment market URL as fallback
 * @returns Production market URL
 */
const getProductionMarketUrl = (): string => {
    return AppConfig.get({
        key: "frontend.marketUrl",
        env: EnumConfigEnv.PRD,
    }) || "https://market.incutix.com"; // Ultimate fallback if even production config is missing
};

/**
 * Map locale codes to language parameters for market URL
 */
const getLanguageParam = (locale: string): string => {
    switch (locale) {
        case "en-US":
            return "en";
        case "zh-HK":
            return "zh";
        case "th-TH":
            return "th";
        default:
            return "en"; // fallback to English
    }
};

/**
 * Generate market URL with user hash and language parameters
 * @param userIdHash - User's hashed ID (optional)
 * @param locale - Current locale (optional, defaults to "en-US")
 * @returns Complete market URL with parameters
 */
export const generateMarketUrl = (userIdHash?: string, locale?: string): string => {
    const baseUrl = FRONTEND.MARKET_URL;
    const languageParam = getLanguageParam(locale || "en-US");

    // Use production environment market URL as fallback if current environment URL is not configured
    const finalBaseUrl = baseUrl || getProductionMarketUrl();

    try {
        const url = new URL(finalBaseUrl);

        // Add language parameter
        url.searchParams.set('locales', languageParam);

        // Add user parameter if available
        if (userIdHash) {
            url.searchParams.set('user', btoa(userIdHash));
        }

        return url.toString();
    } catch (error) {
        console.error("Error generating market URL:", error);
        return finalBaseUrl;
    }
};

/**
 * Get the base market URL for the current environment
 * @returns Base market URL without parameters
 */
export const getMarketBaseUrl = (): string => {
    return FRONTEND.MARKET_URL || getProductionMarketUrl();
};

/**
 * Generate market order URL for viewing order details
 * @param orderId - The order ID to view
 * @param locale - Current locale (optional, defaults to "en-US")
 * @returns Complete market order URL
 */
export const generateMarketOrderUrl = (orderId: string, locale?: string): string => {
    const baseUrl = FRONTEND.MARKET_URL;
    const languageParam = getLanguageParam(locale || "en-US");

    // Use production environment market URL as fallback if current environment URL is not configured
    const finalBaseUrl = baseUrl || getProductionMarketUrl();

    try {
        const url = new URL(`${finalBaseUrl}/order/${orderId}`);

        // Add language parameter
        url.searchParams.set('locales', languageParam);

        return url.toString();
    } catch (error) {
        console.error("Error generating market order URL:", error);
        return `${finalBaseUrl}/order/${orderId}`;
    }
};
