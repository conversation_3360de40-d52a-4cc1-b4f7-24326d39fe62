import { EnumLocale } from "@/constants/enum/Locale";
import { DefaultTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { GetServerSidePropsContext, GetStaticPropsContext, PreviewData } from "next";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { ParsedUrlQuery } from "querystring";

import API from "./API";

interface CustomGetStaticPropsResult {
    props: { [key in string]: unknown };
    revalidate?: number | boolean | undefined;
}
interface CustomGetServerSidePropsResult {
    props: { [key in string]: unknown };
}
interface i18nGetStaticProps {
    context: GetStaticPropsContext<ParsedUrlQuery, PreviewData>,
    additionalFiles?: string[],
    getStaticProps?:(context: GetStaticPropsContext) => Promise<CustomGetStaticPropsResult> | CustomGetStaticPropsResult
}
interface i18nGetServerSideProps {
    context: GetServerSidePropsContext<ParsedUrlQuery, PreviewData>,
    additionalFiles?: string[],
    getServerSideProps?: (context: GetServerSidePropsContext<ParsedUrlQuery, PreviewData>) => Promise<CustomGetServerSidePropsResult> | CustomGetServerSidePropsResult
}
class i18n {
    static GetStaticPropsAsync = async ({additionalFiles, context, getStaticProps}: i18nGetStaticProps) => {
        const { locale } = context;
        const staticProps = getStaticProps ? (await getStaticProps(context)) : { props : {} };
        const i18nProps = await i18n.GetTranslationAsync(locale!, additionalFiles);
        return {
            props: {
                ...i18nProps,
                ...staticProps.props
            }
        }
    }
    static GetServerSidePropsAsync = async ({additionalFiles, context, getServerSideProps}: i18nGetServerSideProps) => {
        const { locale } = context;
        const serverSideProps = getServerSideProps ? (await getServerSideProps(context)) : { props : {} };
        const i18nProps = await i18n.GetTranslationAsync(locale!, additionalFiles);
        const cookies = API.GetServerSideCookies(context);
        return {
            props: {
                ...i18nProps,
                ...serverSideProps.props,
                ...cookies ? {cookies} : {}
            }
        } as {
            props: { [key in string]: unknown }
        }
    }
    static GetTranslationAsync = (locale: string, additionalFiles: string[] = []) => {
        return serverSideTranslations(locale, [
            ...DefaultTranslationJson,
            ...additionalFiles,
        ]);
    };
    public static GetCurrency = (currency: string, amount: number, locale?: string) => {
        console.log("currency",currency);
        console.log("locale",locale);
        return new Intl.NumberFormat(this.GetCurrencyISO(currency, locale || EnumLocale.English), { style: 'currency', currency: currency }).format(amount);
    }
    private static GetCurrencyISO = (currency: string, locale: string) => {
        switch (currency) {
            case "IDR":
                return "id-ID";
            case "MYR":
                return "ms-MY";
            case "THB":
                return "th-TH";
            case "HKD":
                return "zh-HK";
        }
        return locale;
    }
}
export default i18n;