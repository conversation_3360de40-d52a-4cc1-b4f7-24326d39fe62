import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import EventBundle from '@/models/api/models/EventBundle';
import { chunk, chunkTo } from '@/utils/Array';
import i18n from '@/utils/i18n';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import styles from "./rateCard.module.scss";

interface RateCardProps {
    dataSource: EventBundle[];
}
const RateCard = ({dataSource}: RateCardProps) => {
    const router = useRouter();
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);
    const [ openMore, setOpenMore ] = useState<boolean>(false);

    const splitedDataSource = useMemo(() => {
        if (dataSource.length === 0) return [[]];
        if (dataSource.length < 5) return chunkTo<EventBundle>(dataSource, 1);

        const chunked = chunk<EventBundle>(dataSource, 5);
        const others = chunked.reduce((others, data, rowIndex) => {
            if (rowIndex > 0)
                others = others.concat([ ...data ]);
            return others;
        }, []);
        return [
            chunked[0],
            others,
        ];
    }, [dataSource]);

    // DEBUG
    // console.log(splitedDataSource);

    return (
        <TableContainer component={Paper}>
            <Table aria-label="Rate Card">
                <TableHead>
                    <TableRow>
                        <TableCell>{eventTranslation("detail.packageLabel")}</TableCell>
                        <TableCell>{eventTranslation("detail.priceLabel")}</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {
                        splitedDataSource.map((rowItem, rowIndex) => 
                            {
                                if (rowIndex === 0) {
                                    return (
                                        rowItem.map(each => (
                                            <TableRow key={`r-${rowIndex}-i-${each.eventBundleId}`}>
                                                <TableCell className={styles.name}>
                                                    {each.eventBundleName}
                                                </TableCell>
                                                <TableCell className={styles.price}>
                                                    <span>
                                                        {i18n.GetCurrency(each.currency, each.price, router.locale)}
                                                    </span>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )
                                } else {
                                    return (
                                        openMore === false ?
                                            <TableRow key={`package-show-more`}>
                                                <TableCell colSpan={2} sx={{ textAlign: "center", cursor: "pointer" }} onClick={() => setOpenMore(true)}><ExpandMore /></TableCell>
                                            </TableRow>
                                            : 
                                            <>
                                                {rowItem.map(each => (
                                                    <TableRow key={`r-${rowIndex}-i-${each.eventBundleId}`}>
                                                        <TableCell className={styles.name}>
                                                            {each.eventBundleName}
                                                        </TableCell>
                                                        <TableCell className={styles.price}>
                                                            <span>
                                                                {i18n.GetCurrency(each.currency, each.price, router.locale)}
                                                            </span>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                                <TableRow key={`package-show-less`}>
                                                    <TableCell colSpan={2} sx={{ textAlign: "center", cursor: "pointer" }} onClick={() => setOpenMore(false)}><ExpandLess /></TableCell>
                                                </TableRow>
                                            </>
                                        
                                    )
                                }
                            }
                        )
                    }
                </TableBody>
            </Table>
        </TableContainer>
    );
};
export default RateCard;