import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {ipData} from '@/utils/IpGetter';

export interface IdentitySliceState {
    jwt:string,
    userName:string,
    userId:string,
    expiry:Date,
    fingerprint:string,
    ipData:ipData|undefined,
    liveTicketToken:string
}

const initialState: IdentitySliceState = {
    jwt:'',
    userName:'',
    userId:'',
    expiry:new Date(),
    fingerprint:'',
    ipData:undefined,
    liveTicketToken:''
};

export const identitySlice = createSlice({
    name: "identity",
    initialState,
    reducers: {
        setJwt: (state, action: PayloadAction<string>) => {
            state.jwt = action.payload;
        },
        setName: (state, action: PayloadAction<string>) => {
            state.userName = action.payload;
        },
        setId: (state, action: PayloadAction<string>) => {
            state.userId = action.payload;
        },
        setExpiry: (state, action: PayloadAction<Date>) => {
            state.expiry = action.payload;
        },
        setFingerprint: (state, action: PayloadAction<string>) => {
            state.fingerprint = action.payload;
        },
        setLiveTicketToken: (state, action: PayloadAction<string>) => {
            state.liveTicketToken = action.payload;
        },
        clearAll: (state) => {
            state.jwt = '';
            state.expiry = new Date();
            state.fingerprint = '';
            state.userName='';
            state.userId='';
            state.ipData=undefined;
            state.liveTicketToken='';
        },
    }
});

export const { setJwt, setExpiry, setFingerprint, clearAll, setName, setId } = identitySlice.actions;

export default identitySlice.reducer;