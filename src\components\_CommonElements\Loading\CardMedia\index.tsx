import { Card, CardActionArea, CardContent, Typography } from '@mui/material';
import Skeleton from '@mui/material/Skeleton';

import styles from '@/components/Event/Event/event.module.scss';

export default function LoadingCardMeida() {
    return (
        <Card elevation={2}>
            <CardActionArea className={styles.cardActionArea}>
                <Skeleton variant="rectangular" width={"100%"}>
                    <div style={{ paddingTop: "56.25%" }} />
                </Skeleton>
                <CardContent className={styles.cardContentContainer}>
                    <Skeleton width={"100%"}>
                        <Typography variant="subtitle2" className={styles.eventDate}>.</Typography>
                    </Skeleton>
                    <Skeleton variant="rectangular" width={"100%"}>
                        <div className={styles.cardContent} />
                    </Skeleton>
                </CardContent>
            </CardActionArea>
        </Card>
    );
}