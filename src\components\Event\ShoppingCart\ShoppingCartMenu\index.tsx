import React, { useCallback, useMemo } from "react";
import { useSelector } from "@/redux/store";
import MuiBadge from "@mui/material/Badge";
import MuiButton from "@mui/material/Button";
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import styles from "@/components/_PageComponents/Fabs/FabButton/button.module.scss";
import { usePathname } from "next/navigation";
import classNames from "classnames";

const ShoppingCartMenu = () => {
    const cartItems = useSelector(state => state.cart.items);
    const numOfItemsInCart = useMemo(() => cartItems.reduce((numInCart: number, item) => numInCart += item.quantity, 0), [cartItems]);
    const pathname = usePathname();
    const isEventPage = useMemo(() => pathname.includes('event'), [pathname]);
    const scrollToBuySection = useCallback(() => {
        window.location.href = "#buy";
    }, []);
    if (!isEventPage) {
        return null;
    }
    return (
        <MuiBadge 
            badgeContent={numOfItemsInCart} 
            color="secondary"
            sx={{
                '& .MuiBadge-badge': {
                    backgroundColor: "#4db848"
                }
            }}
        >
            <MuiButton id="hello" className={classNames(styles.button, styles.contained)} onClick={scrollToBuySection}>
                <ShoppingCartIcon />
            </MuiButton>
        </MuiBadge>
    );
};

export default ShoppingCartMenu;
