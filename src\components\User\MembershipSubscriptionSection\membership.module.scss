@use 'sass:map';
@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;
@import "rfs/scss";

$breakpoint: 480px;

.membershipSubscriptionSectionContainer {
    margin-bottom: 1vh;
    padding: 0;
    height: 100%;
    max-height: 100svh;
    min-height: 45svh;
}

.membershipCardContainer {
    align-items: center;
}

.membershipCard {
    perspective: 1000px;
    min-width: 510px;
    max-width: 510px;
    min-height: 280px;
    max-height: 280px;

    @media (max-width: $breakpoint) {
        min-width: 300px;
        max-width: 100svw;
        width: 100%;
        min-height: 180px;
        max-height: 180px;
    };
}

.membershipCardFlippable {
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.6s ease-out;
    transform: rotateY(0deg);

    &.flipped {
        transform: rotateY(180deg);
    }
}

.currentSubscribed {
    display: flex;
    flex-direction: column;
    border: 1px solid;
    border-radius: 12px;
    box-shadow: 8px 8px 8px 0px rgba(0, 0, 0, 0.2);

    align-self: center;

    position: absolute;
    backface-visibility: hidden;
    top: 0;
    left: 0;
    width: 100%;

    &--face {
        @extend .currentSubscribed;
        z-index: 2;
        transform: rotateY(0deg);

        & .membershipCardContent {
            display: flex;
            flex: 1;
            flex-direction: column;
            background: linear-gradient(300deg, rgba(252,186,3,1) 0%, rgba(255,119,0,1) 5%, rgba(77,184,64,1) 70%, rgba(255,186,2,1) 90%, rgba(252,186,3,1) 100%);
    
            & h4 {
                padding: 3rem 3rem 3rem 0;
                text-shadow: 0px 0px 5px #e3e3e385, 4px 4px 10px rgb(0 0 0 / 67%);
                background-image: url(https://media.giphy.com/media/26BROrSHlmyzzHf3i/giphy.gif);
                background-size: cover;
                color: transparent;
                background-clip: text;
                font-weight: 900;
            }
    
            & p {
                font-size: 0.8rem;
                font-weight: normal;
            }
        }
    }

    &--back {
        @extend .currentSubscribed;
        transform: rotateY(180deg);

        & .membershipCardContent {
            display: flex;
            flex: 1;
            flex-direction: column;
            background: linear-gradient(300deg, rgba(252,186,3,1) 0%, rgba(255,119,0,1) 5%, rgba(77,184,64,1) 70%, rgba(255,186,2,1) 90%, rgba(252,186,3,1) 100%);
    
            & h4 {
                padding: 3rem 3rem 3rem 0;
                text-shadow: 0px 0px 5px #e3e3e385, 4px 4px 10px rgb(0 0 0 / 67%);
                background-image: url(https://media.giphy.com/media/26BROrSHlmyzzHf3i/giphy.gif);
                background-size: cover;
                color: transparent;
                background-clip: text;
                font-weight: 900;
            }
    
            & p {
                font-size: 0.8rem;
                font-weight: normal;
            }
        }

        & .membershipQRCodeContainer {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 180px;
            height: 180px;
            padding: 1rem;
            margin-top: 1rem;
            background-color: rgb(255, 255, 255);
            border-radius: 0.6rem;
            box-shadow: 0px 0px 10px rgb(255 227 216 / 70%);

            @media (max-width: $breakpoint) {
                width: 70dvw;
                height: 30dvh;
            }

            & img {
                height: 100%;
            }
        }
    }

    & .membershipCardActions {
        justify-content: flex-end;
        background: #071f3d;

        & .cardActionButton {
            background-color: rgb(39 39 39);
            color: rgb(255, 255, 250);
            border-radius: 0.6rem;
            font-size: 0.6rem;

            &::before {
                border-width: 1px;
                border-radius: 0.6rem;
                border-color: #cecece !important;
            }

            &:hover {
                border-radius: 0.6rem;
                box-shadow: 0 0 10px #eda15e;
                background-color: #cecece;
            }

            &.cardActionButtonCancel {
                background-color: #c23929;

                &:hover {
                    border-radius: 0.6rem;
                    box-shadow: 0 0 10px #eda15e;
                    background-color: #c05e53;
                }
            }
        }
    }

    & .subscriptionMask {
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, .7);
        backdrop-filter: blur(2px);
        display: flex;
        justify-content: center;
        align-items: center;

        & p {
            color: white;
        }
    }

    & p {
        font-weight: bold;
    }
}

.membershipTypeContent {
    font-size: 0.8rem;
    
    & .listItem {
        display: flex;
        flex-direction: row;
        gap: 10px;
        margin-left: 20px;

        &:before {
            content: '∙';
            display: block;
        }
    }
    
}

.membershipTypeActions {
    justify-content: center;
}

.cardFlipIcon {
    perspective: 1000px;
    width: 32px;
    height: 32px;
    position: relative;
    margin: 0.6rem;
    margin-right: auto;
    cursor: pointer;

    & .cardFlipIconFront {
        position: absolute;
        top: 30%;
        left: 0;
        background: white;
        border: 1px solid white;
        border-radius: 0.2rem;
        width: 100%;
        height: 70%;
        z-index: 2;
        transform-origin: bottom right;
        box-shadow: 1px 1px 3px rgba(0, 0, 0, .6);

        & div {
            width: 40%;
            height: 25%;
            background: #071f3d;
            margin: 5px 0 0 2px;
            border-radius: 1px;
        }
    }

    & .cardFlipIconBack {
        position: absolute;
        top: 30%;
        left: 0;
        background: #b0b0b0;
        border: 1px solid white;
        border-radius: 0.2rem;
        width: 100%;
        height: 70%;
        box-shadow: 1px 1px 3px rgba(0, 0, 0, .6);
        transform-origin: bottom right;
        transform: translate(0px, 0px) rotateZ(20deg);

        & .cardFlipIconQRCodeContainer {
            width: 15px;
            height: 15px;
            background: #ffffff;
            position: relative;
            top: 3px;
            left: 25%;
            border-radius: 2px;
        }
        & .qrCodeDot {
            position: absolute;
            background-color: black;

            &:nth-child(1) {
                width: 6px;
                height: 5px;
                top: 0;
                left: 0;
            }

            &:nth-child(2) {
                width: 4px;
                height: 4px;
                top: 0;
                right: 0;
            }

            &:nth-child(3) {
                width: 5px;
                height: 7px;
                top: 5px;
                left: 5px;
            }

            &:nth-child(4) {
                width: 4px;
                height: 4px;
                bottom: 0;
                left: 0;
            }

            &:nth-child(5) {
                width: 5px;
                height: 7px;
                bottom: 0;
                right: 0;
            }
        }
    }

    &:hover {
        & .cardFlipIconFront {
            animation: cardFlipFrontRollToBack 1s ease-in-out 1 normal forwards;
        }

        & .cardFlipIconBack {
            animation: cardFlipBackRollToFront 1s ease-in-out 1 normal forwards;
        }
    }
}

@keyframes cardFlipFrontRollToBack {
    0% {
        transform-origin: bottom right;
        transform: translate(0px, 0px) rotateZ(0deg);
        z-index: 2;
    }

    50% {
        z-index: initial;
        transform: translate(-10px, -10px) rotateZ(10deg);
    }

    100% {
        transform: translate(0px, 0px) rotateZ(20deg);
        z-index: initial;
    }
}

@keyframes cardFlipBackRollToFront {
    0% {
        transform: translate(0px, 0px) rotateZ(20deg);
        z-index: initial;
    }

    50% {
        z-index: 2;
        transform: translate(10px, 0px) rotateZ(10deg);
    }

    100% {
        transform: translate(0px, 0px) rotateZ(0deg);
        z-index: 2;
    }
}