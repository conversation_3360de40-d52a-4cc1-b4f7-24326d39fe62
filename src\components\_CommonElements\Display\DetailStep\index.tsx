import styles from "@/components/_CommonElements/Display/display.module.scss";
import classNames from "classnames";

interface DetailStepProps {
    className?: string;
    label: string;
    image: string;
    numbering: number;
    contents: React.ReactNode;
}

const DetailStep = (props: DetailStepProps) => {
    const {
        className,
        label,
        image,
        numbering,
        contents
    } = props;

    return (
        <div className={classNames(className, styles.detailStep)}>
            <figure className={styles.imageContainer}>
                <img src={`${image}?v=2`} />
            </figure>
            <div>
                <div className={styles.titleRow}><span className={styles.numbering}>STEP {numbering}</span><span className={styles.title}>{label}</span></div>
                {contents}
            </div>
        </div>
    );
};

export default DetailStep;