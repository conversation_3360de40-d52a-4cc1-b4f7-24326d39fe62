import styles from "./signUpForm.module.scss";
import { useDispatch } from "@/redux/store";
import Grid from "@mui/material/Grid";
import { useState, useMemo, useCallback } from "react";
import Form from "@/components/_CommonElements/Form";
import {
    FieldContainer,
    TextInput,
    Checkbox,
} from "@/components/_CommonElements/Input";
import { TextButton } from "@/components/_CommonElements/Button";
import ModalTermsWindow from "@/components/ModalWindow/Terms";
import { ModalActions } from "@/redux/slices/uiSlice";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { getCookie, setCookie } from "cookies-next";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import ENDPOINT from "@/models/api/endpoint";
import { useSnackbar } from "notistack";
import API from "@/utils/API";
import { BACKEND, FRONTEND } from "@/constants/config";
import RegisterAPIResult from "@/models/api/result/user/register";
import UserInfoAPIResult from "@/models/api/result/user/info";
import LoadingButton from "@/components/_CommonElements/Button/LoadingButton";
import InputValidation from "@/utils/regex/InputValidation";
import { setUserVerified, setUserIdHash, setIsMembership } from "@/redux/slices/userInfoSlice";

const IsInvalidNameInput = (name?: string) => {
    if (name === undefined) {
        return false;
    }
    return name.length < FRONTEND.NameMinLength;
}
const IsInvalidEmailInput = (email: string | undefined) => {
    if (email === undefined) {
        return false;
    }
    return !InputValidation.validateEmail(email);
}
const IsInvalidPasswordInput = (password: string | undefined) => {
    if (password === undefined) {
        return false;
    }
    return !InputValidation.validatePassword(password) || password.length < FRONTEND.PasswordMinLength;
};
const useRegistration = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async (registerData: { [key in string]: unknown }) => {
        return await fetchAsync<RegisterAPIResult>(
            ENDPOINT.Register(),
            {
                method: "POST",
                data: API.ToFormData(registerData)
            }
        );
    };
    return requestAsync;
}
const useGetUserInfo = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async () => {
        return await fetchAsync<UserInfoAPIResult>(
            ENDPOINT.GetUserInfo(),
            {
                method: "GET"
            }
        );
    };
    return requestAsync;
};
interface SignUpFormProps {
    noFormHead?: boolean;
}
const SignUpForm = (props: SignUpFormProps) => {
    const { t: accountTranslation } = useTranslation(EnumTranslationJson.Account);
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { t: validationTranslation } = useTranslation(EnumTranslationJson.Validation);
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const { enqueueSnackbar } = useSnackbar();

    const [nickname, setNickname] = useState<string>();
    const [email, setEmail] = useState<string>();
    const [password, setPassword] = useState<string>();
    const [confirmPassword, setConfirmPassword] = useState<string>();
    const [promotion, setPromotion] = useState<boolean>(false);
    const [isReadAgreement, setIsReadAgreement] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [termsModalVisible, setTermsModalVisible] = useState(false);
    
    const dispatch = useDispatch();
    const registerAsync = useRegistration();
    const getUserInfoAsync = useGetUserInfo();

    const invalidNickname = useMemo(() => IsInvalidNameInput(nickname), [nickname]);
    const invalidEmail = useMemo(() => IsInvalidEmailInput(email), [email]);
    const invalidPassword = useMemo(() => IsInvalidPasswordInput(password), [password]);
    const invalidConfirmPassword = useMemo(() => password !== confirmPassword, [password, confirmPassword]);

    const disabledSubmit = useMemo(() => {
        if (nickname === undefined || email === undefined || password === undefined || confirmPassword === undefined) {
            return true;
        }
        if (invalidNickname || invalidEmail || invalidPassword || invalidConfirmPassword) {
            return true;
        }
        if (!isReadAgreement) {
            return true;
        }
        return false;
    }, [nickname, email, password, confirmPassword, invalidNickname, invalidEmail, invalidPassword, invalidConfirmPassword, isReadAgreement]);

    const onSubmit = useCallback(() => {
        void (async () => {
            if (disabledSubmit) {
                return;
            }
            setIsLoading(true);
            try {
                const referralCode = getCookie(EnumCookieKey.TRACED_REFERRAL_CODE)?.toString() ?? "";
                const res = await registerAsync({
                    password,
                    confirmPassword,
                    email,
                    nickname,
                    promotion,
                    referralCode,
                    recaptcha: "123"
                });

                const data = res.data!;
                setCookie(EnumCookieKey.USER_JWT, data.userJWT, {
                    maxAge: Math.floor((Number(data.tokenExpiryDateTime) - Date.now()) / 1000),
                    sameSite: "lax",
                });
                setCookie(EnumCookieKey.USER_NICKNAME, data.nickname, {
                    maxAge: Math.floor((Number(data.tokenExpiryDateTime) - Date.now()) / 1000),
                    sameSite: "lax",
                })
                setCookie(EnumCookieKey.USER_ID, data.userId, {
                    maxAge: Math.floor((Number(data.tokenExpiryDateTime) - Date.now()) / 1000),
                    sameSite: "lax",
                });
                setCookie(EnumCookieKey.USER_TOKEN_EXPIRYDATETIME, data.tokenExpiryDateTime, {
                    maxAge: Math.floor((Number(data.tokenExpiryDateTime) - Date.now()) / 1000),
                    sameSite: "lax",
                });

                // Fetch complete user info including userIdHash
                const userInfoRes = await getUserInfoAsync();
                const userInfo = userInfoRes.data!;
                dispatch(setUserVerified(userInfo.isVerified));
                dispatch(setUserIdHash(userInfo.userIdHash));
                if (userInfo.membershipSubscription?.state === "VERIFIED") dispatch(setIsMembership(true));

                dispatch(ModalActions.closeLoginModal());
                enqueueSnackbar(snackbarTranslation("messages.register.success"), { variant: "success"});
                dispatch(ModalActions.openAccountVerificationModal());
            }
            catch (error) {
                const errorMessage = API.GetErrorMessage(error);
                enqueueSnackbar(errorMessage, { variant: "error" });
            }
            setIsLoading(false);
        })();
    }, [nickname, email, password, confirmPassword, promotion, disabledSubmit, registerAsync, getUserInfoAsync, snackbarTranslation, enqueueSnackbar, dispatch]);

    const onNicknameChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setNickname(e.target.value);
    }, []);
    const onEmailChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setEmail(e.target.value);
    }, []);
    const onPasswordChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setPassword(e.target.value);
    }, []);
    const onConfirmPasswordChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setConfirmPassword(e.target.value);
    }, []);
    const onPromotionChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setPromotion(e.target.checked);
    }, []);
    const onIsReadAgreementChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setIsReadAgreement(e.target.checked);
    }, []);
    const showTermsModel = useCallback(() => {
        setTermsModalVisible(true);
    }, []);
    const hideTermsModel = useCallback(() => {
        setTermsModalVisible(false);
    }, []);
    return (
        <>
            <Form.Container size="small">
                <Form.Title noFormHead={props.noFormHead}>{accountTranslation("register")}</Form.Title>

                <Form.Body onEnter={onSubmit} rowSpacing={2} columnSpacing={1}>
                    <Grid container item xs={12}>
                        <TextInput
                            name="nickname"
                            label={accountTranslation("nickName")}
                            value={nickname}
                            error={invalidNickname}
                            helperText={invalidNickname ? validationTranslation("nickname.errorMessage.format") : ""}
                            onChange={onNicknameChanged}
                        />
                    </Grid>

                    <Grid container item>
                        <TextInput
                            name="email"
                            label={accountTranslation("email")}
                            inputType="email"
                            error={invalidEmail}
                            helperText={invalidEmail ? validationTranslation("email.errorMessage.format") : ""}
                            value={email}
                            onChange={onEmailChanged}
                        />
                    </Grid>

                    <FieldContainer>
                        <TextInput
                            name="password"
                            label={accountTranslation("password")}
                            type="password"
                            value={password}
                            error={invalidPassword}
                            helperText={invalidPassword ? validationTranslation("password.errorMessage.format") : ""}
                            onChange={onPasswordChanged}
                        />
                    </FieldContainer>

                    <FieldContainer>
                        <TextInput
                            name="password"
                            label={accountTranslation("comfirmPassword")}
                            type="password"
                            error={invalidConfirmPassword}
                            helperText={invalidConfirmPassword ? validationTranslation("confirmPassword.errorMessage.format") : ""}
                            value={confirmPassword}
                            onChange={onConfirmPasswordChanged}
                        />
                    </FieldContainer>
                    <FieldContainer>
                        <Checkbox
                            fullWidth
                            label={accountTranslation("promotion")}
                            checked={promotion}
                            onChange={onPromotionChanged}
                        />
                        <Checkbox
                            fullWidth
                            onChange={onIsReadAgreementChanged}>
                            {accountTranslation("agreement.text1")}
                            <span
                                className={styles.terms}
                                onClick={showTermsModel}
                            >
                                {accountTranslation("agreement.text2")}
                            </span>
                        </Checkbox>
                    </FieldContainer>

                    <FieldContainer horizontalCenter>
                        { isLoading ? <LoadingButton /> :
                            <TextButton
                                disabled={disabledSubmit}
                                label={modalTranslation("buttons.submit")}
                                onClick={onSubmit}
                            />
                        }
                    </FieldContainer>

                    <ModalTermsWindow
                        onClickClose={hideTermsModel}
                        disableBackdropClickClose
                        visible={termsModalVisible}
                    />
                </Form.Body>
            </Form.Container>
        </>
    );
};

export default SignUpForm;
