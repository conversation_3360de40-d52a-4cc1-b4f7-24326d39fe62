import RouteMap from "../config/RouteMap";

export interface NavigationSubItem {
  displayName: string;
  path?: string;
  action?(): void;
  active?: boolean;
  visible?: boolean | string;
}

export interface NavigationItem {
  displayName: string;
  path?: string;
  action?(): void;
  active?: boolean; 
  visible?: boolean | string;
  subMenu?: NavigationSubItem[];
}

export default class NavigationArray {
  private readonly items: NavigationItem[];

  constructor(currentPath?: string) {
    this.items = [
      {
        displayName: "home",
        path: RouteMap.Main,
        visible: true,
        active: currentPath === RouteMap.Main,
      },
      {
        displayName: "about",
        path: RouteMap.AboutUs,
        visible: true,
        active: currentPath === RouteMap.AboutUs,
      },
      {
        displayName: "guide",
        path: RouteMap.guide,
        visible: true,
        active: currentPath === RouteMap.guide,
      },
      {
        displayName: "faq",
        path: RouteMap.Faq,
        visible: true,
        active: currentPath === RouteMap.Faq,
      }
    ]
  }
  public getItems() {return this.items}
}