import styles from "@/components/_CommonElements/Button/button.module.scss";
import classNames from "classnames";
import MUIIconButton from '@mui/material/IconButton';

interface IconButtonProps {
  className?: string;
  buttonSize?: 'small' | 'medium' | 'large';
  color?: 'inherit' | 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  disabled?: boolean;
  edge?: 'end' | 'start';
  onClick?(params: any): void;
  padding?: string;
  inlineStyle?: { [key: string]: any };
  children?: React.ReactNode | string;
}

const IconButton = (props: IconButtonProps) => {
  const {
    className,
    buttonSize,
    color = "inherit",
    disabled,
    edge,
    onClick,
    padding,
    inlineStyle,
    children,
  } = props;

  return (<>
    <MUIIconButton
      className={classNames(styles.iconButton, className)}
      color={color}
      disabled={disabled}
      disableFocusRipple
      edge={edge}
      onClick={onClick}
      size={buttonSize}
      sx={{
        ...inlineStyle,
        padding: inlineStyle?.padding ?? padding,
      }}
    >
      {children}
    </MUIIconButton>
  </>);
};

export default IconButton;