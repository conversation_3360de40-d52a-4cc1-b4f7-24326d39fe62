import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import styles from "./index.module.scss";
import ScrollToTopFab from "./FabButton/ScrollToTop";
import ShoppingCartFab from "./FabButton/ShoppingCart";

const Fabs = () => {
    const { route } = useRouter();
    const [isKioskMode, setIsKioskMode] = useState(false);
    const [isCheckOutPage, setIsCheckingOutPage] = useState(false);
  
    useEffect(() => {
        if (route.includes('/kiosk')) {
          setIsKioskMode(true);
        } else if (route.includes('/checkout')) {
          setIsCheckingOutPage(true);
        } else {
            setIsKioskMode(false);
            setIsCheckingOutPage(false);
        }
    }, [route]);

    if (isKioskMode || isCheckOutPage) return null
    return (
        <div className={styles.fixedOverlay}>
            <div className={styles.fabsContainer}>
                <ScrollToTopFab />
                <ShoppingCartFab />
            </div>
        </div>
    );
};
export default Fabs;