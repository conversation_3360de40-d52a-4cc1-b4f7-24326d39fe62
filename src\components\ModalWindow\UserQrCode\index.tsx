import React from "react";
import Modal from "@/components/_CommonElements/Modal";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import Card from "@mui/material/Card";
import { Box, CardContent, CardMedia } from "@mui/material";

interface Props {
    visible: boolean;
    qrCodeToken: string;
    onCancel?: () => void;
}

const UserQrCode = (props: Props) => {
    const {
        visible,
        qrCodeToken,
        onCancel
    } = props;

    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    
    const qrcodeImage = "data:image/png;base64,"+qrCodeToken;

    return (
        <Modal
            hideConfirmButton={true}
            hideCancelButton={true}
            visible={visible}
            onCancel={onCancel ?? (() => {})}
            title={modalTranslation("modals.userQrcode.title")}
        >   
            <Box padding="1rem">
                <Card elevation={0}>
                    <CardContent>{modalTranslation("modals.userQrcode.content.reminder")}</CardContent>
                    <CardMedia component="img" image={qrcodeImage} />
                </Card>
            </Box>
        </Modal>
    );
};

export default UserQrCode;