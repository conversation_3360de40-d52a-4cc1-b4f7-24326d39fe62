import { useEffect, useMemo } from 'react';

import type { GetServerSideProps, NextPage } from 'next';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { BACKEND } from '@/constants/config';
import RouteMap from '@/constants/config/RouteMap';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import { OrderType } from '@/constants/enum/OrderType';
import ENDPOINT from '@/models/api/endpoint';
import OrderStatusAPIResult from '@/models/api/result/order/status';
import UtilityHooks from '@/hooks/Utility';
import i18n from '@/utils/i18n';

import { LoadingCircle } from '@/components/_CommonElements/LoadingBar';

import styles from "./purchasedResult.module.scss";

const useOrderStatus = (id: string) => {
    return BACKEND.Gateway.useQuery<OrderStatusAPIResult>({
        url: `${ENDPOINT.GetOrderById(id)}`,
        params: {
          queryKey: `order-${id}`
        },
        refetchInterval: 5000,
        cacheTime: 0,
        staleTime: 0
    });
}
const ReceiptPage: NextPage = () => {
    const { query, push: goTo } = useRouter();
    const { order_id } = query;
    const { counter, setCounter } = UtilityHooks.useCountDown(-1);
    const { data } = useOrderStatus(order_id as string);
    const { t: receiptTranslation } = useTranslation(EnumTranslationJson.Receipt);
    const internalPaymentOrderState = useMemo(() => data?.data?.paymentInfo.internalPaymentOrderState, [data?.data?.paymentInfo.internalPaymentOrderState]);
    const isPending = useMemo(() => !data || !data.data || [OrderType.PENDING].includes(internalPaymentOrderState as OrderType), [data, internalPaymentOrderState]);
    const isSuccess = useMemo(() => [OrderType.PAID, OrderType.PROCESSING, OrderType.HANDLING, OrderType.COMPLETED].includes(internalPaymentOrderState as OrderType), [internalPaymentOrderState]);
    const isFailed = useMemo(() => [OrderType.FAILED].includes(internalPaymentOrderState as OrderType), [internalPaymentOrderState]);
    console.warn(internalPaymentOrderState);
    const isUnknownState = useMemo(() => !isPending && !isSuccess && !isFailed, [isPending, isSuccess, isFailed]);
    useEffect(() => {
        if (isSuccess) {
            setCounter(3);
        }
    }, [isSuccess, setCounter]);
    useEffect(() => {
        if (counter === 0) {
            void goTo(RouteMap.ticketOrders);
        }
    }, [counter, goTo]);

    const successMessage = useMemo(() => receiptTranslation("success", { countdown: counter }), [counter, receiptTranslation]);
    const icon = useMemo(() => {
        if (isPending) {
            return <LoadingCircle />;
        }
        if (isSuccess) {
            return <CheckCircleIcon sx={{ fontSize: 70 }} color="success" />;
        }
        if (isFailed) {
            return <HighlightOffIcon sx={{ fontSize: 70 }} color="error" />;
        }
        if (isUnknownState) {
            return <HelpOutlineIcon sx={{ fontSize: 70 }} color="warning" />;
        }
        return null;
    }, [isFailed, isPending, isSuccess, isUnknownState]);
    const message = useMemo(() => {
        if (isPending) {
            return receiptTranslation("loading");
        }
        if (isSuccess) {
            return successMessage;
        }
        if (isFailed) {
            return receiptTranslation("failed");
        }
        if (isUnknownState)
            return receiptTranslation("unknown");
        return null;
    }, [isPending, isSuccess, isFailed, isUnknownState, receiptTranslation, successMessage]);

    return (
        <div className={styles.container}>
            { icon }
            <h3>#{ order_id }</h3>
            { message && <div className={styles.message}>{ message }</div> }
        </div>
    );
};
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [EnumTranslationJson.Receipt],
    context
});

export default ReceiptPage;