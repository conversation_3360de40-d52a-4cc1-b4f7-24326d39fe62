@use 'sass:map';
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/fluid.module.scss" as fluid;
@use "@/styles/utils/viewport.module.scss" as viewport;

.label {
    color: map.get(theme.$color, "text");
    border-left: 6px solid map.get(theme.$color, "secondary");
    padding: 0 0 0 10px;

    font: {
        size: 24px;
        weight: bold;
    }
}

.chipLabelContainer {
    .chipLabel {
        margin: 0 auto;
        font-size: 1.2rem;
        font-weight: 900;
        letter-spacing: 3px;
        background-color: map.get(theme.$color, "primary");
        color: map.get(theme.$color, "background");
        padding: 16px 38px;
        width: fit-content;
        border-radius: 50px;

        @include viewport.within("mobile") {
            font-size: 1rem;
            text-align: center;
            padding: 10px 20px;
        }
    }
}

.stepBox {
    color: map.get(theme.$color, "text");
    text-align: center;

    .stepLabel {
        margin-top: 20px;
        padding: 0 6px;

        font: {
            size: 1.2rem;
            weight: bold;
        }
    }
}

.stepNumberContainer {
    display: flex;
    justify-content: center;
    align-items: center;
}

.stepNumber {
    cursor: context-menu;
    background-color: map.get(theme.$color, "secondary");
    border-radius: 50%;
    width: 50px;
    height: 50px;
    padding: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 3px solid map.get(theme.$color, "border");
    position: relative;
    color: white;

    font: {
        size: 1.5rem;
        weight: bold;
    }
}

.stepIconBox {
    width: 90%;
    height: 200px;
    border: 3px solid map.get(theme.$color, "border");
    border-radius: map.get(theme.$border, "radius");
    margin: -25px auto 0 auto;
    transition: 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;

    span {
        font-size: 3rem;
    }
}

.helpText {
    color: #716e6e;
    margin: 0px 8px;
}
.detailStep {
    padding: 15px;
    display: flex;

    @include viewport.within("tablet") {
        flex-direction: column;
        padding: 0;
        margin: 0;
    }
    .imageContainer {
        user-select: none;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
            width: 100%;
            height: auto;
        }
    }
    div {
        flex: 1;
        padding: 16px;

        @include viewport.within("tablet") {
            padding: 8px 0;
        }
    }

    .titleRow {
        display: flex;
        padding: 0 0 8px 0;

        font: {
            size: 1.25rem;
        }
        align-items: center;
        font-weight:bold;
    }
    .numbering {
        padding-right: 16px;
    }

    .title {
        border-left: 3px solid #{map.get(theme.$color, "secondary")};
        padding-left: 16px;
        flex: 1
    }
}
.fieldSet {
    margin: 10px 0;
    $letter-spacing: 4px;
    padding: 10px;
    &.fullWidth {
        width: 100%;
    }
    & .legend {
        @include fluid.font-size(8px, 18px, viewport.$mobile-breakpoint, viewport.$tablet-breakpoint);
        position: relative;
        padding: 6px 25px 4px 25px;
        font-weight: 700;
        user-select: none;

        & span {
            letter-spacing: $letter-spacing;
            display: inline-block;
            transform: translate($letter-spacing * 0.5, 1px);
        }
        &:before {
            content: "";
            position: absolute;
            top: 0;
            left: 10px;
            right: 10px;
            bottom: 0px;

            border: {
                color: map.get(theme.$color, "primary");
                width: map.get(theme.$border, "width");
                style: solid;
                radius: map.get(theme.$border, "radius");
            }
        }
    }
}