import { NextPageContext } from "next";
import router from 'next/router';

const ErrorRedirection = () => null;

ErrorRedirection.getInitialProps = (context: NextPageContext) => {
    const { res, err } = context;

    const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
    let redirectPath = `/error/${statusCode}?route=`;
    if (res) {
        // SSR
        redirectPath += context.asPath;
        res.writeHead(302, {
            Location: redirectPath
        });
        res.end();
        return;
    }
    else {
        // CSR
        redirectPath += router.asPath;
        void router.push(redirectPath);
    }
};

export default ErrorRedirection;