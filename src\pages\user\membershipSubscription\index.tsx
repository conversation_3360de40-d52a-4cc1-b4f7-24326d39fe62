import PageContent from "@/components/_PageComponents/PageContent";
import MembershipSubscriptionSection from "@/components/User/MembershipSubscriptionSection";
import { ProfileSectionWrapper } from "@/components/User/ProfileSections";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import ENDPOINT from "@/models/api/endpoint";
import MembershipSubscription from "@/models/api/models/MembershipSubscription";
import UserMembershipSubscriptionAPIResult from "@/models/api/result/user/membershipSubscription";
import API from "@/utils/API";
import i18n from "@/utils/i18n";
import serverSideAuth from "@/utils/serverSideAuth";
import { GenericAPI } from "@stoneleigh/api-lib";

import type { GetServerSideProps } from "next";
import { useTranslation } from "next-i18next";
import { useMemo } from "react";

interface Props {
    fallback: {
        UserMembership?: MembershipSubscription,
    }
}
const MembershipSubscriptionPage = (props:Props) => {
    const { fallback } = props;
    const userMemberhsip = fallback.UserMembership ?? {} as MembershipSubscription;
    const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
    
    const title = useMemo(() => seoTranslation("page.membershipSubscription.title"), [seoTranslation]);
    return (
        <PageContent
            title={title}
            content={
                <ProfileSectionWrapper>
                    <MembershipSubscriptionSection userMemberhsip={userMemberhsip} />
                </ProfileSectionWrapper>
            }
        />
    );
};

export const getServerSideProps: GetServerSideProps = serverSideAuth(
    {
        permission: "userOnly",
    },
    async (context) =>
        i18n.GetServerSidePropsAsync({
            additionalFiles: [
                EnumTranslationJson.Account,
                EnumTranslationJson.Profile,
                EnumTranslationJson.Redeem,
            ],
            context,
            getServerSideProps: async context => {
                const { params, locale, res } = context;
                const userJWT = API.GetServerSideCookies(context)[EnumCookieKey.USER_JWT];
                var UserMembership = {};

                if (userJWT) {
                    try {
                        const UserMembershipSubscription = await GenericAPI.requestAsync<UserMembershipSubscriptionAPIResult>(
                            ENDPOINT.GetUserMembershipSubscription(),
                            {
                                method: "GET",
                                headers: {
                                    [EnumRequestHeader.AUTHORIZATION as string]: userJWT,
                                    [EnumRequestHeader.LANGUAGE]: locale || "en-US"
                                }
                            }
                        )
    
                        if (UserMembershipSubscription.result) {
                            UserMembership = UserMembershipSubscription.data!;
                        }
                    } catch(error) {
                        console.log(error);
                    }
                }
                
                return {
                    props: {
                        fallback: {
                            UserMembership
                        }
                    }
                }
            }
        })
);

export default MembershipSubscriptionPage;
