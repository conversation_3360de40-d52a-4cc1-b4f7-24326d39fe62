import type { GetServerSideProps, NextPage } from 'next';
import { useRouter } from 'next/router';
import React, { useCallback, useMemo } from "react";
import Tabs from "@/components/_CommonElements/Tabs";
import SignUpForm from '@/components/User/SignUp/Form';
import i18n from '@/utils/i18n';
import serverSideAuth from '@/utils/serverSideAuth';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import { useTranslation } from 'next-i18next';
import PageContent from '@/components/_PageComponents/PageContent';

const SignUpPage: NextPage = () => {
  const router = useRouter();
  const { t: accountTranslation } = useTranslation(EnumTranslationJson.Account);

  const goToLoginPage = useCallback(() => { void router.push("/login"); }, [router]);
  
  const tabs = useMemo(() =>[
    {name: accountTranslation("login"), action: goToLoginPage, active: false},
    {name: accountTranslation("register"), active: true},
  ], [accountTranslation, goToLoginPage]);

  const title = useMemo(() => accountTranslation("register"), [accountTranslation]);
  return (
    <PageContent
        title={title}
        content={
            <>
                <Tabs tabs={tabs} />
                <SignUpForm/>
            </>
        }
    />
  );
};

export const getServerSideProps: GetServerSideProps = serverSideAuth(
  {
      permission: "guestOnly",
  },
  async (context) =>
      i18n.GetServerSidePropsAsync({
          additionalFiles: [EnumTranslationJson.Account, EnumTranslationJson.Profile],
          context,
      })
);

export default SignUpPage;