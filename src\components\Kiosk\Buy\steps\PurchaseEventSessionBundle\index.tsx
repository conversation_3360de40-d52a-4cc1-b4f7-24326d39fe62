import Label from "@/components/_CommonElements/Label";
import Section from "@/components/_CommonElements/Section";
import styles from "@/components/Kiosk/InfoDisplay/event.module.scss";
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import { ShoppingCartSummarizedItem } from "@/models/api/result/user/shoppingCart";
import DataSourceProps from "@/models/props/DataSourceProps";
import { useSelector } from "@/redux/store";
import i18n from "@/utils/i18n";
import { Box, Divider, Stack, Typography } from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import dayjs, { Dayjs } from "dayjs";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useCallback, useMemo, useState } from "react";

import ShoppingCartItemDisplay from "./ShoppingCartItemDisplay";

interface StepPurchaseEventSessionBundleProps {
    dataSource?: DataSourceProps[];
    flattenedDataSource?: ShoppingCartSummarizedItem[];
    selectedDate: Dayjs | null | undefined;
}

const StepPurchaseEventSessionBundle = (props: StepPurchaseEventSessionBundleProps) => {
    const { flattenedDataSource, selectedDate } = props;
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);
    const { t: purchaseTranslation } = useTranslation(EnumTranslationJson.Purchase);
    const { t: kioskTranslation } = useTranslation(EnumTranslationJson.Kiosk);
    const router = useRouter();
    const cartItems = useSelector(state => state.cart.items);
    
    const currency = flattenedDataSource?.at(0)?.currency ?? "HKD";
    const formattedSelectedDate = selectedDate?.format("YYYY-MM-DD") ?? "";
    const sessionStartDateTime_LocalTime = dayjs.utc(flattenedDataSource?.at(0)?.sessionStartDateTime).tz(flattenedDataSource?.at(0)?.eventTimeZone);
    const sessionEndDateTime_LocalTime = dayjs.utc(flattenedDataSource?.at(0)?.sessionEndDateTime).tz(flattenedDataSource?.at(0)?.eventTimeZone);
    const totalPrice = useMemo(() => cartItems.reduce((sum, item) => {
        sum += item.price * item.quantity;
        return sum;
    }, 0.0), [cartItems]);

    const kioskSessionCartItems = useMemo(() => {
        return flattenedDataSource?.map(data => ({
            ...data,
            quantity: 0
        })) ?? [];
    }, [flattenedDataSource]);
    const isLoading = useMemo(() => !flattenedDataSource || !kioskSessionCartItems, [flattenedDataSource, kioskSessionCartItems]);

    return (
        <Section
            containerSize="wide"
            className={{ root: styles.purchaseSection }}
            content={
                <>
                    <Typography variant="h5">{formattedSelectedDate} {kioskTranslation('purchaseProcess.ticketsOnTheDay')}</Typography>
                    <Stack direction="column" spacing={2} className={styles.purchaseItemsPanel}>
                        <Stack direction="row">
                            <Label title={eventTranslation("detail.packageLabel")} className={styles.ticketType} />
                            <Label title={eventTranslation("detail.qty")} />
                        </Stack>
                        {isLoading ? <CircularProgress /> : kioskSessionCartItems?.map(data => 
                            <ShoppingCartItemDisplay
                                key={data.eventSessionBundleId}
                                item={data}
                            />
                        )}
                    </Stack>
                    <Divider orientation="horizontal" flexItem />
                    <Box display='flex' justifyContent='flex-end'>
                        <Typography variant="h2" component="h2">
                            {isLoading ? <CircularProgress /> : purchaseTranslation('modal.total')} {i18n.GetCurrency(currency, totalPrice, router.locale)}
                        </Typography>
                    </Box>
                </>
            }
        />
    );
};

export default StepPurchaseEventSessionBundle;