import styles from "@/components/_CommonElements/Button/button.module.scss";
import { FunctionComponent, RefObject } from "react";

import classNames from "classnames";
import Button from '@mui/material/Button';
import type { ButtonProps } from "@mui/material/Button";

interface TextButtonProps extends Omit<ButtonProps, 'size'> {
  id?: string;
  children?: React.ReactNode | string;
  className?: string;
  color?: 'primary' | 'secondary';
  disabled?: boolean;
  endIcon?: React.ReactNode | string;
  fullWidth?: boolean;
  label?: string | React.ReactNode;
  onClick?: ((params: any) => void) | undefined;
  forwardedRef?: ((instance: HTMLButtonElement | null) => void) | RefObject<HTMLButtonElement> | null | undefined;
  size?: 'small' | 'medium' | 'large' | 'extra';
  startIcon?: React.ReactNode | string;
  letterSpacing?: boolean;
  variant?: "text" | "outlined" | "contained";
}

const TextButton: FunctionComponent<TextButtonProps> = (props) => {
  const {
    id,
    children,
    className,
    color = "primary",
    disabled,
    endIcon,
    fullWidth,
    label,
    onClick,
    forwardedRef,
    size = "medium",
    startIcon,
    letterSpacing = true,
    variant = "outlined",
  } = props;

  return (<>
    <Button
      id={id}
      aria-label={id}
      className={classNames(
        className, 
        styles.container,
        size && styles[size],
        styles[variant],
        styles[`color-${color}`],
      )}
      ref={forwardedRef}
      disabled={disabled}
      endIcon={endIcon}
      fullWidth={fullWidth}
      startIcon={startIcon}
      onClick={onClick}
      style={{textTransform: 'none'}}
    >      
      <span
        className={letterSpacing ? styles.letterSpacing : ""}
      >
        {children || label}
      </span>
    </Button>      
  </>);
};

export default TextButton;