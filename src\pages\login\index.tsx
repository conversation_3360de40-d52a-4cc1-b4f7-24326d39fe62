import type { GetServerSideProps, NextPage } from "next";
import { useRouter } from "next/router";
import React, { useCallback, useMemo } from "react";
import Tabs from "@/components/_CommonElements/Tabs";
import LoginForm from "@/components/User/Login/Form";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import i18n from "@/utils/i18n";
import serverSideAuth from "@/utils/serverSideAuth";
import PageContent from "@/components/_PageComponents/PageContent";

const LoginPage: NextPage = () => {
  const { t: accountTranslation } = useTranslation(EnumTranslationJson.Account);
  const router = useRouter();

  const goToRegisterPage = useCallback(() => { void router.push("/signUp"); }, [router]);

  const tabs = useMemo(() => [
    { name: accountTranslation("login"), active: true },
    { name: accountTranslation("register"), action: goToRegisterPage, active: false }
  ], [accountTranslation, goToRegisterPage]);

  const title = useMemo(() => accountTranslation("login"), [accountTranslation]);
  return (
    <PageContent
        title={title}
        content={
            <>
                <Tabs tabs={tabs} />
                <LoginForm />
            </>
        }
    />
  );
};

export const getServerSideProps: GetServerSideProps = serverSideAuth(
    {
        permission: "guestOnly",
    },
    async (context) => i18n.GetServerSidePropsAsync({ context})
);

export default LoginPage;
