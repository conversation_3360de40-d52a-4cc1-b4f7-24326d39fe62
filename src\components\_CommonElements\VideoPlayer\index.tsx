import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Paper, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import ReactPlayer from "react-player";
import { useIntersection } from 'react-use';

import styles from "@/components/_CommonElements/VideoPlayer/videoPlayer.module.scss";

interface VideoPlayerProps {
    src: string;
    source?: string;
    thumbnail?: string;
}

const VideoPlayer = React.memo((props: VideoPlayerProps) => {
    const { src, source, thumbnail } = props;
    const [ playing, setPlaying ] = useState(false);
    const intersectionRef = React.useRef(null);
    const intersection = useIntersection(intersectionRef, {
      root: null,
      rootMargin: '0px',
      threshold: 1
    });
    
    const theme = useTheme();
    const greaterThanMid = useMediaQuery(theme.breakpoints.up("md"));
    const smallToMid = useMediaQuery(theme.breakpoints.between("sm", "md"));
    const lessThanSmall = useMediaQuery(theme.breakpoints.down("sm"));

    const bannerVideoThumbnailUrl = useMemo(() => {
        let videoId;
        switch (source?.toLowerCase()) {
            case 'youtube':
                videoId = src.substring(9).split("/").at(2) || src.split("be/").at(1) || src.split("v=").at(1);
                if (greaterThanMid) return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
                if (smallToMid) return `https://img.youtube.com/vi/${videoId}/sddefault.jpg`;
                if (lessThanSmall) return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
                break;
            default:
                return thumbnail;
        }
    }, [greaterThanMid, lessThanSmall, smallToMid, source, src, thumbnail]);

    const onPlay = useCallback(() => {
        setPlaying(true);
    }, []);
    const onPause = useCallback(() => {
        setPlaying(false);
    }, []);

    const isInViewport = useMemo(() => intersection && intersection.intersectionRatio > 0.0, [intersection]);
    useEffect(() => {
        if (!isInViewport && playing) {
            setPlaying(false);
        }
    }, [isInViewport, playing]);

    return (
        <Paper 
            ref={intersectionRef}
            elevation={0} 
            className={styles.videoPlayerContainer}
            sx={{
                height: {
                    xs: "360px",
                    lg: "480px",
                    xl: "500px"
                }
            }}
        >
            <div className={styles.videoPlayerWrapper}>
                <ReactPlayer 
                    key={`v-${src}`}
                    url={src} 
                    config={{
                        youtube: {
                            playerVars: {
                                autoplay: 1,
                                rel: 0,
                                showinfo: 0
                            }
                        }
                    }}
                    playsinline
                    controls
                    width={"100%"}
                    height={"100%"}
                    light={bannerVideoThumbnailUrl}
                    onPlay={onPlay}
                    onPause={onPause}
                    playing={playing}
                />
            </div>
        </Paper>
    );
});
VideoPlayer.displayName = "VideoPlayer";

export default VideoPlayer;