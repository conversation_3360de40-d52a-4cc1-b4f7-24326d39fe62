import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import EventSession from "@/models/api/models/EventSession";

export interface PurchaseTicketSliceState {
    eventId:string,
    sessionId:string,
    ticketTypeId:string,
    amount:number,
    hKDPricePerOne:number,
    agreeRules:boolean,
    sessionList:EventSession[]
}

const initialState: PurchaseTicketSliceState = {
    eventId:"",
    sessionId:"",
    ticketTypeId:"",
    amount:1,
    hKDPricePerOne:0,
    agreeRules:false,
    sessionList:[]
};

export const purchaseTicketSlice = createSlice({
    name: "purchaseTicket",
    initialState,
    reducers: {
        setEventId: (state, action: PayloadAction<string>) => {
            state.eventId = action.payload;
        },
        setSessionId: (state, action: PayloadAction<string>) => {
            state.sessionId = action.payload;
        },
        setTicketTypeId: (state, action: PayloadAction<string>) => {
            state.ticketTypeId = action.payload;            
        },
        setAmount: (state, action: PayloadAction<number>) => {  
            state.amount = action.payload; 
        },
        setHKDPricePerOne: (state, action: PayloadAction<number>) => {  
            state.hKDPricePerOne = action.payload; 
        },
        setAgreeRules: (state, action: PayloadAction<boolean>) => {  
            state.agreeRules = action.payload; 
        },
        setSessionList: (state, action: PayloadAction<EventSession[]>) => {  
            state.sessionList = action.payload; 
        },
    }
});

export const { setEventId,  setSessionId, setTicketTypeId, setAmount, setAgreeRules, setHKDPricePerOne,setSessionList } = purchaseTicketSlice.actions;

export default purchaseTicketSlice.reducer;