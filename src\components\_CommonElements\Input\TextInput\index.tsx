import styles from "@/components/_CommonElements/Input/input.module.scss";
import classNames from "classnames";

import TextField, { TextFieldProps } from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import React from "react";

type TextInputProps = TextFieldProps &{
  solidBackground?: boolean;
  className?: string;
  color?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  defaultValue?: any;
  disabled?: boolean;
  readonly?: boolean;
  fullWidth?: boolean;
  error?: boolean;
  helperText?: string;
  id?: string;
  inputType?: string;
  label?: string;
  rows?: number;
  minRows?: number;
  maxRows?: number;
  multiline?: boolean;
  name?: string;
  onChange?(params: any): void;  
  onClick?(params: any): void;
  prefix?: string;
  size?: 'medium' | 'small';
  tabIndex?: number;
  value?: any;
  ref?: any;
  
};

const TextInput = React.memo((props: TextInputProps) => {
  const {
    solidBackground,
    className,
    color,
    defaultValue,
    disabled,
    readonly = false,
    fullWidth = true,
    error,
    helperText,
    id,
    inputType,
    type,
    label,
    rows,
    minRows,
    maxRows,
    multiline,
    name,
    onChange,
    onClick,
    prefix,
    size,
    tabIndex,
    value,
    ref,
    ...otherProps
  } = props;

  return (<>
    <TextField
      className={classNames(
        styles.textField,
        solidBackground && styles.solidBackground,
        className
      )}
      color={color || "primary"}
      defaultValue={defaultValue}
      disabled={disabled}
      error={error}
      fullWidth={fullWidth}
      helperText={helperText}
      id={id}
      InputProps={{ 
        className: classNames(styles.textInput),
        startAdornment: prefix && <InputAdornment position="start">{prefix}</InputAdornment>,
        readOnly: readonly
      }}
      inputProps={{ 
        className: classNames(styles.native),
        type: type,
        tabIndex: tabIndex,
      }}
      label={label}
      maxRows={maxRows}
      minRows={minRows}
      multiline={multiline}
      name={name}
      onChange={onChange}
      onClick={onClick}
      rows={rows}
      size={size || "medium"}
      value={value}
      inputRef={ref}
      variant="outlined"
    />
  </>);
});
TextInput.displayName = "TextInput";
export default TextInput;