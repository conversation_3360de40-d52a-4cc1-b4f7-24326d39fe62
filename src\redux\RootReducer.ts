import { combineReducers } from "redux";
import { persistReducer, PersistConfig } from "redux-persist";
import createWebStorage from "redux-persist/lib/storage/createWebStorage";
// slices
import userInfoReducer, { UserInfoSliceState } from "@/redux/slices/userInfoSlice";
import identitySliceReducer, { IdentitySliceState } from "@/redux/slices/identitySlice"; 
import forgotPasswordReducer, { ForgotPasswordSliceState } from "@/redux/slices/forgotPasswordSlice";
import purchaseTicketReducer, { PurchaseTicketSliceState } from "@/redux/slices/purchaseTicketSlice";
import changePasswordReducer, { ChangePasswordSliceState } from "@/redux/slices/changePasswordSlice";
import uiReducer from "@/redux/slices/uiSlice";
import cartReducer from "@/redux/slices/cartSlice";
import kioskReducer from "@/redux/slices/kioskSlice";
import oneTimePaymentReducer from "@/redux/slices/oneTimePaymentSlice";

// ----------------------------------------------------------------

// see https://github.com/vercel/next.js/discussions/15687#discussioncomment-45319
const createNoopStorage = () => {
  return {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getItem(_key: string) {
      return Promise.resolve(null);
    },
    setItem(_key: string, value: string) {
      return Promise.resolve(value);
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    removeItem(_key: string) {
      return Promise.resolve();
    },
  };
};


const storage =
  typeof window !== "undefined"
    ? createWebStorage("local")
    : createNoopStorage();

// ----------------------------------------------------------------
const userPersistConfig: PersistConfig<UserInfoSliceState> = {
  key: "user",
  storage,
  keyPrefix: "Ix-",
};

const purchasePersistConfig: PersistConfig<PurchaseTicketSliceState> = {
  key: "purchase",
  storage,
  keyPrefix: "Ix-",
};
const identityPersistConfig: PersistConfig<IdentitySliceState> = {
  key: "identity",
  storage,
  keyPrefix: "Ix-",
};
const forgotPasswordPersistConfig: PersistConfig<ForgotPasswordSliceState> = {
  key: "forgotPassword",
  storage,
  keyPrefix: "Ix-",
};
const changePasswordPersistConfig: PersistConfig<ChangePasswordSliceState> = {
  key: "changePassword",
  storage,
  keyPrefix: "Ix-",
};

const reducers = {
    user: persistReducer(userPersistConfig, userInfoReducer),
    identity: persistReducer(identityPersistConfig, identitySliceReducer),
    purchase: persistReducer(purchasePersistConfig, purchaseTicketReducer),
    forgotPassword: persistReducer(forgotPasswordPersistConfig, forgotPasswordReducer),
    changePassword: persistReducer(changePasswordPersistConfig, changePasswordReducer),
    ui: uiReducer,
    cart: cartReducer,
    kiosk: kioskReducer,
    oneTimePaymentOrder: oneTimePaymentReducer,
};
const RootReducer = combineReducers(reducers);
export default RootReducer;