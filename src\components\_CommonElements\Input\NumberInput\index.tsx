import styles from "@/components/_CommonElements/Input/input.module.scss";
import classNames from "classnames";

import { TextInput } from "@/components/_CommonElements/Input";

interface NumberInputProps {
  label?: string;
  value?: number;
  onDecrementClick?: () => void;
  onIncrementClick?: () => void;
  onTextInputChange: (e: any) => void;
  size?: "small" | "medium";
}

const NumberInput = (props: NumberInputProps) => {
  const {
    label,
    value,
    onDecrementClick,
    onIncrementClick,
    onTextInputChange,
    size = "small",
  } = props;

  return (<>
    <div className={classNames(
      styles.numberInputContainer,
      size && styles[size],
    )}>

      <span
        className={styles.decrement}
        onClick={onDecrementClick}
      >-</span>

      <span
        className={styles.numberValue}
      >
        <TextInput
          label={label}
          inputType="number"
          className={styles.numberTextInput}
          size={size}
          value={value}
          onChange={onTextInputChange}
        />
      </span>

      <span
        className={styles.increment}
        onClick={onIncrementClick}
      >+</span>
    </div>
  </>);
};

export default NumberInput;