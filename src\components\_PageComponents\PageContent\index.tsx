import { ReactNode } from "react";
import PageMeta from "@/components/_PageComponents/Meta/Page";
import Section from "@/components/_CommonElements/Section";

interface PageContentProps {
    title: string;
    content: ReactNode
    robotIndex?: "noindex" | "index";
    hiddenTitle?: boolean;
    containerSize?: "thin" | "wide" | "tight" | undefined;
}
const PageContent = ({title, content, robotIndex, hiddenTitle = false, containerSize = "thin"}: PageContentProps) => {
    return (
        <>
            <PageMeta title={title} robotIndex={robotIndex} />
            <Section
                containerSize={containerSize}
                {...(!hiddenTitle ? {label: title, labelSize: "xlarge"} : {})}
                content={content}
            />
        </>
    );
};
export default PageContent;