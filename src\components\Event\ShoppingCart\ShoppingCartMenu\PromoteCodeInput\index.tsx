import PromoCodeDialog from "@/components/ModalWindow/PromoCode";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";

import React from "react";
import { useCallback, useState } from "react";
import { useTranslation } from "next-i18next";
import { SxProps, Theme } from "@mui/system";
import { Button, Stack } from "@mui/material";
import { enqueueSnackbar } from "notistack";
import styles from "./promoteCodeInput.module.scss";

interface Props {
    defaultValue?: string;
    onChange?: (value: string) => void;
    sx?:  { [key: string]: any };
    disabled?: boolean;
}
const PromoteCodeInput = React.memo((props: Props) => {
    const { onChange, defaultValue, sx, disabled = false } = props;

    const [openPromoCodeModal, setOpenPromoCodeModal] = useState<boolean>(false);

    const { t: purchaseTranslation } = useTranslation(EnumTranslationJson.Purchase);
    const { t: snackBarTranslation } = useTranslation(EnumTranslationJson.SnackBar);

    const onClickOpenPromoCodeModal = useCallback(() => setOpenPromoCodeModal(!openPromoCodeModal), [openPromoCodeModal]);
    const onClosePromoCodeModal = useCallback(() => setOpenPromoCodeModal(false), []);
    const onApplySuccess = useCallback(() => enqueueSnackbar(snackBarTranslation("messages.discountCode.apply.success"), { variant: "success" }) && setOpenPromoCodeModal(false), [snackBarTranslation]);
    const onApplyError = useCallback((errorMessage: string) => enqueueSnackbar(errorMessage, { variant: "error" }), []);

    return (
        <Stack direction={"row"}>
            <Button variant="outlined" onClick={onClickOpenPromoCodeModal} disabled={disabled}>{purchaseTranslation("modal.extra.usePromoCode")}</Button>
            <PromoCodeDialog
                visible={openPromoCodeModal}
                onChange={onChange} 
                defaultValue={defaultValue}
                sx={sx}
                onClose={onClosePromoCodeModal}
                onSuccess={onApplySuccess}
                onError={onApplyError}
            />
        </Stack>
    );
});
PromoteCodeInput.displayName = "PromoteCodeInput";
export default PromoteCodeInput;