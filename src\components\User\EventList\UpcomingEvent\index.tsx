import styles from "@/components/User/EventList/eventList.module.scss";
import classNames from "classnames";
import Grid from '@mui/material/Grid';
import TicketPermission from "@/constants/enum/TicketPermission";
import dayjs from "dayjs";
import { useMemo } from "react";
import TicketWithEventDetail from "@/models/api/models/TicketWithEventDetail";

export interface UpcomingEventProps {
    event: TicketWithEventDetail;
}

const UpcomingEvent = (props: UpcomingEventProps) => {
    const {
        event,
    } = props;

    const sessionStartDate_LocalTime = useMemo(() => dayjs.utc(event.sessionStartDateTime), [event.sessionStartDateTime]);
    const redeemed = useMemo(() => {
        return event.usedPermissions.find(permission => permission === TicketPermission.TICKET_REDEEM) !== null;
    }, [event]);

    return (
        <div className={classNames(
            styles.itemContainer,
            styles.upcomingEvent
        )}>
            <Grid container flexWrap="nowrap">
                <div className={styles.dateTime}>
                    <p>{sessionStartDate_LocalTime.format('YYYY-MM-DD (Z)')}</p>
                    <p>{sessionStartDate_LocalTime.format('HH:mm')}</p>
                </div>

                <div className={styles.name}>
                    <p>{event.eventName}</p>
                </div>

                <div className={classNames(
                    styles.redeemStatus,
                    redeemed && styles.redeemed,
                )}>
                    <p>{redeemed ? "已綁定" : "未綁定"}</p>
                </div>
            </Grid>
        </div>
    );
};

export default UpcomingEvent;