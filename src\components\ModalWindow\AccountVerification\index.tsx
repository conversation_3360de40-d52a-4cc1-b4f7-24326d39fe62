import { useCallback, useRef, useState } from "react";
import Modal from "@/components/_CommonElements/Modal";
import AccountVerificationForm, { AccountVerificationFormRef } from '@/components/User/AccountVerification/Form';
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import LoadingButton from "@/components/_CommonElements/Button/LoadingButton";
import { dispatch, useSelector } from "@/redux/store";
import { ModalActions } from "@/redux/slices/uiSlice";

const ModalAccountVerificationWindow = () => {
    const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
    const [isLoading, setIsLoading] = useState(false);
    const formRef = useRef<AccountVerificationFormRef>(null);

    const hideModal = () => {
        dispatch(ModalActions.closeAccountVerificationModal());
    }
    const onFormSubmit = useCallback(() => {
        if (!formRef.current) {
            return;
        }
        setIsLoading(true);
        formRef.current.onSubmit();
        setIsLoading(false);
    }, []);
    const visible: boolean = useSelector((state) => state.ui.modal.accountVerification.visible);
    return (<>
        <Modal
            visible={visible}
            title={modalTranslation("modals.emailVerification.title")}
            onConfirm={onFormSubmit}
            onCancel={hideModal}
            hideConfirmButton={isLoading}
            hideCancelButton
        >
            <AccountVerificationForm
                ref={formRef}
                onClose={hideModal}
            />
            {isLoading && <LoadingButton />}
        </Modal>
    </>);
};

export default ModalAccountVerificationWindow;