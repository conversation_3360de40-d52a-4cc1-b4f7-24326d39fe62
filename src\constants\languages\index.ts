import { EnumLocale } from "@/constants/enum/Locale";
export interface ILanguageItem {
  displayName: string;
  locale: EnumLocale;
}

export default class LanguageList {
  static readonly items: ILanguageItem[] = [
    {
      displayName: "繁",
      locale: EnumLocale.TraditionalChinese,
    },
    {
      displayName: "EN",
      locale: EnumLocale.English,
    },
    {
      displayName: "ไทย",
      locale: EnumLocale.Thai,
    }
  ];
}
