import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface ForgotPasswordSliceState {
    email: string,    
    phoneAreaCode:string,
    newPassword: string,
    confirmNewPassword: string,
    validateCode:string,
    validateCodePrefix:string
}

const initialState: ForgotPasswordSliceState = {
    email: "",    
    phoneAreaCode:"",
    newPassword: "",
    confirmNewPassword: "",
    validateCode:"",
    validateCodePrefix:""
};

export const forgotPasswordSlice = createSlice({
    name: "forgotPassword",
    initialState,
    reducers: {
        setEmail: (state, action: PayloadAction<string>) => {
            state.email = action.payload;
        },
        setNewPassword: (state, action: PayloadAction<string>) => {
            state.newPassword = action.payload;
        },
        setConfirmNewPassword: (state, action: PayloadAction<string>) => {
            state.confirmNewPassword = action.payload;
        },
        setValidateCode:(state, action: PayloadAction<string>) => {
            state.validateCode = action.payload;
        },        
        setValidateCodePrefix:(state, action: PayloadAction<string>) => {
            state.validateCodePrefix = action.payload;
        },
        setPhoneAreaCode: (state, action: PayloadAction<string>) => {
            state.phoneAreaCode = action.payload;            
        },
    }
});

export const { setEmail,  setNewPassword, setPhoneAreaCode,setValidateCode, setConfirmNewPassword, setValidateCodePrefix } = forgotPasswordSlice.actions;
export default forgotPasswordSlice.reducer;