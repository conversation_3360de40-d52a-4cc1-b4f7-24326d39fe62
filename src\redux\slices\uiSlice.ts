import { createSlice, PayloadAction } from "@reduxjs/toolkit";

type IUIState = {
    visible: boolean;
};
type IUIExtraState<T> = Omit<T, "visible">;

const NoArgumentUIState: IUIState = { visible: false };
type LoginUIState = { tabIndex: number; }
const CreateNoArgumentUIInitialState = (): IUIState => Object.assign({}, NoArgumentUIState)
const CreateUIInitialState = <T>(UIExtraProps: IUIExtraState<T>): IUIState & IUIExtraState<T> => {
    return {
        ...NoArgumentUIState,
        ...(UIExtraProps || {})
    };
};

export type UISliceState = {
    overlay: {
        navigation: IUIState,
    },
    modal: {
        login: IUIState & LoginUIState,
        accountVerification: IUIState,
        kioskPaymentMethod: IUIState,
    }
};
const initialState: UISliceState = {
    overlay: {
        navigation: CreateNoArgumentUIInitialState(),
    },
    modal: {
        login: CreateUIInitialState<LoginUIState>({ tabIndex: 0 }),
        accountVerification: CreateNoArgumentUIInitialState(),
        kioskPaymentMethod: CreateNoArgumentUIInitialState(),
    }
};
export const uiSlice = createSlice({
    name: "ui",
    initialState,
    reducers: {
        // Overlay
        openNavigationMenu: (state) => {
            state.overlay.navigation.visible = true;
        },
        closeNavigationMenu: (state) => {
            state.overlay.navigation.visible = false;
        },
        // Modal
        openLoginModal: (state, action: PayloadAction<LoginUIState | undefined>) => {
            if (!action.payload) {
                state.modal.login.visible = true;
                return;
            }
            state.modal.login = {
                visible: true,
                ...action.payload
            };
        },
        closeLoginModal: (state) => {
            state.modal.login.visible = false;
        },
        setLoginModalState: (state: UISliceState, action: PayloadAction<LoginUIState>) => {
            state.modal.login = {
                ...state.modal.login,
                ...action.payload
            };
        },
        openAccountVerificationModal: (state) => {
            state.modal.accountVerification.visible = true;
        },
        closeAccountVerificationModal: (state) => {
            state.modal.accountVerification.visible = false;
        },
        openKioskPaymentMethodModal: (state) => {
            state.modal.kioskPaymentMethod.visible = true;
        },
        closeKioskPaymentMethodModal: (state) => {
            state.modal.kioskPaymentMethod.visible = false;
        }
    }
});

const {
    // Overlay
    openNavigationMenu,
    closeNavigationMenu,
    // Modal
    openLoginModal,
    closeLoginModal,
    setLoginModalState,
    openAccountVerificationModal,
    closeAccountVerificationModal,
    openKioskPaymentMethodModal,
    closeKioskPaymentMethodModal
} = uiSlice.actions;

export const OverlayActions = {
    openNavigationMenu,
    closeNavigationMenu
};
export const ModalActions = {
    openLoginModal,
    closeLoginModal,
    setLoginModalState,
    openAccountVerificationModal,
    closeAccountVerificationModal,
    openKioskPaymentMethodModal,
    closeKioskPaymentMethodModal
};
export default uiSlice.reducer;