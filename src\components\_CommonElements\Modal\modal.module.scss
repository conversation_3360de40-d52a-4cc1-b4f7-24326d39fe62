@use "sass:map";
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;
@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/fluid.module.scss" as fluid;

.contents {
    margin-top: 20px;
    text-align: center;
    padding: 20px 0;
    overflow: auto;
}

;

div.background {
    z-index: 200;
}

.container {
    color: map.get(theme.$color, "text");
    z-index: 220;
    min-width: viewport.$website-min-width;
    border-radius: map.get(theme.$border, "radius") !important;
    padding: 0 0 20px 0;
    margin: 0 5px;
    box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.5);
    border: map.get(theme.$presets, "dark-border");
    background-color: map.get(theme.$color, "background");
    position: relative;
    overflow: visible !important;

    &.withTitle {
        & .title {
            $height: 30px;
            position: absolute;
            height: $height;
            top: -$height / 2;
            left: 0;
            @include fluid.font-size(8px, 18px, viewport.$mobile-breakpoint, viewport.$tablet-breakpoint);
            padding: 4px 25px;

            font-weight: 700;
            user-select: none;
            margin: 0 0 -20px 20px;
            z-index: 1;
            $letter-spacing: 4px;
            letter-spacing: $letter-spacing;
            display: flex;
            justify-content: center;
            align-items: center;
            color: black;

            @include viewport.within("mobile") {
                margin: 0 0 -20px 0px;
            }

            border: {
                color: map.get(theme.$color, "primary");
                width: map.get(theme.$border, "width");
                style: solid;
                radius: map.get(theme.$border, "radius");
            }

            background-color: map.get(theme.$color, "background");
        }
    }
}

.tabContainer {
    display: flex;
    width: 100%;
    max-width: 1000px;

    & button[class~="MuiButtonBase-root"] {
        color: map.get(theme.$color, "text");
    }
}

.modal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // max-height: 100%;
    overflow-y: hidden;
}

.scrollContainer {
    overflow-y: auto;
    width: 100%;
    max-width: 1000px;
    padding: 0 20px;
    max-height: calc(90vh - 10px - 40px - 5px);
    /*
  &:before {
    content: "";
    margin-top: 7px;
    display: block;
  }
  */
}

.closeButton {
    display: flex;
    align-items: middle;
    justify-content: center;
    position: absolute;
    top: -20px;
    right: -20px;

    & svg {
        color: map.get(theme.$color, "text");
        background-color: map.get(theme.$color, "background");
        border-radius: 50%;
    }
}