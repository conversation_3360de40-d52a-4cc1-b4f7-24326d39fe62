@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/animations.module.scss" as animations;
@use "@/styles/utils/viewport.module.scss" as viewport;
@import "rfs/scss";

.campaignBg {
    &.haikyuu2404tst {
        // background: 
        //     linear-gradient(135deg,#0000 20.5%,#102a4b 0 29.5%,#0000 0) 0 16px,
        //     linear-gradient( 45deg,#0000 8%,#102a4b 0 17%, #0000 0 58%) 32px 0,
        //     linear-gradient(135deg,#0000 8%,#102a4b 0 17%, #0000 0 58%,#102a4b 0 67%,#0000 0),        
        //     linear-gradient( 45deg,#0000 8%,#102a4b 0 17%, #0000 0 58%,#102a4b 0 67%,#0000 0 83%,#102a4b 0 92%,#0000 0),
        //     #f1f1f1;
        background-color: black;
        background-size: 64px 64px;
    }
}

.banner {
    display: block;
    max-height: 500px;
    margin: 0 auto;
    width: 100%;
    object-fit: contain;
}

.sectionGap0 {
    gap: 0 !important;
}
.sectionDesc {
    margin-top: 1.5rem;
}
.sectionBg {
    background: 
        linear-gradient(135deg,#0000 20.5%,#102a4b 0 29.5%,#0000 0) 0 16px,
        linear-gradient( 45deg,#0000 8%,#102a4b 0 17%, #0000 0 58%) 32px 0,
        linear-gradient(135deg,#0000 8%,#102a4b 0 17%, #0000 0 58%,#102a4b 0 67%,#0000 0),        
        linear-gradient( 45deg,#0000 8%,#102a4b 0 17%, #0000 0 58%,#102a4b 0 67%,#0000 0 83%,#102a4b 0 92%,#0000 0),
        #f1f1f1;
}

.toBuy {
    display: flex;
    align-items: center;
    justify-content: center;

    margin-bottom: 2rem;
}

.descriptionSection {
    margin-top: 25px;

    & .descriptionContent {
        & img {
            display: block;
            margin: 0 auto;
            width: 100%;
            object-fit: contain;
        }
    }

    & .descriptionTitle {
        width: 0;
        height: 0;
        border-left: 24px solid transparent;
        border-right: 60px solid transparent;
        border-top: 165px solid #f00;
        transform: translate(-12px, 0px) skew(31deg, 315deg);
        color: white;
        position: absolute;
        z-index: 99;    
    
        & p {
            text-align: center;
            top: 0px;
            left: 0px;
            position: relative;
            width: 3rem;
            height: 3rem;
            font-size: 8pt;
            margin: 0px;
            transform: translate(-8px, -155px) skew(336deg, 29deg);
            text-shadow: 0 1px black, -1px 0 black, 1px 1px black, -1px -1px black;
        }
    }

    &:nth-child(2n) {
        & .descriptionTitle {
            border-left: 25px solid transparent;
            border-right: 90px solid transparent;
            border-top: 165px solid #f00;
            transform: translate(min(842px + 10%, 930px), 0px) skew(-18deg, 390deg);

            & p {
                width: 5rem;
                height: 5rem;
                font-size: 11pt;
                transform: translate(-25px, -155px) skew(365deg, 0deg);
            }
        }
    }
}

.colStackFull {
    @include viewport.within("tablet") {
        flex: 1 1;
    }
}

.itemCardContainer {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    min-height: 220px;

    & .itemCardContent {
        min-height: 100%;
    }

    & .itemNameContainer {
        display: flex;
        flex-direction: row;
        flex: 1;
    }

    & .itemQuantityContainer {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
    }
}

.quantity {
    font-size: 1.1rem;
    color: rgb(87, 87, 87) !important;
}

.purchaseSection {
    width: auto;
    padding-left: 1rem;
    padding-right: 1rem;

    & .label {
        color: white;
        text-shadow: -1px -1px 2px #102a4b, 1px 1px 1px #102a4b, -1px 1px #102a4b, 1px -1px #102a4b;
    }
    & .row {
        display: flex;
        flex-direction: row;
        flex: 1;
        gap: 10px;
        align-items: center;
        @include viewport.within("tablet") {
            flex-direction: column;
            align-items: flex-start;
        };
    }
    & .ticketType {
        flex: 1;
    }
    & .ticketPrice {
        justify-content: center;
        align-items: center;
    }
    & .tnc {
        color: white;
        text-shadow: -1px -1px 2px #102a4b, 1px 1px 1px #102a4b, -1px 1px #102a4b, 1px -1px #102a4b;

        & * {
            color: white;
        }
    }
    & .total {
        color: white;
        text-shadow: -1px -1px 2px #102a4b, 1px 1px 1px #102a4b, -1px 1px #102a4b, 1px -1px #102a4b;
    }
}