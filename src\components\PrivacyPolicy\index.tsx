import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import Section from "@/components/_CommonElements/Section";

const PrivacyPolicy = () => {
    const { t: termsTranslation } = useTranslation(EnumTranslationJson.Terms);
    return (
        <Section
            content={termsTranslation("sections.privacyPolicy.content", {
                UPDATE_DATE: "2023/07/18",
                EMAIL_URL: "<EMAIL>",
                PLATFORM_DOMAIN: "incutix.com",
                PLATFORM_NAME: "INCUTix"
            })}
        />
    );
}

export default PrivacyPolicy;