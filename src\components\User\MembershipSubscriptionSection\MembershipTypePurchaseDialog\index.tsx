import { TextButton } from "@/components/_CommonElements/Button";
import { Checkbox } from "@/components/_CommonElements/Input";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import MembershipType from "@/models/api/models/MembershipType";
import { Box, DialogActions, DialogContent, Divider, Stack } from "@mui/material";
import Markdown from "markdown-to-jsx";
import { useTranslation } from "next-i18next";
import React, { useCallback, useState } from "react";
import { Fragment } from "react";

const AgreementLabel = React.memo(() => {
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);

    return (
        <span>
            <Markdown
                options={{
                    wrapper: Fragment,
                    forceWrapper: false
                }}
            >
                {profileTranslation("membershipSubscription.dialog.purchase.agreementClaim")}
            </Markdown>
        </span>
    );
});
AgreementLabel.displayName = "AgreementLabel";

interface MembershipTypePurchaseDialogContentProps {
    membershipType?: MembershipType;
    onClose: () => void;
    onConfirm: () => void;
}

const MembershipTypePurchaseDialogContent = (props: MembershipTypePurchaseDialogContentProps) => {
    const { 
        membershipType,
        onClose,
        onConfirm
    } = props;

    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);

    const [isAcceptedAgreement, setIsAcceptedAgreement] = useState<boolean>(false);
    const onChangeIsAcceptedAgreement = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setIsAcceptedAgreement(e.target.checked);
    }, []);

    const checkIsAcceptedAgreement = useCallback(() => isAcceptedAgreement, [isAcceptedAgreement]);

    return (
        <>
            <DialogContent dividers={true}>
                <Stack direction={"column"}>
                    <Markdown
                        options={{
                            wrapper: Fragment,
                            forceWrapper: false,
                            overrides: {
                            },
                        }}
                    >
                        「奇妙處處通」優惠條款及細則
                    </Markdown>
                </Stack>
            </DialogContent>
            <Box pl={2}>
                <Checkbox
                    label={<AgreementLabel />}
                    checked={isAcceptedAgreement}
                    onChange={onChangeIsAcceptedAgreement}
                />
            </Box>
            <DialogActions>
                <TextButton onClick={onClose}>{profileTranslation("membershipSubscription.dialog.common.buttons.cancel")}</TextButton>
                <TextButton onClick={onConfirm} disabled={!checkIsAcceptedAgreement()}>{profileTranslation("membershipSubscription.dialog.common.buttons.confirm")}</TextButton>
            </DialogActions>
        </>
    );
};

export default MembershipTypePurchaseDialogContent;