import type { NextPage } from "next";
import { GetServerSideProps } from "next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import i18n from "@/utils/i18n";
import Section from "@/components/_CommonElements/Section";
import classNames from "classnames";

import styles from "@/components/_PageComponents/Campaign/campaign.module.scss";
import { useMemo } from "react";

interface Props {
    fallback: {
        orderId: string,
        alreadyRedeem: string,
    }
}
const CampaignProductRedeemPage: NextPage<Props> = (props) => {
    const { fallback } = props;
    const { orderId, alreadyRedeem } = fallback;

    const isRedeem = useMemo(() => alreadyRedeem.toLowerCase() === "true", [alreadyRedeem]);

    return (
        <>
            <div className={classNames(styles.haikyuu2404tst)}>
                <Section
                    containerSize="tight"
                    className={{
                        content: styles.sectionGap0
                    }}
                    content={
                        <div>
                            <h2>編號：{orderId}</h2>
                            <p style={{ color: isRedeem ? "red" : "green" }}>
                                {isRedeem ? "已領取" : "可領取"}
                            </p>
                        </div>
                    }
                />
            </div>
        </>
    );
};
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [EnumTranslationJson.Campaign, EnumTranslationJson.Purchase],
    context,
    getServerSideProps: context => {
        const { query } = context;
        return {
            props: {
                fallback: {
                    orderId: query.orderId,
                    alreadyRedeem: query.alreadyRedeem
                }
            }
        }
    }
});
export default CampaignProductRedeemPage;