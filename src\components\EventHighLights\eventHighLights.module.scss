@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

$tablet-header-height: map.get(theme.$height, "tablet-header");
$desktop-header-height: map.get(theme.$height, "desktop-header");
$background-color: map.get(theme.$color, "background");
$primary-color: map.get(theme.$color, "primary");
$secondary-color: map.get(theme.$color, "secondary");

.highLightCarouselContainer {
    & .highLightCarouselSlider {
        height: 240px;
        
        @include viewport.beyond("mobile") {
            height: 30rem;
        }

        & .highLightCarouselSliderItem {
            height: 240px;

            @include viewport.beyond("mobile") {
                height: 30rem;
            }

            & .imageHeader {
                position: relative;
                overflow: hidden;
            
                & .eventBannerImage {
                    height: 240px;
                    object-fit: contain;
                    position: relative;
                    z-index: 1;

                    @include viewport.beyond("mobile") {
                        height: 30rem;
                    }
                }
            
                & .eventBannerImageBlurredBackground {
                    filter: blur(100px) saturate(0.5);
                    position: absolute;
                    top: 0;
                    background-repeat: no-repeat;
                    height: 100%;
                    width: 100%;
                    background-size: cover;
                    background-position-y: calc(1rem / 8);

                    @include viewport.beyond("mobile") {
                        background-position-y: calc(-15rem / 8);
                    }
                }
            }
        }
    }
}

.developmentOnly {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}
.button {
    padding: 5px 10px;
    background: white;
    border-radius: 20px;
    font-size: 2rem;
}