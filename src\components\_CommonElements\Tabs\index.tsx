import styles from "@/components/_CommonElements/Tabs/tabs.module.scss";
import classNames from "classnames";

import { useMemo, useState } from "react";
import MUITabs from "@mui/material/Tabs";
import MUITab from "@mui/material/Tab";
import Box from "@mui/material/Box";

interface TabsProps {
  tabs: {
    name: string;
    active?: boolean;
    action?: () => void;
    contents?: React.ReactNode;
  }[];
}

interface TabPanelProps {
    contents?: React.ReactNode;
    active: boolean;
    index: number;
}

export function TabPanel(props: TabPanelProps) {
  const { contents, active, index, ...other } = props;

  const tabContent = useMemo(() => {
    if (!active) {
        return null;
    }
    return contents;
  }, [contents, active]);

  return (
    <div
      role="tabpanel"
      data-tabpanel-index={index}
      hidden={!active}
      {...other}
    >
      {tabContent}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `tab-${index}`,
    "aria-controls": `tabpanel-${index}`,
  };
}

const Tabs = (props: TabsProps) => {
  const { tabs } = props;

  const defaultActiveIndex = useMemo(() => {
    const tabDefaultActiveIndex = tabs.findIndex(item => item.active);
    return tabDefaultActiveIndex == -1 ? 0 : tabDefaultActiveIndex;
  }, [tabs]);

  const [value, setValue] = useState(defaultActiveIndex);
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <div className={classNames(styles.tabContainer)}>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2, width: "100%" }}>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <MUITabs
            indicatorColor="primary"
            value={value}
            onChange={handleChange}
          >
            {tabs.map((each, i) => {
              return (
                <MUITab
                  label={each.name}
                  key={i}
                  onClick={each.action}
                  disableFocusRipple
                  {...a11yProps(i)}
                />
              );
            })}
          </MUITabs>
        </Box>
        { tabs.map((each, i) => (
            <TabPanel
                key={i}
                index={i}
                active={value === i}
                contents={each.contents}
            /> 
        )) }
      </Box>
    </div>
  );
};

export default Tabs;
