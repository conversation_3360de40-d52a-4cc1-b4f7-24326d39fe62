import Grid from '@mui/material/Grid';

interface FieldContainerProps {
  children?: any;
  className?: string;
  flexDirection?: "row" | "column";
  columnSpacing?: number;
  justifyContent?: string;
  rowSpacing?: number;
  spacing?: number;
  horizontalCenter?: string | true;
  verticalCenter?: string | true;
  visible?: boolean;
  xs?: any;
  sm?: any;
  md?: any;
  inlineStyle?: { [key: string]: any };
};

const FieldContainer = (props: FieldContainerProps) => {
  const {
    children,
    className,
    flexDirection,
    columnSpacing,
    justifyContent,
    rowSpacing,
    spacing,
    horizontalCenter,
    verticalCenter,
    visible = true,
    xs,
    sm,
    md,
    inlineStyle,
  } = props;

  return (<>
    <Grid
      container
      item
      className={className}
      direction={flexDirection ?? ("row" || "column")}
      columnSpacing={columnSpacing}
      rowSpacing={rowSpacing}
      spacing={spacing}
      justifyContent={justifyContent ?? (horizontalCenter && "center")}
      alignItems={verticalCenter && "center"}
      xs={xs}
      sm={sm}
      md={md}
      sx={{
        ...inlineStyle,
        display: inlineStyle?.display ?? (visible ? "" : "none"),
      }}
    >
      {children}
    </Grid>
  </>);
};

export default FieldContainer;