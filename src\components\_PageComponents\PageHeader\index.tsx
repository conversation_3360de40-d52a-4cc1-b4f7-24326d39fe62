import { ReactNode } from "react";
import styles from "./pageHeader.module.scss";
import classNames from "classnames";

interface SectionHeadProps {
  title?: string;
  children?: ReactNode;
  noLetterSpacing?: boolean;
  noUnderline?: boolean;
  size?: "small" | "medium" | "large";
}

const SectionHead = (props: SectionHeadProps) => {
  const { 
    title,
    children,
    noLetterSpacing,
    noUnderline,
    size = "medium",
  } = props;

  return (<>
    <div className={classNames(styles.head)}>

      {title &&
        <h1 
          className={classNames(
            styles.titleContainer,
            noUnderline && styles.noUnderline,
            !noLetterSpacing && styles.letterSpacing,
            size && styles[size]
          )}
        >
            <span className={styles.title}>{title}</span>
        </h1> }

      {children}

    </div>
  </>);
};

export default SectionHead;