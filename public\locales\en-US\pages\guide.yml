complete:
  title: "<Nav title='Complete Process' linkTo='complete'/>"
  content: |
    <StepContainer linkTo='complete'>
      <StepTitle> Complete Process </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-user-plus' label='Register as a member' helpText='During registration, you will receive an email verification to confirm that you can receive messages and complete the registration process.' />
        <StepBoxs numbering='2' icon='fa-mouse-pointer' label='Purchase tickets' helpText='Click "Buy Tickets" on the dedicated event page.' />
        <StepBoxs numbering='3' icon='fa-credit-card' label='Payment' helpText='After a successful purchase, you will receive a QR code for your ticket. This QR code can be obtained through your registered email or by accessing the "My Account" ＞ "Ticket(s)" section on the INCUTix website.' />
        <StepBoxs numbering='4' icon='fa-qrcode' label='Show QR Code to enter event on time.' helpText='Click "My Account", go to "Ticket(s)", then show the "QR Code" at event entrance.' />
      </StepBoxsContainer>
    </StepContainer>
register:
  title: "<Nav title='Register as a member' linkTo='register'/>"
  content: |
    <StepContainer linkTo='register'>
      <StepTitle> Register as a member </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-user-plus' label='' helpText='Click "Register".' />
        <StepBoxs numbering='2' icon='fa-file-signature' label='' helpText='After filling in the personal information, click “Submit” and click “Obtain” to receive the verification code. A verification code will be sent to your email in a few minutes. (Note: If you do not receive the email, kindly check your spam folder)' />
        <StepBoxs numbering='3' icon='fa-envelope' label='' helpText='After receiving the verification code email, take note of the 6-digit number. Go to the page and input the 6-digit number. Or click "My Account" and go to "Account Information," then click “Verify Email” to get verification code in the bottom right corner.' />
        <StepBoxs numbering='4' icon='fa-right-to-bracket' label='' helpText='Enter the 6-digit verification code and click "Confirm" to complete membership registration' />
      </StepBoxsContainer>
    </StepContainer>
purchase:
  title: "<Nav title='Purchase Tickets' linkTo='purchase'/>"
  content: |
    <StepContainer linkTo='purchase'>
      <StepTitle> Purchase Tickets </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-mouse-pointer' label='' helpText='Click "Buy Tickets" on the dedicated event page.' />
        <StepBoxs numbering='2' icon='fa-calendar-check' label='' helpText='Select the session and number of tickets to purchase.' />
        <StepBoxs numbering='3' icon='fa-credit-card' label='' helpText='Complete online payment within the specified time.' />
        <StepBoxs numbering='4' icon='fa-thumbs-up' label='' helpText='After successful payment, you will be redirected to the purchase completion page.' />
        <StepBoxs numbering='5' icon='fa-ticket-simple' label='' helpText='Click "My Account", go to "Ticket(s)", then show the "QR Code" at event entrance.' />
      </StepBoxsContainer>
    </StepContainer>
merchandise:
  title: "<Nav title='Purchase Merchandise' linkTo='merchandise'/>"
  content: |
    <StepContainer linkTo='merchandise'>
      <StepTitle> Purchase Merchandise </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-mouse-pointer' label='' helpText='Visit the dedicated product page to make a purchase.' />
        <StepBoxs numbering='2' icon='fa-cart-plus' label='' helpText='Select the product and quantity.' />
        <StepBoxs numbering='3' icon='fa-credit-card' label='' helpText='Complete online payment within the specified time.' />
        <StepBoxs numbering='4' icon='fa-thumbs-up' label='' helpText='After successful payment, you will be redirected to the order confirmation page.' />
        <StepBoxs numbering='5' icon='fa-envelope' label='' helpText='Click "My Account", go to "Product Orders", and follow the instructions in the email to redeem the product.' />
      </StepBoxsContainer>
    </StepContainer>
getTicket:
  title: "<Nav title='Bind/Share Tickets' linkTo='getTicket'/>"
  content: |
    <StepContainer linkTo='getTicket'>
      <StepTitle> Bind/Share Tickets </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-address-card' label='' helpText='Go to "My Account" and click “Unbound Ticket(s)” to view purchased activities.' />
        <StepBoxs numbering='2' icon='fa-ticket-simple' label='' helpText='Click on the redemption code to bind.' />
        <StepBoxs numbering='3' icon='fa-share' label='' helpText='For purchases of more than 1 ticket, you will receive multiple redemption code links. You can either bind all the codes to one account or bind one code to your account and share the rest with friends using the provided redemption code links.' />
        <StepBoxs numbering='4' icon='fa-receipt' label='' helpText='Go to "My Account" to view bound activities.' />
        <StepBoxs numbering='5' icon='fa-qrcode' label='' helpText='Show QR Code to enter event on time.' />
      </StepBoxsContainer>
    </StepContainer>
getTicketBySharedLink:
  title: "<Nav title='Bind with Redemption Code Link' linkTo='getTicketBySharedLink'/>"
  content: |
    <StepContainer linkTo='getTicketBySharedLink'>
      <StepTitle> Bind with Redemption Code Link </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-ticket-simple' label='' helpText='Enter binding page through redemption code link.' />
        <StepBoxs numbering='2' icon='fa-user-plus' label='' helpText='If not logged in, please register/login first.' />
        <StepBoxs numbering='3' icon='fa-receipt' label='' helpText='Go to "My Account" to view bound activities.' />
        <StepBoxs numbering='4' icon='fa-qrcode' label='' helpText='Show QR Code to enter event on time.' />
      </StepBoxsContainer>
    </StepContainer>
getTicketByEnterCode:
  title: "<Nav title='Bind with Redemption Code' linkTo='getTicketByEnterCode'/>"
  content: |
    <StepContainer linkTo='getTicketByEnterCode'>
      <StepTitle> Bind with Redemption Code </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-address-card' label='' helpText='Click on "My Account".' />
        <StepBoxs numbering='2' icon='fa-key' label='' helpText='Enter the redemption code.' />
        <StepBoxs numbering='3' icon='fa-ticket-simple' label='' helpText='Go to "My Account" to view bound activities.' />
        <StepBoxs numbering='4' icon='fa-qrcode' label='' helpText='Show QR Code to enter event on time.' />
      </StepBoxsContainer>
    </StepContainer>

1complete:
  title: "<Nav title='Complete Process' linkTo='complete'/>"
  content:
    <StepContainer linkTo='complete'>
      <StepTitle> Complete Process </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-user-plus' label='Register as a member' helpText='During registration, you will receive Email verification to confirm that you can receive messages and complete the registration process.' />
        <StepBoxs numbering='2' icon='fa-mouse-pointer' label='Purchase tickets' helpText='Click "Buy Tickets" on the dedicated event page.' />
        <StepBoxs numbering='3' icon='fa-credit-card' label='Payment' helpText='After successfully purchasing, you will receive a ticket redemption code and a redemption link, which can be viewed or shared with friends in "My Account".' />
        <StepBoxs numbering='4' icon='fa-ticket-simple' label='Bind tickets' helpText='Bind the tickets redemption code to your registered account before the event starts. Ticket redemption code to be bound to one account only.' />
        <StepBoxs numbering='5' icon='fa-qrcode' label='Watch live at the specified time' helpText='Click the "Watch Program" button in the upper right corner or go to "My Account" and view "Bound Activities", then click "Qr Code".' />
      </StepBoxsContainer>
    </StepContainer>
1register:
  title: "<Nav title='Register as a member' linkTo='register'/>"
  content:
    <StepContainer linkTo='register'>
      <StepTitle> Register as a member </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-user-plus' label='' helpText='Click "Register".' />
        <StepBoxs numbering='2' icon='fa-file-signature' label='' helpText='Fill in personal information to register, including name, email address, and set a password. Please make sure the email is correct. You must agree to the terms and conditions of registration and confirm that the information is correct before clicking "Register".' />
        <StepBoxs numbering='3' icon='fa-envelope' label='' helpText='Receive Email verification to verify your email address.' />
        <StepBoxs numbering='4' icon='fa-right-to-bracket' label='' helpText='Complete registration and log in.' />
        <Accordion title='Register as a member'>
          <DetailStep label='Click "Register".' image='step-1.png' numbering='1' contents='Click the "Register" button.' />
          <DetailStep label='Fill in personal information.' image='step-2.png' numbering='2' contents='Please fill in your name, email address, and set a password. Please make sure the email is correct. You must agree to the terms and conditions of registration and confirm that the information is correct before clicking "Register".' />
          <DetailStep label='Receive Email verification to verify your email address.' image='step-3.png' numbering='3' contents='The system will send a verification code via Email within one minute. Then, please enter the verification code. If you do not receive the verification code, you can click "Get Verification Code" again. If there are still problems, please refer to the troubleshooting methods on the FAQ page.' />
          <DetailStep label='Complete registration and log in.' image='step-4.png' numbering='4' contents='After completion, the system will display a success message and automatically log in.' />
        </Accordion>
      </StepBoxsContainer>
    </StepContainer>
1purchase:
  title: "<Nav title='Purchase Tickets' linkTo='purchase'/>"
  content:
    <StepContainer linkTo='purchase'>
      <StepTitle> Purchase Tickets </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-mouse-pointer' label='' helpText='Click "Buy Tickets" on the dedicated event page.' />
        <StepBoxs numbering='2' icon='fa-calendar-check' label='' helpText='Select the session and number of tickets to purchase.' />
        <StepBoxs numbering='3' icon='fa-credit-card' label='' helpText='Complete online payment within the specified time.' />
        <StepBoxs numbering='4' icon='fa-thumbs-up' label='' helpText='After successful payment, you will be redirected to the purchase completion page.' />
        <StepBoxs numbering='5' icon='fa-ticket-simple' label='' helpText='Bind or share the redemption code link.' />
      </StepBoxsContainer>
      <Accordion title='Purchase Tickets'>
        <DetailStep label='Dedicated event page' image='step-1.png' numbering='1' contents='Go to the dedicated event page and click the "Buy Tickets" button.' />
        <DetailStep label='Select the session and number of tickets to purchase.' image='step-2.png' numbering='2' contents='Click the dropdown menu to select the session and enter the number of tickets to purchase. Click "Confirm Purchase". Each set of redemption codes can only be bound to one account, so please complete the binding before the start of the event. If you purchase more than one ticket, you can give the redemption code to a friend to bind to their account.' />
        <DetailStep label='Payment' image='step-3.png' numbering='3' contents='Follow the instructions to enter your credit card information and complete the payment within the specified time. This website accepts Visa/Mastercard/UnionPay for online payment.' />
        <DetailStep label='After successful payment, you will be redirected to the purchase completion page.' image='step-4.png' numbering='4' contents='The page will display the "Purchase Completed" page.' />
        <DetailStep label='Bind or share the redemption code link.' image='step-5.png' numbering='5' contents='You can view, bind, and share the redemption code link on the page, or go to "My Account" later to view "Purchased Activities" and then bind or share the redemption code link.' />
      </Accordion>
    </StepContainer>
1getTicket:
  title: "<Nav title='Bind/Share Tickets' linkTo='getTicket'/>"
  content:
    <StepContainer linkTo='getTicket'>
      <StepTitle> Bind/Share Tickets </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-address-card' label='' helpText='Go to "My Account" to view purchased activities.' />
        <StepBoxs numbering='2' icon='fa-ticket-simple' label='' helpText='Click on the redemption code to bind.' />
        <StepBoxs numbering='3' icon='fa-share' label='' helpText='If you have purchased more than one ticket, you can share the redemption code link with friends.' />
        <StepBoxs numbering='4' icon='fa-receipt' label='' helpText='Go to "My Account" to view bound activities.' />
        <StepBoxs numbering='5' icon='fa-qrcode' label='' helpText='Show QR Code to enter event on time.' />
      </StepBoxsContainer>
      <Accordion title='Bind/Share Tickets'>
        <DetailStep label='Go to "My Account" to view purchased activities.' image='step-1.png' numbering='1' contents='After logging in, click on "My Account" in the navigation bar and go to the "Purchased Activities" page.' />
        <DetailStep label='Click on the redemption code to bind.' image='step-2.png' numbering='2' contents='In the activity you want to bind, click on the "Redemption Code" button, and click "Bind" for a redemption code.' />
        <DetailStep label='If you have purchased more than one ticket, you can share the redemption code link with friends.' image='step-3.png' numbering='3' contents='If you have purchased more than one ticket, you can share the redemption code link with friends. Next to each redemption code, there is a "Share" button.    Click it to send the link and message to your contacts via email or WhatsApp. You can also copy the default sharing link and message by clicking the "Copy" button and paste it into other messaging apps to send.' />
        <DetailStep label='Go to "My Account" to view bound activities.' image='step-4.png' numbering='4' contents='After successful binding, you can go to the "Bound Tickets" page to confirm.' />
        <DetailStep label='Show QR Code to enter event on time.' image='step-5.png' numbering='5' contents='Click “My Account”, go to "Bound Tickets", then show the "QR Code" at event entrance.' />
      </Accordion>
    </StepContainer>
1getTicketBySharedLink:
  title: "<Nav title='Bind with Redemption Code Link' linkTo='getTicketBySharedLink'/>"
  content:
    <StepContainer linkTo='getTicketBySharedLink'>
      <StepTitle> Bind with Redemption Code Link </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-ticket-simple' label='' helpText='Enter binding page through redemption code link.' />
        <StepBoxs numbering='2' icon='fa-user-plus' label='' helpText='If not logged in, please register/login first.' />
        <StepBoxs numbering='3' icon='fa-receipt' label='' helpText='Go to "My Account" to view bound activities.' />
        <StepBoxs numbering='4' icon='fa-qrcode' label='' helpText='Show QR Code to enter event on time.' />
      </StepBoxsContainer>
      <Accordion title='Bind with Redemption Code Link'>
        <DetailStep label='Enter binding page through redemption code link.' image='step-1.png' numbering='1' contents='Click on the redemption code link to enter the "Bind Ticket" page. The "Redemption Code" field is automatically filled in, and the user only needs to click the "Bind" button to confirm.' />
        <DetailStep label='If not logged in, please register/login first.' image='step-2.png' numbering='2' contents='If not logged in, the system will prompt you to register/login first. If you are not a member, please register and login before clicking the "Bind" button again.' />
        <DetailStep label='Go to "My Account" to view bound activities.' image='step-3.png' numbering='3' contents='After successful binding, you can go to the "Bound Activities" page to confirm.' />
        <DetailStep label='Show QR Code to enter event on time.' image='step-4.png' numbering='4' contents='Click on "Qr Code" on the right or go to the "Bound Activities" page and click "Go to Live Broadcast" to watch at the specified time.' />
      </Accordion>
    </StepContainer>
1getTicketByEnterCode:
  title: "<Nav title='Bind with Redemption Code' linkTo='getTicketByEnterCode'/>"
  content:
    <StepContainer linkTo='getTicketByEnterCode'>
      <StepTitle> Bind with Redemption Code </StepTitle>
      <StepBoxsContainer>
        <StepBoxs numbering='1' icon='fa-address-card' label='' helpText='Click on "My Account".' />
        <StepBoxs numbering='2' icon='fa-key' label='' helpText='Enter the redemption code.' />
        <StepBoxs numbering='3' icon='fa-ticket-simple' label='' helpText='Go to "My Account" to view bound activities.' />
        <StepBoxs numbering='4' icon='fa-qrcode' label='' helpText='Show QR Code to enter event on time.' />
      </StepBoxsContainer>
      <Accordion title='Bind with Redemption Code'>
        <DetailStep label='Click on "My Account".' image='step-1.png' numbering='1' contents='Click on "My Account," then click on "Enter Redemption Code."' />
        <DetailStep label='Enter the redemption code.' image='step-2.png' numbering='2' contents='Enter the "redemption code" in the blank field, then click the "Submit" button to confirm the binding.' />
        <DetailStep label='Go to "My Account" to view bound activities.' image='step-3.png' numbering='3' contents='After successful binding, you can go to the "Bound Activities" page to confirm.' />
        <DetailStep label='Watch the live broadcast at the specified time.' image='step-4.png' numbering='4' contents='Click on "Qr Code" on the right or go to the "Bound Activities" page and click "Qr Code" to watch at the specified time.' />
      </Accordion>
    </StepContainer>
