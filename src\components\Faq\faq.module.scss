@use 'sass:map';
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;
@use "@/styles/utils/animations.module.scss" as animations;

$background-color: map.get(theme.$color, "background");
$tablet-header-height: map.get(theme.$height, "tablet-header");
$secondary-navigation-offset: map.get(theme.$height, "secondary-navigation-offset");

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    gap: 20px;
    .help {
        font: {
            size: 1.1rem;
        }
        margin: 24px;
    }
}

.displayLabel {
    margin: 16px 0;
    color: map.get(theme.$color, "text");
    border-left: 6px solid map.get(theme.$color, "secondary");
    padding: 0 0 0 10px;
    font: {
        size: 24px;
        weight: bold;
    };
}

.faqSection {
    display: flex;
    flex-direction: column;
    gap: inherit;
    width: 100%;
}