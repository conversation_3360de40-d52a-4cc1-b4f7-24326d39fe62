@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

*, *::before, *::after {
  margin: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  -webkit-tap-highlight-color: transparent;
};
img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}
html,
body {
  min-width: viewport.$website-min-width;
  min-height: viewport.$website-min-height;
  padding: 0;
  margin: 0 auto;
  color: map.get(theme.$color, "text");
  background-color: map.get(theme.$color, "background");
  border-color: map.get(theme.$color, "border");
  letter-spacing: normal;
  overflow-x: hidden;
};

#__next {
  font-family: theme.$font-family;
};

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
};

fieldset {
  border: map.get(theme.$border, "width") solid map.get(theme.$color, "border");
  border-radius: map.get(theme.$border, "radius");
};

button {
  color: inherit;
  border: 0;
  font-size: inherit;
  cursor: pointer;
  background-color: transparent;
};

a {
  color: inherit;
  text-decoration: none;
  display: contents;
};

ol, ol li { 
  margin-left: 0.3rem; 
  padding-left: 0.6rem; 
}

ul {
  padding: 0;
  & li {
    list-style: none;
  };
};

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 1000px map.get(theme.$color, "background") inset !important;
  box-shadow: 0 0 0 1000px map.get(theme.$color, "background") inset !important;
  -webkit-text-fill-color: map.get(theme.$color, "text");
};

:disabled {
  cursor: context-menu;
};

div.MuiBackdrop-root {
  background-color: rgba(0, 0, 0, 0.7);
};

div.MuiTooltip-tooltip {  
  background-color: grey;
  font-size: 14px;
  letter-spacing: normal;
  padding: 6px 12px 4px 12px;
  border-radius: 12px;
  user-select: none;

  span.MuiTooltip-arrow::before {
    background-color: grey;
  };
};

div, fieldset {
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    border-radius: 8px;
    background-color: map.get(theme.$color, "background");
  };
  
  &::-webkit-scrollbar {
    width: 8px;
    border-radius: 8px;
    background-color: #333333;
  };
  
  &::-webkit-scrollbar-thumb {
    border-radius: 8px;
    box-shadow: inset 0 0 6px rgba(0,0,0,.3);
    background-color: #888888;
  };
};

.image-gallery {
  &.bannerGallery {
    &.fullscreen-modal {
      background: rgba(17, 17, 17, .97);
      z-index: 101;
    }

    & .fullscreen {
      & .image-gallery-slide {
        max-height: 80vh;
      }
    }

    & .react-player__preview {
      background-size: contain !important;
      background-repeat: no-repeat;
      background-color: #000;
    }
  }
}

.notistack-SnackbarContainer.z-alert {
  z-index: 10003;
}