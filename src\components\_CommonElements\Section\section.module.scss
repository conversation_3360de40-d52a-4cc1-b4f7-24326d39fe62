@use "sass:map";
@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

$tablet-breakpoint: map.get(viewport.$breakpoint, "tablet");

.section {
    display: flex;
    flex-direction: column;
    gap: 10px;
    line-height: 2rem;
    &.wideSection {
        width: 100%;
    }

    &.thinSection {
        margin: 0 auto;
        max-width: min($tablet-breakpoint + 5% * 2, $tablet-breakpoint + 15px * 2);
        width: 100%;

        padding: {
            top: 0;
            right: \min(5%, 15px);
            bottom: 0;
            left: \min(5%, 15px);
        }
    }
    &.tightSection {
        margin: 0 auto;
        max-width: min(900px + 5% * 2, 900px + 15px * 2);
        width: 100%;
        padding: {
            top: 0;
            right: \min(5%, 15px);
            bottom: 0;
            left: \min(5%, 15px);
        }
    }

    & .paragraph {}

    & .label {
        &.xlarge {
            $highlight_line_size: 6px;
            $highlight_line_offset: 3px;
            position: relative;
            display: flex;
            gap: 4px;
            min-width: 150px;
            white-space: pre-line;
            font-size: 1.8rem;
            line-height: 3rem;
            font-weight: 800;
            letter-spacing: 4px;
            padding-bottom: $highlight_line_offset + $highlight_line_size;
            & span {
                position: relative;
                &:after {
                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: -$highlight_line_offset;
                    height: $highlight_line_size;
                    content: '';
                    background: linear-gradient(90deg, #F48121 40%, #429b3f 40%) left 0px bottom 3px no-repeat;
                }
            }
        }

        &.large {
            font-size: 1.5rem;
            line-height: 3rem;
            padding: 0 0 0 10px;
            border-left: 6px solid map.get(theme.$color, "secondary");
        }

        &.medium {
            font-size: 1rem;
            line-height: 2rem;
            padding: 0 0 0 10px;
            border-left: 6px solid map.get(theme.$color, "secondary");
        }

        &.small {
            font-size: 1rem;
            line-height: 2rem;
        }

        color: map.get(theme.$color, "text");
        font-weight: bold;
        font-size: inherit;
    }

    & .content {
        display: flex;
        flex-direction: column;
        gap: 10px;
        color: map.get(theme.$color, "text");
        white-space: normal;

        & .markdown {
            display: flex;
            flex-direction: column;
            gap: 10px;
            line-height: 2rem;
            font-size: 1rem;
            overflow: hidden;

            & img {
                max-width: 100%;
            }
        }
    }

    & .listItem {
        display: flex;
        flex-direction: row;
        gap: 10px;
        margin-left: 20px;

        &:before {
            content: '∙';
            display: block;
        }
    }
}