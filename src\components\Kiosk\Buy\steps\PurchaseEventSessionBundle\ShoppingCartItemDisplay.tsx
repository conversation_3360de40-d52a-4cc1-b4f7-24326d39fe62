import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { addItem, decreaseItemQuantity, increaseItemQuantity, removeItem } from "@/redux/slices/cartSlice";
import { dispatch, useSelector } from "@/redux/store";
import API from "@/utils/API";
import Card from "@mui/material/Card";
import Box from "@mui/material/Box"
import { useTranslation } from "next-i18next";
import { useSnackbar } from "notistack";
import { useCallback, useMemo, useState } from "react";
import CardContent from "@mui/material/CardContent";
import shoppingCartMenuStyles from "@/components/Event/ShoppingCart/ShoppingCartMenu/shoppingCartMenu.module.scss";
import ButtonGroup from "@mui/material/ButtonGroup";
import Button from "@mui/material/Button";
import { IconButton } from "@/components/_CommonElements/Button";
import DeleteIcon from '@mui/icons-material/Delete';
import { CircularProgress } from "@mui/material";
import { ShoppingCartSummarizedItem } from "@/models/api/models/ShoppingCart";
import i18n from "@/utils/i18n";
import { useRouter } from "next/router";
import classNames from "classnames";

interface ShoppingCartItemDisplayProps {
    item: ShoppingCartSummarizedItem
}

const ShoppingCartItemDisplay = (props: ShoppingCartItemDisplayProps) => {
    const { item } = props;
    const [ isUpdating, setIsUpdating ] = useState(false);
    const [ isDeleting, setIsDeleting ] = useState(false);
    const router = useRouter();
    const { enqueueSnackbar } = useSnackbar();
    const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);
    const cartItems = useSelector(state => state.cart.items);
    const quantity = useMemo(() => cartItems.find(ci => ci.eventSessionBundleId === item.eventSessionBundleId)?.quantity ?? item.quantity, [cartItems, item.eventSessionBundleId, item.quantity]);
    const removeItemInCart = useCallback((eventSessionBundleId: string) => {
        setIsDeleting(true);
        try {
            dispatch(removeItem(eventSessionBundleId));
            enqueueSnackbar(snackbarTranslation("messages.cart.remove.success"), { variant: "success" });
        }
        catch (error) {
            const errorMessage = API.GetErrorMessage(error);
            enqueueSnackbar(errorMessage, { variant: "error" });
        }
        setIsDeleting(false);
    }, [enqueueSnackbar, snackbarTranslation]);
    const decreaseItemByOne = useCallback(() => {
        const targetItem = cartItems.find(i => i.eventSessionBundleId === item.eventSessionBundleId);
        if (targetItem) {
            // Dont decrease any more, keep the last one quantity for this item.
            if (targetItem.quantity > 1) {
                setIsUpdating(true);
                try {
                    dispatch(decreaseItemQuantity({eventSessionBundleId: item.eventSessionBundleId, quantity: 1}));   
                }
                catch (error: unknown) {
                    const errorMessage = API.GetErrorMessage(error);
                    enqueueSnackbar(errorMessage, { variant: "error" });
                }
                setIsUpdating(false);
            } else if (targetItem.quantity == 1) {
                removeItemInCart(targetItem.eventSessionBundleId);
            }
        }
    }, [cartItems, item.eventSessionBundleId, enqueueSnackbar, removeItemInCart]);
    const increaseItemByOne = useCallback(() => {
        setIsUpdating(true);
        try {
            const targetItem = cartItems.find(i => i.eventSessionBundleId === item.eventSessionBundleId);
            if (targetItem) {
                dispatch(increaseItemQuantity({ eventSessionBundleId: item.eventSessionBundleId, quantity: 1}));   
            } else {
                dispatch(addItem({
                    ...item,
                    quantity: 1
                }));
            }
        }
        catch (error: unknown) {
            const errorMessage = API.GetErrorMessage(error);
            enqueueSnackbar(errorMessage, { variant: "error" });
        }
        setIsUpdating(false);
    }, [cartItems, enqueueSnackbar, item]);

    const isLoading = useMemo(() => isUpdating || isDeleting, [isUpdating, isDeleting]);
    const outOfStock = useMemo(() => item.soldOut, [item.soldOut]);

    return (
        <Card elevation={0} className={classNames(shoppingCartMenuStyles.itemCardContainer, outOfStock ? shoppingCartMenuStyles.outOfStock : null)}>
            <Box className={shoppingCartMenuStyles.itemNameContainer}>
                <CardContent sx={{ paddingTop: 0, paddingBottom: 0 }}>
                    {item.eventBundleName}{outOfStock ? eventTranslation("detail.soldOut") : null}<br />
                </CardContent>
            </Box>
            <Box>
                <CardContent sx={{ paddingTop: 0, paddingBottom: 0 }}>
                    { i18n.GetCurrency(item.currency, item.price * quantity, router.locale) } (@ { i18n.GetCurrency(item.currency, item.price, router.locale) })
                </CardContent>
            </Box>
            <Box className={shoppingCartMenuStyles.itemQuantityContainer}>
                <CardContent sx={{ paddingTop: 0, paddingBottom: 0, display: "flex", gap: 1}}>
                    <ButtonGroup size="small" aria-label="small outlined button group" sx={{ marginRight: "2rem" }}>
                        <Button disabled={isLoading || outOfStock} onClick={decreaseItemByOne}>-</Button>
                        <Button className={shoppingCartMenuStyles.quantity} disabled>{isUpdating ? <CircularProgress size="1rem" /> : quantity}</Button>
                        <Button disabled={isLoading || outOfStock} onClick={increaseItemByOne}>+</Button>
                    </ButtonGroup>
                    <IconButton disabled={isLoading || outOfStock} onClick={() => removeItemInCart(item.eventSessionBundleId)}>
                        { isDeleting ? <CircularProgress size="1rem" /> : <DeleteIcon fontSize="medium" /> }
                    </IconButton>
                </CardContent>
            </Box>
        </Card>
    );
};
ShoppingCartItemDisplay.displayName = "ShoppingCartItemDisplay";
export default ShoppingCartItemDisplay;