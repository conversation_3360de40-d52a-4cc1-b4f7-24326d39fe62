import styles from "@/components/User/ProfileSections/section.module.scss";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import ProfileSectionArray from "@/constants/navigation/profileSection";
import { useSelector } from "@/redux/store";
import { Box, Divider, Grid, List, ListItem, ListItemButton, ListItemIcon, ListItemText, useMediaQuery } from '@mui/material';
import classNames from "classnames";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { FunctionComponent } from "react";

const ProfileSections: FunctionComponent = () => {
  const router = useRouter();
  
  const profileSections = new ProfileSectionArray(router.pathname);
  const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
  const isMembership = useSelector(state => state.user.isMembership);
  const itemList = profileSections.getItems();

  return (
    <Box className={styles.box}>
      <List className={styles.list}>
        {itemList.map((each, i) => {
          if (each.visible) {
            const requiredMembership = each?.requiredMembership ?? each.requiredMembership;
            if (requiredMembership) {
              if (!isMembership) return null;
              return (
                <div key={`profileMenu-${i}`}>
                  <ListItem 
                    disablePadding
                    className={classNames(
                      styles.item,
                      each.active && styles.active,
                    )}
                  >
                    <ListItemButton component="a" href={each.path || ""} disabled={each.active}>
                      {each?.icon ? <ListItemIcon>{each.icon}</ListItemIcon> : null}
                      <ListItemText 
                        primary={profileTranslation(each.displayName)} 
                      />
                    </ListItemButton>
                  </ListItem>
                  {i < itemList.length-1 ? <Divider /> : null}
                </div>
              );
            } else {
              return (
                <div key={`profileMenu-${i}`}>
                  <ListItem 
                    disablePadding
                    className={classNames(
                      styles.item,
                      each.active && styles.active,
                    )}
                  >
                    <ListItemButton component="a" href={each.path || ""} disabled={each.active}>
                      {each?.icon ? <ListItemIcon>{each.icon}</ListItemIcon> : null}
                      <ListItemText 
                        primary={profileTranslation(each.displayName)} 
                      />
                    </ListItemButton>
                  </ListItem>
                  {i < itemList.length-1 ? <Divider /> : null}
                </div>
              );
            }
          }
        })}
      </List>
    </Box>
  );
};

interface ProfileSectionWrapperProps {
  children: React.ReactNode
}

const ProfileSectionWrapper: FunctionComponent<ProfileSectionWrapperProps> = (props: ProfileSectionWrapperProps) => {
  const screenBreakpointUpDetect = useMediaQuery('(min-width: 800px)');

  return (  
    <Grid container justifyContent="center" className={styles.container}>
      {screenBreakpointUpDetect ? <ProfileSections/> : null}

      <Grid item xs className={styles.content}>
        {props.children}
      </Grid>
    </Grid>
  );
};

export { ProfileSectionWrapper, ProfileSections };
