@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;

div.MuiFormControl-root {
  & * {
    color: map.get(theme.$color, "text");
  }

  & .Mui-disabled {
    -webkit-text-fill-color: map.get(theme.$color, "text");
  }

  & .Mui-error {
    color: map.get(theme.$color, "highlight-text");
    text-align: left;
    margin-top: 5px;
    width: 100%;
    font-weight: bold;
    font: {
      size: 0.9rem;
    }
  }

  & .MuiFormHelperText-root {
    width: 92%;
    font-size: 14px;
  }

  & label.MuiInputLabel-root {
    font-weight: bold;
    background-color: transparent;
    border-radius: 6px;
    padding: 0 4px;
    margin-left: -4px;
    font-size: 18px;
    color: map.get(theme.$color, "text");
  }

  & fieldset.MuiOutlinedInput-notchedOutline {
    border: {
      radius: map.get(theme.$border, "radius");
      color: map.get(theme.$color, "border") !important;
      width: map.get(theme.$border, "width") !important;
    }
  }

  & div.MuiSelect-select {
    white-space: pre-line;
  }

  & *::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    background-color: map.get(theme.$color, "background");
  }

  & *::-webkit-scrollbar {
    width: 8px;
    border-radius: 8px;
    background-color: #333333;
  }

  & *::-webkit-scrollbar-thumb {
    border-radius: 8px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #888888;
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 60px inherit inset !important;
  box-shadow: 0 0 0 60px inherit inset !important;
  background-color: inherit !important;
  background-clip: content-box !important;
}

input[type="number"] {
  -moz-appearance: textfield;
}
