@use "sass:map";

@use "@/styles/utils/theme.module.scss" as theme;
@use "@/styles/utils/viewport.module.scss" as viewport;

$mobile-header-height: map.get(theme.$height, "mobile-header");
$desktop-header-height: map.get(theme.$height, "desktop-header");
$footer-height: map.get(theme.$height, "footer");
$background-color: map.get(theme.$color, "background");
$text-color: map.get(theme.$color, "text");

.layouts {
    height: auto;
    min-height: 100%;
    display: flex;
    background-color: $background-color;

    flex: {
        direction: column;
        wrap: nowrap;
    }

    padding-top: constant(safe-area-inset-top);
    padding-right: constant(safe-area-inset-right);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-left: constant(safe-area-inset-left);
    padding-top: env(safe-area-inset-top);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
}

.headerPlaceholder {
    height: $desktop-header-height;

    @include viewport.within("mobile") {
        height: $mobile-header-height;
    }
}

.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    color: $text-color;
    background-color: $background-color;
    //scroll-margin-top: $desktop-header-height;
    height: $desktop-header-height;
    min-height: $desktop-header-height;
    max-height: $desktop-header-height;
    border-bottom: 1px solid rgba(215, 215, 215, 0.5);
    display: flex;
    flex-wrap: nowrap;
    flex-shrink: 0;
    align-items: center;
    // overflow: hidden;
    z-index: 100;
    box-shadow: 0 0 3px rgba(255, 255, 255, 0.4);

    @include viewport.within("mobile") {
        //scroll-margin-top: $mobile-header-height;
        height: $mobile-header-height;
        min-height: $mobile-header-height;
        max-height: $mobile-header-height;
    }
}

.mainBody {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    gap: 2rem;
    word-break: break-word;
    color: map.get(theme.$color, "text");
    background-color: #{map.get(theme.$color, "background")};
    margin: 0 auto;
    width: 100%;
    // background-color: grey;
    @include viewport.within("tablet") {
        margin: 0 auto;
    }
}

.footer {
    margin-top: 50px;
    position: relative;
    color: map.get(theme.$color, "text");
    background-color: map.get(theme.$color, "background");
    border-top: 1px solid hsla(0, 0%, 84%, 0.5);
    min-height: $footer-height;
    flex-shrink: 0;
    padding: 50px 30px;
    // margin-top: 100px;
    overflow: hidden;
    z-index: 90;
    cursor: context-menu;
    // background: {
    //   image: url("https://assets.easylive.show/layout/footer_crop.png");
    //   size: 700px;
    //   position: right bottom;
    //   repeat: no-repeat;
    // }

    & .container {
        flex-wrap: nowrap;
        max-width: 1040px;
        margin: 0 auto;

        &>div {
            margin-bottom: 50px;
        }
    }

    & .contactUs {
        margin: 0;
        font-size: 32px;
        position: relative;
        min-width: 50%;
        letter-spacing: 8px;
        user-select: none;
        background: linear-gradient(90deg, #F48121 40%, #429b3f 40%) left bottom / 100% 3px no-repeat;
    }

    & .contactInfo {
        margin: 20px 0;

        &>li {
            font-size: 14px;
        }

        &>li svg {
            margin: 5px 5px 5px 0;
            vertical-align: middle;
            font-size: 26px;
        }
    }

    & .iconButton {
        cursor: pointer;
        font-size: 28px;

        margin: {
            right: 10px;
        }
    }

    & .copyright {
        &.footerPoweredClaim {
            margin-top: 2rem;
        }

        &.footerPoweredClaimAdress {
            font-size: .8rem;
        }
    }
}

.verticalDivider {
    display: inline-block;
    border-left: 1px solid #ccc;
    margin: 0 12px;
    height: 12px;
}