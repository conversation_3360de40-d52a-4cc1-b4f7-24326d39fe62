import type { NextPage } from "next";
import { PageMeta } from "@/components/_PageComponents";
import EventInfoDisplay from "@/components/Event/InfoDisplay";
import { GetServerSideProps } from "next";
import ENDPOINT from "@/models/api/endpoint";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import EventDetailsAPIResult from "@/models/api/result/events/details";
import { useMemo } from "react";
import EnumEventPreviewPosition from "@/constants/enum/EventPreviewPosition";
import RouteMap from "@/constants/config/RouteMap";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import i18n from "@/utils/i18n";
import { GenericAPI } from "@stoneleigh/api-lib";
import Section from "@/components/_CommonElements/Section";
import classNames from "classnames";

import styles from "@/components/_PageComponents/Campaign/campaign.module.scss";
import CampaignInfoDisplay from "@/components/_PageComponents/Campaign/InfoDisplay";
import ProductListAPIResult from "@/models/api/result/products/list";

interface Props {
    fallback: {
        Products: ProductListAPIResult,
        EventDetails: EventDetailsAPIResult,
        CampaignId: string
    }
}
const CampaignPage: NextPage<Props> = (props) => {
    const { fallback } = props;
    const products = fallback.Products.data!;
    const eventDetails = fallback.EventDetails.data!;
    const campaignId = fallback.CampaignId;
    const metaImages = eventDetails.previewList.filter(preview => preview.position === EnumEventPreviewPosition.META);
    const metaImage = useMemo(() => {
        return metaImages.length === 0 ? undefined : metaImages[0];        
    }, [metaImages]);
    
    return (
        <>
            <PageMeta
                websiteUrl={RouteMap.ShareEvent(eventDetails.eventId)}
                title={eventDetails.eventName}
                keywords={eventDetails.eventName}
                description={eventDetails.eventDescription.substring(0, 160) + "…"}
                imagePath={metaImage?.previewContent || ""}
                imageType={`image/${metaImage?.previewType || ""}`}
                imageHeight={metaImage?.height?.toString?.() || ""}
                imageWidth={metaImage?.width?.toString?.() || ""}
            />
            <div className={classNames(styles.campaignBg, styles.haikyuu2404tst)}>
                <Section
                    containerSize="tight"
                    className={{
                        content: styles.sectionGap0
                    }}
                    content={<CampaignInfoDisplay products={products} eventDetails={eventDetails} campaignId={campaignId} />}
                />
            </div>
        </>
    );
};
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [EnumTranslationJson.Campaign, EnumTranslationJson.Purchase],
    context,
    getServerSideProps: async context => {
        const { params, locale, res } = context;
        const [Products] = await Promise.all([
            GenericAPI.requestAsync<ProductListAPIResult>(
                ENDPOINT.CampaignProducts(params!.id as string),
                {
                    method: "GET",
                    headers: {
                        [EnumRequestHeader.LANGUAGE]: locale || "en-US"
                    }
                }
            )
        ]);
        const [EventDetails] = await Promise.all([
            GenericAPI.requestAsync<EventDetailsAPIResult>(
                ENDPOINT.EventDetilsById(params!.id as string),
                {
                    method: "GET",
                    headers: {
                        [EnumRequestHeader.LANGUAGE]: locale || "en-US"
                    }
                }
            )
        ]);

        if (Products.data) {
            if (Products.data.length <= 0) {
                const redirectPath = `/`;
                res.writeHead(302, {
                    Location: redirectPath
                });
                res.end();
                return { props: { } };
            }
        }
        
        return {
            props: {
                fallback: {
                    Products,
                    EventDetails,
                    CampaignId: params!.id
                }
            }
        }
    }
});
export default CampaignPage;