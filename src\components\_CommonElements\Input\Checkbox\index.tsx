import styles from "@/components/_CommonElements/Input/input.module.scss";
import classNames from "classnames";

import Grid from '@mui/material/Grid';
import FormGroup from '@mui/material/FormGroup';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormHelperText from '@mui/material/FormHelperText';
import MUICheckbox from '@mui/material/Checkbox';

interface CheckboxProps {
  checked?: boolean;
  children?: React.ReactNode | string;
  className?: string;
  color?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  defaultChecked?: boolean;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  label?: string | React.ReactNode;
  labelPlacement?: "end" | "start" | "bottom" | "top";
  onChange?(params: any): void;
  onClick?(params: any): void;
  size?: "medium" | "small";
  value?: any;
  inlineStyle?: { [key: string]: any };
  fullWidth?: boolean
};

const Checkbox = (props: CheckboxProps) => {
  const {
    checked,
    children,
    className,
    color,
    defaultChecked,
    disabled,
    error,
    helperText,
    label,
    labelPlacement,
    onChange,
    onClick,
    size,
    value,
    inlineStyle,
    fullWidth,
  } = props;

  return (
    <FormControl
      fullWidth={fullWidth}
      className={classNames(
        className,
        styles.checkbox,
      )}
    >
      <FormControlLabel
        control={<>
          <MUICheckbox
            color={color || "primary"}
            defaultChecked={defaultChecked}
            size={size || "medium"}      
            onChange={onChange}
            onClick={onClick}
            checked={checked}
          />
        </>}
        
        disabled={disabled}
        label={<>{children || label || ""}</>}
        labelPlacement={labelPlacement || "end"}        
        value={value}
        sx={inlineStyle}
      />
      <FormHelperText error={error}>
        {helperText}
      </FormHelperText>
    </FormControl>
  );
};

export default Checkbox;