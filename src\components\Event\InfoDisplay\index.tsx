import { TextButton } from "@/components/_CommonElements/Button";
import Label from "@/components/_CommonElements/Label";
import Section from "@/components/_CommonElements/Section";
import EventBanner from "@/components/Event/InfoDisplay/Banner";
import RouteMap from "@/constants/config/RouteMap";
import EnumEventPreviewPosition from "@/constants/enum/EventPreviewPosition";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import EventBundles from "@/models/api/models/EventBundles";
import EventDetails from "@/models/api/models/EventDetails";
import Icon from "@mui/material/Icon";
import classNames from "classnames";
import dayjs from "dayjs";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useMemo } from "react";

import EventBuyContainer from "../Buy";
import EventMetadata from "../EventMetadata";
import RateCard from "./RateCard";

import styles from "@/components/Event/InfoDisplay/event.module.scss";

interface EventInfoDisplayProps {
    eventDetails: EventDetails;
    eventBundles: EventBundles;
}
// add session dropdown;
const EventInfoDisplay = (props: EventInfoDisplayProps) => {
    const { eventDetails, eventBundles } = props;
    const { t: eventTranslation } = useTranslation(EnumTranslationJson.Event);

    const router = useRouter();

    const isForSale = useMemo(() => eventBundles.eventBundleList.length > 0, [eventBundles.eventBundleList.length]);
    const saleStartDateTime_Unix = useMemo(() => dayjs.utc(eventDetails.saleStartDateTimeForDisplay), [eventDetails.saleStartDateTimeForDisplay]);
    const saleEndDateTime_Unix = useMemo(() => dayjs.utc(eventDetails.saleEndDateTimeForDisplay), [eventDetails.saleEndDateTimeForDisplay]);
    const saleStartDateTime_LocalTime = useMemo(() => saleStartDateTime_Unix.tz(eventDetails.eventTimeZone), [eventDetails.eventTimeZone, saleStartDateTime_Unix]);
    const saleEndDateTime_LocalTime = useMemo(() => saleEndDateTime_Unix.tz(eventDetails.eventTimeZone), [eventDetails.eventTimeZone, saleEndDateTime_Unix]);
    
    const eventStartDateTime_LocalTime = useMemo(() => dayjs.utc(eventDetails.eventStartDateTime).tz(eventDetails.eventTimeZone), [eventDetails.eventStartDateTime, eventDetails.eventTimeZone]);
    const eventEndDateTime_LocalTime = useMemo(() => dayjs.utc(eventDetails.eventEndDateTime).tz(eventDetails.eventTimeZone), [eventDetails.eventEndDateTime, eventDetails.eventTimeZone]);

    const banner = useMemo(() => {
        const banners = eventDetails.previewList.filter((preview) => preview.position === EnumEventPreviewPosition.BANNER);
        return banners.length > 0 ? banners : undefined;
    }, [eventDetails.previewList]);

    return (
        <>
            {banner && <EventBanner items={banner} />}
            <div className={classNames(styles.field, styles.border)}>
                <div className={styles.basicInfoTable}>
                    <EventMetadata
                        title={eventTranslation("detail.event")}
                        content={`${eventStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")} ~ ${eventEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")}`}
                    />
                    <EventMetadata
                        title={eventTranslation("detail.salesDate")}
                        content={`${saleStartDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")} ~ ${saleEndDateTime_LocalTime.format("YYYY-MM-DD HH:mm (Z)")}`}
                    />
                    { isForSale &&
                        <>
                            <Label title={eventTranslation("detail.rateCardLabel")} />
                            <RateCard dataSource={eventBundles.eventBundleList} />
                        </>
                    }
                </div>
            </div>
            <div className={styles.toBuy}>
                <TextButton
                        size="extra"
                        onClick={()=>{window.location.href="#buy";}}
                        variant="contained"
                    >
                    <Icon baseClassName="fa" className="fa-ticket-simple" />
                    &nbsp; {eventTranslation("detail.buy")}
                </TextButton>
            </div>

            <Section
                labelSize="large"
                content={eventDetails.eventDescription}
            />
            <Section
                labelSize="large"
                label={eventTranslation("detail.buy")}
                id={'buy'}
                content={
                    <EventBuyContainer eventDetails={eventDetails} />
                }
            />
            <button
                className={styles.back}
                onClick={() => {
                    void router.push(RouteMap.Main);
                }}
            >
                <Icon baseClassName="fa" className="fa-chevron-left" />
                {" "}
                {eventTranslation("detail.back")}
            </button>
        </>
    );
};
export default EventInfoDisplay;
