enum EnumComponentsTranslationJsonKey {
  Navigation = "components/navigation",
  Footer = "components/footer",
  SEO = "components/seo",
  Validation = "components/validation",
  Modal = "components/modal",
  SnackBar = "components/snackbar",
  Share = "components/share",
  Terms = "components/terms",
  Redeem = "components/redeem",
  Hooks = "components/hooks",
  ShoppingCart = "components/shoppingCart",
  Purchase = "components/purchase",
  Reservation = "components/reservation",
  MobileBottomAppBar = "components/mobileBottomAppBar",
}

enum EnumPageTranslationJsonKey {
    Account = "pages/account",
    Profile = "pages/profile",
    AboutUs = "pages/aboutUs",
    Event = "pages/event",
    FAQ = "pages/faq",
    Guide = "pages/guide",
    Error = "pages/error",
    Receipt = "pages/receipt",
    Kiosk = "pages/kiosk",
    Campaign = "pages/campaign",
    Checkout = "pages/checkout",
}

const EnumTranslationJson = {
    Common: "common",
    ...EnumComponentsTranslationJsonKey,
    ...EnumPageTranslationJsonKey,
};
const DefaultTranslationJson = [
    EnumTranslationJson.Common,
    EnumTranslationJson.Navigation,
    EnumTranslationJson.Footer,
    EnumTranslationJson.SEO,
    EnumTranslationJson.Terms,
    EnumTranslationJson.Account,
    EnumTranslationJson.Validation,
    EnumTranslationJson.Modal,
    EnumTranslationJson.Profile,
    EnumTranslationJson.SnackBar,
    EnumTranslationJson.Share,
    EnumTranslationJson.Hooks,
    EnumTranslationJson.ShoppingCart,
    EnumTranslationJson.Purchase,
    EnumTranslationJson.MobileBottomAppBar,
]
export { EnumTranslationJson, DefaultTranslationJson };