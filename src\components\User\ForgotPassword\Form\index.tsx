// import styles from "./signUpForm.module.scss";
import { forwardRef, useEffect, useState } from "react";
import Form from "@/components/_CommonElements/Form";
import {
  FieldContainer,
  TextInput
} from "@/components/_CommonElements/Input";
import { TextButton } from "@/components/_CommonElements/Button";
import {
  useRequestForgotPassword,
} from "@/hooks/Authentication";
import InputValidation from "@/utils/regex/InputValidation";
import { useTranslation } from "next-i18next";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useFormState } from "@/hooks/Utils/FormState";
import UtilityHooks from "@/hooks/Utility";
import LoadingButton from "@/components/_CommonElements/Button/LoadingButton";
import API from "@/utils/API";
import { useSnackbar } from "notistack";

export type ForgotPasswordFormRef = {
    onSubmit: () => void;
}
interface Props {
    onClose: () => void;
}
const ForgotPasswordForm = forwardRef<ForgotPasswordFormRef, Props>((props, ref) => {
  const requestForgotPasswordCodeAsync = useRequestForgotPassword();
  const { t: modalTranslation } = useTranslation(EnumTranslationJson.Modal);
  const { t: accountTranslation } = useTranslation(EnumTranslationJson.Account);
  const { t: validatorTranslation } = useTranslation(EnumTranslationJson.Validation);
  const forgotPasswordInitialState = {
    email: {
      value: "",
      isRequired: true,
      validator: InputValidation.validateEmail,
      errorMessage: {
        empty: validatorTranslation(`email.errorMessage.empty`),
        format: validatorTranslation("email.errorMessage.format"),
      },
    },
    verificationCode: {
      value: "",
      isRequired: true,
      errorMessage: {
        empty: validatorTranslation(`verificationCode.errorMessage.empty`),
        format: validatorTranslation(`verificationCode.errorMessage.format`),
      },
    },
    newPassword: {
      value: "",
      isRequired: true,
      validator: InputValidation.validatePassword,
      errorMessage: {
        empty: validatorTranslation(`password.errorMessage.empty`),
        format: validatorTranslation(`password.errorMessage.format`),
      },
    },
    confirmNewPassword: {
      value: "",
      isRequired: true,
      validator: (): boolean => {
        return newPassword.value === confirmNewPassword.value;
      },
      errorMessage: {
        empty: validatorTranslation(`confirmPassword.errorMessage.empty`),
        format: validatorTranslation(`confirmPassword.errorMessage.format`),
      },
    },
  };
  const forgotPasswordFields = useFormState(forgotPasswordInitialState);
  const { email, verificationCode, newPassword, confirmNewPassword } =
    forgotPasswordFields.data;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { counter, setCounter } = UtilityHooks.useCountDown(-1);
  const [enable, setEnable] = useState<boolean>(false);
  const { enqueueSnackbar } = useSnackbar();
  
  const getNewCode = async () => {
    forgotPasswordFields.validateFields(["email"]);
    if (!email.isValid) {
      return;
    }
    setIsLoading(true);

    try {
      const response = await requestForgotPasswordCodeAsync(email.value);
      setEnable(true);
      setCounter(60);
    } catch (error) {
        const errorMessage = API.GetErrorMessage(error);
        enqueueSnackbar(errorMessage, { variant: "error" });
    }
    setIsLoading(false);
  };

  return (
    <>
      <Form.Container size="small">
        <Form.Body rowSpacing={2} columnSpacing={2}>
          <FieldContainer>
            <TextInput
              size="medium"
              name="email"
              label={accountTranslation(`email`)}
              inputType="email"
              error={Boolean(
                email.errorType &&
                email.errorMessage?.[email.errorType]
              )}
              helperText={
                email.errorType &&
                email.errorMessage?.[email.errorType]
              }
              onChange={(e) => {
                forgotPasswordFields.set({
                  field: "email",
                  value: e.target.value,
                });
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter") getNewCode();
              }}
            />
          </FieldContainer>

          <FieldContainer>
            <TextButton
              label={
                counter > 0
                  ? modalTranslation(`resendAfter`, {
                    SEC: String(counter)
                  }) : modalTranslation(`obtainCode`)
              }
              disabled={counter > 0 || enable}
              onClick={getNewCode}
            />
          </FieldContainer>
          <FieldContainer>
            { isLoading ? <LoadingButton /> :
              <p style={{margin:"auto"}}>{enable && accountTranslation(`forgotpasswordText.emailSent`)}</p>
            }
          </FieldContainer>
        </Form.Body>
      </Form.Container>
    </>
  );
});
ForgotPasswordForm.displayName = "ForgotPasswordForm";
export default ForgotPasswordForm;
