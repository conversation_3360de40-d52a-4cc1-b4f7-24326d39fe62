import classNames from "classnames";
import React from "react";
import styles from "./label.module.scss";

interface Props {
    title: string;
    className?: string;
}
const Label = React.memo((props: Props) => {
    const { title, className } = props;
    return (
        <div className={classNames(styles.labelContainer, className)}>
            <div className={styles.label}>
                {title}
            </div>
        </div>
    );
});
Label.displayName = "Label";
export default Label;