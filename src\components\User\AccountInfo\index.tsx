import AddPhotoAlternateIcon from '@mui/icons-material/AddPhotoAlternate';
import { Box, Container, Stack } from "@mui/material";
import Grid from "@mui/material/Unstable_Grid2";
import type {
  TCountryCode
} from "countries-list";
import { countries, getCountryData } from "countries-list";
import { MuiFileInput } from "mui-file-input";
import { useTranslation } from "next-i18next";
import { useCallback, useEffect, useMemo, useState } from "react";

import { TextButton } from "@/components/_CommonElements/Button";
import Form from "@/components/_CommonElements/Form";
import { FieldContainer, Select, TextInput } from "@/components/_CommonElements/Input";
import Switch from "@/components/_CommonElements/Input/Switch";
import { BACKEND } from "@/constants/config";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import ENDPOINT from "@/models/api/endpoint";
import UserAccount from '@/models/api/models/UserAccount';

import styles from "@/components/User/AccountInfo/account.module.scss";
import LoadingButton from "@/components/_CommonElements/Button/LoadingButton";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { RequestMethod } from "@/constants/enum/RequestMethod";
import UserInfoAPIResult from "@/models/api/result/user/info";
import UploadUserPortraitAPIResult from "@/models/api/result/user/uploadUserPortrait";
import { useSnackbar } from "notistack";
import dynamic from 'next/dynamic';
import dayjs, { Dayjs } from 'dayjs';

const DatePicker = dynamic(async () => (await import("@mui/x-date-pickers")).DatePicker, { ssr: false });

const file2Base64 = (file:File):Promise<string> => {
  return new Promise<string> ((resolve, reject)=> {
    const reader = new FileReader();

    if (file.size > 5e6) reject("File is too big, please upload smaller than 5MB.");

    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result?.toString() || '');
    reader.onerror = error => reject(error);
  });
};

type UpdateUserInfoParam = {
  firstName: string,
  lastName: string,
  mobilePhoneNumber: string,
  dateOfBirth: string,
  country: string,
};

const useUpdateUserInfo = () => {
  const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
  const requestAsync = async (param: UpdateUserInfoParam) => {
      const formData = new FormData();
      Object.keys(param).map((keyname) => {
        const value = param[keyname as keyof UpdateUserInfoParam];
        if (value != "") formData.append(keyname, value);
      });

      return await fetchAsync<UserInfoAPIResult>(
          ENDPOINT.GetUserInfo(),
          {
              method: RequestMethod.PUT,
              data: formData
          }
      );
  };
  return requestAsync;
};

const useUploadUserPortrait = () => {
  const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
  const requestAsync = async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);

      return await fetchAsync<UploadUserPortraitAPIResult>(
          ENDPOINT.UploadUserPortrait(),
          {
              method: RequestMethod.POST,
              data: formData
          }
      );
  };
  return requestAsync;
};

interface AccountInfoProps {
  user: UserAccount;
  onClickChangePassword?: () => void;
  onClickValidatePhone?: () => void;
}

const AccountInfo = (props: AccountInfoProps) => {
  const {
    user,
    onClickChangePassword,
    onClickValidatePhone
  } = props;
  
  const { enqueueSnackbar } = useSnackbar();
  const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
  const { t: snackbarTranslation } = useTranslation(EnumTranslationJson.SnackBar);
  const [ isAPILoading, setIsAPILoading ] = useState<boolean>(false);
  const [ selectedCountry, setSelectedCountry ] = useState(user.country ?? "disable");
  const [ promotionOptIn, setPromotionOptIn ] = useState(user.optIn ?? false);
  const [ userUploadPortrait, setUserUploadPortrait] = useState<File | null>(null);
  const [ selectedDateOfBirth, setSelectedDateOfBirth ] = useState<Dayjs | null>(null);
  const [ inputValues, setInputValues ] = useState({
    firstName: user.firstName ?? "",
    lastName: user.lastName ?? "",
    mobilePhoneNumber: user.mobilePhoneNumber ?? "",
  });
  const [ showUserPortrait, setShowUserPortrait ] = useState(() => {
    return user.portraitURL ?? "https://i.pravatar.cc/150?img=63";
  });

  const currentDateTime = dayjs();
  const defaultCalendarMonth = useMemo(() => currentDateTime, [currentDateTime]);

  const hideMembershipUpdate = true;

  useEffect(() => {
    (async () => {
      if (userUploadPortrait) {
        try {
          setShowUserPortrait(await file2Base64(userUploadPortrait));
          enqueueSnackbar(snackbarTranslation("messages.profile.update.success"), { variant: "success" });
        } catch(error) {
          setUserUploadPortrait(null);
          enqueueSnackbar(snackbarTranslation("messages.profile.portrait.upload.fileTooLarge"), { variant: "error" });
        }
      }
    })();

    if (user.dateOfBirth) {
      setSelectedDateOfBirth(dayjs(user.dateOfBirth));
    }
  }, [userUploadPortrait, user?.dateOfBirth]);

  const updateUserInfoAsync = useUpdateUserInfo();
  const uploadUserPortrait = useUploadUserPortrait();

  const onTextInputChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues((prev) => ({ ...prev, [name]: value }));
  }, []);

  const onSelectedCountryChanged = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSelectedCountry(value);
  }, [setSelectedCountry]);

  const onOptInChanged = useCallback(() => setPromotionOptIn(!promotionOptIn), [promotionOptIn]);

  const onClickUpdateUserInfo = useCallback(async () => {
    setIsAPILoading(true);
    if (userUploadPortrait) void await uploadUserPortrait(userUploadPortrait);
    const updateRes = await updateUserInfoAsync({
      ...inputValues,
      dateOfBirth: selectedDateOfBirth ? selectedDateOfBirth.format('YYYY-MM-DD hh:mm:ss') : '',
      country: selectedCountry ?? ''
    });
    if (updateRes.result) {
      enqueueSnackbar(snackbarTranslation("messages.profile.update.success"), { variant: "success" });
    } else {
      enqueueSnackbar(snackbarTranslation("messages.profile.update.error"), { variant: "error" });
    }
    setIsAPILoading(false);
  }, [inputValues]);

  const handleUserPortraitChange = (newValue: File | null) => {
    setUserUploadPortrait(newValue);
  }

  const onSelectedDateChanged = useCallback((date: unknown) => {
    setSelectedDateOfBirth(date as Dayjs | null);
}, []);

  return (
    <>
      <Container className={styles.accountInfoContainer}>
        <Grid container spacing={{ xs: 2, lg: 3 }} columns={{ xs: 6, lg: 12 }} className={styles.accountInfoContent}>
          <Grid lg={4}>
            {hideMembershipUpdate ?
              null
              :
              <Box className={styles.portrait}>
                <img src={showUserPortrait} alt="Live from space album cover" />
                {!user.portraitURL ? 
                  <>
                    <br />
                    <MuiFileInput
                      className={styles.uploadImageButton}
                      placeholder="Insert a file"
                      InputProps={{
                        inputProps: {
                          accept: '.jpg, .jpeg, .png',
                        },
                        startAdornment: <AddPhotoAlternateIcon />
                      }}
                      value={userUploadPortrait}
                      onChange={handleUserPortraitChange}
                      getInputText={(value) => value ? 'Change other image' : ''}
                      hideSizeText
                    />
                  </>
                  : null
                }
              </Box>
            }
          </Grid>
          <Grid lg={8}>
            <Box>
              <Form.Container size="small">
                <Form.Body rowSpacing={2} columnSpacing={2} className={styles.formBody}>
                  <FieldContainer>
                    <TextInput
                      size="medium"
                      name="name"
                      label={profileTranslation("profile.name")}
                      disabled
                      value={user.nickname}
                    />
                  </FieldContainer>

                  <FieldContainer>
                    <TextInput
                      size="medium"
                      name="email"
                      label={profileTranslation("profile.email")}
                      inputType="email"
                      disabled
                      value={user.email}
                    />
                  </FieldContainer>

                  <FieldContainer>
                    <Stack direction={"row"} gap={1}>
                      <FieldContainer>
                        <TextInput
                          size="medium"
                          name="firstName"
                          label={profileTranslation("profile.firstName")}
                          value={inputValues.firstName}
                          onChange={onTextInputChanged}
                        />
                      </FieldContainer>

                      <FieldContainer>
                        <TextInput
                          size="medium"
                          name="lastName"
                          label={profileTranslation("profile.lastName")}
                          value={inputValues.lastName}
                          onChange={onTextInputChanged}
                        />
                      </FieldContainer>
                    </Stack>
                  </FieldContainer>

                  <FieldContainer>
                    <DatePicker
                      label={profileTranslation("profile.dateOfBirth")}
                      className={styles.calendar}
                      disableFuture
                      openTo='year'
                      defaultCalendarMonth={defaultCalendarMonth}
                      value={selectedDateOfBirth}
                      onChange={onSelectedDateChanged}
                      slotProps={{
                        textField: {
                          helperText: 'MM/DD/YYYY',
                          fullWidth: true
                        },
                      }}
                    />
                  </FieldContainer>

                  <FieldContainer>
                    <TextInput
                      size="medium"
                      name="mobilePhoneNumber"
                      label={profileTranslation("profile.mobile")}
                      value={inputValues.mobilePhoneNumber}
                      onChange={onTextInputChanged}
                    />
                  </FieldContainer>

                  <FieldContainer>
                    <Select.Box
                      fullWidth
                      size="medium"
                      label={profileTranslation("profile.country")}
                      value={selectedCountry}
                      className={styles.selectBox}
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      // @ts-ignore
                      onChange={onSelectedCountryChanged}
                    >
                      <Select.Option disabled value="disable">
                          {profileTranslation("profile.country")}
                      </Select.Option>
                      {Object.keys(countries).map(countryCode => {
                        const country = getCountryData(countryCode as TCountryCode);
                        return (<Select.Option key={countryCode} className={styles.selectOption} divider value={country.name}>{country.native}</Select.Option>);
                      })}
                    </Select.Box>
                  </FieldContainer>

                  <FieldContainer>
                    <Switch 
                      checked={promotionOptIn}
                      onChange={onOptInChanged}
                    />
                    {profileTranslation("profile.promotionOptIn")}
                  </FieldContainer>
                </Form.Body>
              </Form.Container>
            </Box>
          </Grid>
        </Grid>
        <Grid container justifyContent="right" xs={12}>
          {!user.isVerified &&
          <TextButton 
              label={profileTranslation("profile.verifyEmail")}
              onClick={onClickValidatePhone}                       
          />}
          {hideMembershipUpdate ? 
            null
            :
            <>
              {/* <LoadingButton
                variant="outlined"
                loading={isAPILoading}
                onClick={onClickUpdateUserInfo}
              >
                {profileTranslation("profile.update")}
              </LoadingButton> */}
            </>
          }
          <TextButton 
            label={profileTranslation("profile.changePassword")}
            onClick={onClickChangePassword}                    
          />
        </Grid>
      </Container>
    </>
  );
};

export default AccountInfo;