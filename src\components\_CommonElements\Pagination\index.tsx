import styles from "@/components/_CommonElements/Pagination/pagination.module.scss";
import classNames from 'classnames';
import Stack from '@mui/material/Stack';
import Icon from "@mui/material/Icon";
import { useMemo } from "react";

interface PaginationProps {
    maxPage: number;
    currentPage: number;
    onClick: (pageNumber: number) => void;
    size?: "small" | "medium";
    siblingCount?: number,
    boundaryCount?: number,
}

const Pagination = (props: PaginationProps) => {
    const {
        maxPage,
        currentPage,
        onClick,
        size = "medium",
    } = props;

    const pageNumbers = useMemo(() => new Array<string>(maxPage).fill("").map((_, index) => index + 1), [maxPage]);
    return (<>
        <Stack
            direction="row"
            spacing={1}
            className={classNames(styles.container, styles[size])}
        >
            {pageNumbers.map((pageNum) => {
                    if (pageNum === currentPage) {
                        return <button key={pageNum} className={classNames(styles.pageButton, styles.active)}>{pageNum}</button>;
                    }
                    return <button key={pageNum} onClick={() => onClick(pageNum)} className={classNames(styles.pageButton)}>{pageNum}</button>;
                }
            )}
        </Stack>
    </>);
};

export default Pagination;


// interface PaginationProps {
//   boundaryCount?: number;
//   className?: string;
//   currentPage: number;
//   color?: "primary" | "secondary" | "standard";
//   disabled?: boolean;
//   hideNextButton?: boolean;
//   hidePrevButton?: boolean;
//   onChange?(params: any): void,
//   showFirstButton?: boolean;
//   showLastButton?: boolean;
//   siblingCount?: number;
//   size?: "small" | "medium" | "large";
//   variant?: "text" | "outlined";
//   maxPage: number;
// };

// const Pagination = (props: PaginationProps) => {
//   const {
//     boundaryCount,
//     className,
//     currentPage,
//     color,
//     disabled,
//     hideNextButton = true,
//     hidePrevButton = true,
//     onChange,
//     showFirstButton,
//     showLastButton,
//     siblingCount,
//     size,
//     variant,
//     maxPage,
//   } = props;

//   return (<div className={styles.container}>
//     <MUIPagination 
//       boundaryCount={boundaryCount}
//       className={classNames(className, styles.pagination)}
//       count={maxPage}
//       page={currentPage}
//       color={color || "primary"}
//       disabled={disabled}
//       hideNextButton={hideNextButton}
//       hidePrevButton={hidePrevButton}
//       onChange={onChange}
//       showFirstButton={showFirstButton}
//       showLastButton={showLastButton}
//       siblingCount={siblingCount}
//       size={size || "medium"}
//       variant={variant || "outlined"}
//     />
//   </div>);
// };