import type { GetServerSideProps, NextPage } from 'next';

import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import { useTranslation } from 'next-i18next';
import i18n from '@/utils/i18n';
import Section from '@/components/_CommonElements/Section';

const Kiosk: NextPage = () => {
    const { t: kioskTranslation } = useTranslation(EnumTranslationJson.Kiosk);
    const title = kioskTranslation("title");
    const description = kioskTranslation("description");
    return (<>
        <Section
            labelSize="xlarge"
            label={title}
            content={description}
        />
    </>);
}
export const getServerSideProps: GetServerSideProps = context => i18n.GetServerSidePropsAsync({
    additionalFiles: [EnumTranslationJson.Kiosk],
    context
});

export default Kiosk;