import LoadingCardMeida from "@/components/_CommonElements/Loading/CardMedia";
import { EnumLocale } from "@/constants/enum/Locale";
import { useEvents } from "@/hooks/Events/Events";
import Event from "@/models/api/models/Event";
import EventsAPIResult from "@/models/api/result/events/events";
import { useRouter } from "next/router";
import { useEffect, useState, useTransition } from "react";

import EventDisplay from "../Event";
import styles from "./eventFeed.module.scss";

interface EventFeedProps {
    eventCategory: string
    isTrending?: boolean
}

const EventFeed = (props: EventFeedProps) => {
    const { eventCategory, isTrending = false } = props;
    const router = useRouter();
    const { getEventsAsync } = useEvents();
    const [isPending, startTransition] = useTransition();

    const { locale } = router;
    const [eventData, setEventData] = useState<Event[]>([]);

    const loadingSamples = [...Array(3)];

    useEffect(() => {
        const getEvents = async () => {
            const { data: res } : EventsAPIResult = await getEventsAsync(eventCategory, isTrending, locale ?? EnumLocale.English);
            startTransition(() => {
                if (res) {
                    if (res.list) {
                        setEventData(res.list);
                    }
                }
            });
        };
        void getEvents();
    }, [locale]);

    return (
        <article className={styles.eventDisplayContainer}>
            {isPending ?
                loadingSamples.map((_, i) => <LoadingCardMeida key={`event_feed__loading_${i}`} />)
                :
                eventData.map((event, i) => <EventDisplay key={i} event={event} />)
            }
        </article>
    );
};

export default EventFeed;