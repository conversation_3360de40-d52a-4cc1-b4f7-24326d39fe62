import { Fragment, useMemo } from "react";
import { useTranslation } from "next-i18next";
import Markdown from "markdown-to-jsx";
import { Paper } from "@mui/material";

import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import EnumEventPreviewPosition from "@/constants/enum/EventPreviewPosition";
import ProductDetails from "@/models/api/models/ProductDetails";
import EventDetails from "@/models/api/models/EventDetails";
import Section from "@/components/_CommonElements/Section";
import CampaignProductList from "@/components/_PageComponents/Campaign/ProductList";

import styles from "@/components/_PageComponents/Campaign/campaign.module.scss";
import classNames from "classnames";

interface CampaignInfoDisplayProps {
    products: ProductDetails[];
    eventDetails: EventDetails;
    campaignId: string;
}

const CampaignInfoDisplay = (props: CampaignInfoDisplayProps) => {
    const { products, eventDetails, campaignId } = props;

    const { t: campaignTranslation } = useTranslation(EnumTranslationJson.Campaign);

    const bannerImages = eventDetails.previewList.filter(preview => preview.position === EnumEventPreviewPosition.BANNER);
    const descImgSet = useMemo<string[]>(() => campaignTranslation(`campaigns.${campaignId}.descImgSet`, { returnObjects: true }), [campaignId, campaignTranslation]);

    return (
        <>
            <Section
                labelSize="large"
                content={
                    <img 
                        alt="campaign banner"
                        src={bannerImages.length > 0 && bannerImages[0].previewContent || "https://sdfsdf.dev/1140x500.jpg"} 
                        className={styles.banner}
                    />
                }
            />
            <Section
                labelSize="large"
                className={{
                    content: classNames(styles.sectionBg, styles.sectionDesc)
                }}
                content={eventDetails.eventDescription}
            />
            <Section
                labelSize="large"
                className={{
                    content: styles.sectionBg
                }}
                content={
                    <>
                        {/* {descImgSet.map((imgSrc, index) =>
                            imgSrc ? 
                            <div key={`descImg-${index}`} className={styles.descriptionSection}>
                                <div className={styles.descriptionContent}>
                                    <img
                                        alt="description image"
                                        src={imgSrc}
                                    />
                                </div>
                            </div> : null
                        )}
                        <Section
                            containerSize="wide"
                            className={{ root: styles.purchaseSection, label: styles.label }}
                            label={campaignTranslation(`campaigns.${campaignId}.reminder`)}
                            labelSize="xlarge"
                            content={
                                <Paper elevation={0} sx={{ padding: "0 1rem" }}>
                                    <Markdown
                                        options={{
                                            wrapper: Fragment,
                                            forceWrapper: false
                                        }}
                                    >
                                        {campaignTranslation(`campaigns.${campaignId}.description`)}
                                    </Markdown>
                                </Paper>
                            } /> */}
                        {products ? <CampaignProductList products={products} /> : null}
                    </>
                }
            />
        </>
    );
};
export default CampaignInfoDisplay;
