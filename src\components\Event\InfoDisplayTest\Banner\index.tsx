import { useCallback, useEffect, useMemo, useState } from "react";
import ImageGallery, { ReactImageGalleryItem, ReactImageGalleryProps } from "react-image-gallery";
import { useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";

import EventPreview from "@/models/api/models/EventPreview";
import { getBannerVideoThumbnailUrl, parseVideoSource } from "@/utils/VideoPlayerUtils";

import styles from "@/components/Event/InfoDisplay/event.module.scss";
import { EventPreviewType } from "@/constants/enum/EventPreviewType";
import dynamic from "next/dynamic";

const VideoPlayer = dynamic(() => import("@/components/_CommonElements/VideoPlayer"), { ssr: false });

interface EventBannerProps {
    items: EventPreview[];
}

interface GalleryItemOption extends Omit<ReactImageGalleryProps, 'items'>  {
    original: string;
    thumbnail: string;
    originalClass: string;
    renderItem?: ((item: ReactImageGalleryItem) => React.ReactNode) | undefined;
}

const EventBanner = (props: EventBannerProps) => {
    const {
        items,
    } = props;

    const theme = useTheme();
    const greaterThanMid = useMediaQuery(theme.breakpoints.up("md"));
    const [thumbnailPosition, setThumbnailPosition] = useState<ReactImageGalleryProps["thumbnailPosition"]>(greaterThanMid ? "right" : "bottom");
    const [galleryShowNav, setGalleryShowNav] = useState<boolean>(false);

    useEffect(() => {
        //setThumbnailPosition(greaterThanMid ? "right" : "bottom");
        setThumbnailPosition("bottom");
    }, [greaterThanMid]);

    const onToggledFullscreen = useCallback(() => {
        if (thumbnailPosition === "right") {
            setThumbnailPosition("bottom");
        } else {
            setThumbnailPosition("right");
        }

        setGalleryShowNav(!galleryShowNav);
    }, [galleryShowNav, thumbnailPosition]);

    const modifiedForGallery = useMemo(() => {
        return items.reduce((galleryItems: GalleryItemOption[], bannerItem) => {
            const itemOption: GalleryItemOption = {
                original: bannerItem.previewContent || "https://sdfsdf.dev/1140x500.jpg",
                thumbnail: bannerItem.previewContent || "https://sdfsdf.dev/1140x500.jpg",
                originalClass: styles.banner,
            };
            if (bannerItem.previewType === EventPreviewType.VIDEO) {
                const videoSourcePlatform = parseVideoSource(bannerItem.previewContent);
                itemOption.thumbnail = getBannerVideoThumbnailUrl(bannerItem.previewContent);
                itemOption.renderItem = (item: ReactImageGalleryItem) => <VideoPlayer src={item.original} source={videoSourcePlatform} />;
            }
            galleryItems.push(itemOption);
            return galleryItems;
        }, []);
    }, [items]);

    return modifiedForGallery.length > 1 ?
        <ImageGallery 
            items={modifiedForGallery} 
            thumbnailPosition={thumbnailPosition} 
            useBrowserFullscreen={false} 
            showPlayButton={false}
            showBullets={false}
            showNav={galleryShowNav}
            onScreenChange={onToggledFullscreen}
            additionalClass={'bannerGallery'}
        />
        :
        items.length == 1 ?
        <>
            <div className={styles.bannerBackground}>
                {items[0].previewType === EventPreviewType.JPEG ?
                    <img 
                        alt="event banner"
                        src={items[0].previewContent || "https://sdfsdf.dev/1140x500.jpg"} 
                        className={styles.banner}
                    />
                    :
                    items[0].previewType === EventPreviewType.VIDEO ?
                        <VideoPlayer src={items[0].previewContent} source={parseVideoSource(items[0].previewContent)} />
                    : null
                }
            </div>
        </>
        : null;
};

export default EventBanner;