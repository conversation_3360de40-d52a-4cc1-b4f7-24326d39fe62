import styles from "@/components/_CommonElements/Display/display.module.scss";
import classNames from "classnames";
import { ReactNode } from "react";

interface ChipLabelProps {
    children?: ReactNode;
    className?: string;
}

const ChipLabel = (props: ChipLabelProps) => {
    const {
        children,
        className,
    } = props;

    return (<div className={styles.chipLabelContainer}>
        <div className={classNames(className, styles.chipLabel)}>
            {children}
        </div>
    </div>);
};

export default ChipLabel;