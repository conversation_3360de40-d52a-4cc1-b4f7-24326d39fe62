import type { GetServerSideProps, NextPage } from "next";
import { ProfileSectionWrapper } from "@/components/User/ProfileSections";
import LegendFieldSet from "@/components/_CommonElements/Display/LegendFieldSet";
import serverSideAuth from "@/utils/serverSideAuth";
import i18n from "@/utils/i18n";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import TicketRedemptionInput from "@/components/Event/Redeem";
import styles from "./redeem.module.scss";
import PageContent from "@/components/_PageComponents/PageContent";
import { useMemo } from "react";

const ProfilePage: NextPage = () => {
  const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
  const { t: redeemTranslation } = useTranslation(EnumTranslationJson.Redeem);
  const { query } = useRouter();
  const { code } = query;

  const title = useMemo(() => seoTranslation("page.redeemTicket.title"), [seoTranslation]);
  return (
    <PageContent
        title={title}
        content={
            <ProfileSectionWrapper>
            <LegendFieldSet legend={redeemTranslation("form.title")}>
                <div className={styles.form}>
                    <TicketRedemptionInput defaultValue={code as string} />
                    <a target="_blank" href="/guide#getTicketByEnterCode" >
                        <div className={styles.guide}>
                            {redeemTranslation("form.redemptionGuide")}
                        </div>
                    </a>
                </div>
            </LegendFieldSet>
          </ProfileSectionWrapper>
        }
    />
  );
};

export const getServerSideProps: GetServerSideProps = serverSideAuth(
  {
      permission: "userOnly",
  },
  async (context) =>
      i18n.GetServerSidePropsAsync({
          additionalFiles: [EnumTranslationJson.Account, EnumTranslationJson.Profile, EnumTranslationJson.Redeem],
          context,
      })
);


export default ProfilePage;
