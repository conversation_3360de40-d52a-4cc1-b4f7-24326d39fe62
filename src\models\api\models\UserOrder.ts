interface UserOrderItem {
    eventId: string;
    eventName: string;
    eventBundleId: string;
    quantity: number;
    pricePerUnit: number;
    totalFee: number;
    eventBundleName: string;
    discountFee?: number;
    discountedUnitTotalFee?: number;
}

interface UserOrder {
    paymentInfo: {
        paymentOrderId: string;
        paymentGatewayId: string;
        paymentGatewayOrderId: string;
        paymentType: string;
        internalPaymentOrderState: string;
        userId: string;
        currency: string;
        orderTotalFee: number;
        orderOriginalFee: number;
        isDiscounted: boolean;
        discountCode?: string;
        discountValue?: string;
        discountValueType?: string;
        createdDateTime: number;
        lastModifiedDateTime: number;
    };
    itemList: UserOrderItem[];
}
export type { UserOrder, UserOrderItem };